(()=>{var t={};t.id=462,t.ids=[462],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11216:(t,e,i)=>{Promise.resolve().then(i.bind(i,80621))},11462:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>d,tree:()=>h});var n=i(65239),s=i(48088),r=i(88170),a=i.n(r),o=i(30893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["[lang]",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,80621)),"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,93846)),"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,2635)),"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\layout.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,87569))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,87569))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[lang]/(home)/page",pathname:"/[lang]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20944:(t,e,i)=>{Promise.resolve().then(i.bind(i,42461))},25334:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:t=>{"use strict";t.exports=require("path")},34839:(t,e,i)=>{Promise.resolve().then(i.bind(i,13453)),Promise.resolve().then(i.bind(i,76773)),Promise.resolve().then(i.bind(i,46250)),Promise.resolve().then(i.bind(i,52923)),Promise.resolve().then(i.bind(i,61481)),Promise.resolve().then(i.bind(i,61883)),Promise.resolve().then(i.bind(i,353)),Promise.resolve().then(i.bind(i,65809)),Promise.resolve().then(i.bind(i,31116))},42461:(t,e,i)=>{"use strict";let n;i.r(e),i.d(e,{default:()=>r5});var s,r,a=i(60687),o=i(10218),l=i(43210),h=i.t(l,2);function u(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function c(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function d(t,e,i,n){if("function"==typeof e){let[s,r]=c(n);e=e(void 0!==i?i:t.custom,s,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,r]=c(n);e=e(void 0!==i?i:t.custom,s,r)}return e}function p(t,e,i){let n=t.getProps();return d(n,e,void 0!==i?i:n.custom,t)}let m=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],f=new Set(m),g=new Set(["width","height","top","left","right","bottom",...m]),v=t=>Array.isArray(t),y=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),x=t=>v(t)?t[t.length-1]||0:t,b=["read","resolveKeyframes","update","preRender","render","postRender"],w={value:null,addProjectionMetrics:null},P={skipAnimations:!1,useManualTiming:!1};function T(t,e){let i=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=b.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,r=!1)=>{let o=r&&s?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(o=t,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(h),e&&w.value&&w.value.frameloop[e].push(l),l=0,i.clear(),s=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{read:o,resolveKeyframes:l,update:h,preRender:u,render:c,postRender:d}=a,p=()=>{let r=P.useManualTiming?s.timestamp:performance.now();i=!1,P.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(r-s.timestamp,40),1)),s.timestamp=r,s.isProcessing=!0,o.process(s),l.process(s),h.process(s),u.process(s),c.process(s),d.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(p))},m=()=>{i=!0,n=!0,s.isProcessing||t(p)};return{schedule:b.reduce((t,e)=>{let n=a[e];return t[e]=(t,e=!1,s=!1)=>(i||m(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<b.length;e++)a[b[e]].cancel(t)},state:s,steps:a}}let M=t=>t,{schedule:S,cancel:A,state:k,steps:C}=T("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:M,!0);function E(){n=void 0}let D={now:()=>(void 0===n&&D.set(k.isProcessing||P.useManualTiming?k.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(E)}};function V(t,e){-1===t.indexOf(e)&&t.push(e)}function j(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class R{constructor(){this.subscriptions=[]}add(t){return V(this.subscriptions,t),()=>j(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let L=t=>!isNaN(parseFloat(t)),F={current:void 0};class B{constructor(t,e={}){this.version="12.7.4",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=D.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=D.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=L(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new R);let i=this.events[t].add(e);return"change"===t?()=>{i(),S.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return F.current&&F.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=D.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function I(t,e){return new B(t,e)}let O=t=>!!(t&&t.getVelocity);function N(t,e){let i=t.getValue("willChange");if(O(i)&&i.add)return i.add(e);if(!i&&P.WillChange){let i=new P.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let U=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),z="data-"+U("framerAppearId"),$={current:!1},W=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function Y(t,e,i,n){if(t===e&&i===n)return M;let s=e=>(function(t,e,i,n,s){let r,a,o=0;do(r=W(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:W(s(t),e,n)}let X=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,q=t=>e=>1-t(1-e),_=Y(.33,1.53,.69,.99),H=q(_),K=X(H),G=t=>(t*=2)<1?.5*H(t):.5*(2-Math.pow(2,-10*(t-1))),Z=t=>1-Math.sin(Math.acos(t)),J=q(Z),Q=X(Z),tt=t=>/^0[^.\s]+$/u.test(t),te=(t,e,i)=>i>e?e:i<t?t:i,ti={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tn={...ti,transform:t=>te(0,1,t)},ts={...ti,default:1},tr=t=>Math.round(1e5*t)/1e5,ta=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,to=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tl=(t,e)=>i=>!!("string"==typeof i&&to.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),th=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,a,o]=n.match(ta);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tu=t=>te(0,255,t),tc={...ti,transform:t=>Math.round(tu(t))},td={test:tl("rgb","red"),parse:th("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tc.transform(t)+", "+tc.transform(e)+", "+tc.transform(i)+", "+tr(tn.transform(n))+")"},tp={test:tl("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:td.transform},tm=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tf=tm("deg"),tg=tm("%"),tv=tm("px"),ty=tm("vh"),tx=tm("vw"),tb={...tg,parse:t=>tg.parse(t)/100,transform:t=>tg.transform(100*t)},tw={test:tl("hsl","hue"),parse:th("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tg.transform(tr(e))+", "+tg.transform(tr(i))+", "+tr(tn.transform(n))+")"},tP={test:t=>td.test(t)||tp.test(t)||tw.test(t),parse:t=>td.test(t)?td.parse(t):tw.test(t)?tw.parse(t):tp.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?td.transform(t):tw.transform(t)},tT=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tM="number",tS="color",tA=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tk(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,a=e.replace(tA,t=>(tP.test(t)?(n.color.push(r),s.push(tS),i.push(tP.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(tM),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:n,types:s}}function tC(t){return tk(t).values}function tE(t){let{split:e,types:i}=tk(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===tM?s+=tr(t[r]):e===tS?s+=tP.transform(t[r]):s+=t[r]}return s}}let tD=t=>"number"==typeof t?0:t,tV={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(ta)?.length||0)+(t.match(tT)?.length||0)>0},parse:tC,createTransformer:tE,getAnimatableNone:function(t){let e=tC(t);return tE(t)(e.map(tD))}},tj=new Set(["brightness","contrast","saturate","opacity"]);function tR(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(ta)||[];if(!n)return t;let s=i.replace(n,""),r=+!!tj.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let tL=/\b([a-z-]*)\(.*?\)/gu,tF={...tV,getAnimatableNone:t=>{let e=t.match(tL);return e?e.map(tR).join(" "):t}},tB={...ti,transform:Math.round},tI={borderWidth:tv,borderTopWidth:tv,borderRightWidth:tv,borderBottomWidth:tv,borderLeftWidth:tv,borderRadius:tv,radius:tv,borderTopLeftRadius:tv,borderTopRightRadius:tv,borderBottomRightRadius:tv,borderBottomLeftRadius:tv,width:tv,maxWidth:tv,height:tv,maxHeight:tv,top:tv,right:tv,bottom:tv,left:tv,padding:tv,paddingTop:tv,paddingRight:tv,paddingBottom:tv,paddingLeft:tv,margin:tv,marginTop:tv,marginRight:tv,marginBottom:tv,marginLeft:tv,backgroundPositionX:tv,backgroundPositionY:tv,rotate:tf,rotateX:tf,rotateY:tf,rotateZ:tf,scale:ts,scaleX:ts,scaleY:ts,scaleZ:ts,skew:tf,skewX:tf,skewY:tf,distance:tv,translateX:tv,translateY:tv,translateZ:tv,x:tv,y:tv,z:tv,perspective:tv,transformPerspective:tv,opacity:tn,originX:tb,originY:tb,originZ:tv,zIndex:tB,size:tv,fillOpacity:tn,strokeOpacity:tn,numOctaves:tB},tO={...tI,color:tP,backgroundColor:tP,outlineColor:tP,fill:tP,stroke:tP,borderColor:tP,borderTopColor:tP,borderRightColor:tP,borderBottomColor:tP,borderLeftColor:tP,filter:tF,WebkitFilter:tF},tN=t=>tO[t];function tU(t,e){let i=tN(t);return i!==tF&&(i=tV),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tz=new Set(["auto","none","0"]),t$=t=>180*t/Math.PI,tW=t=>tX(t$(Math.atan2(t[1],t[0]))),tY={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tW,rotateZ:tW,skewX:t=>t$(Math.atan(t[1])),skewY:t=>t$(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},tX=t=>((t%=360)<0&&(t+=360),t),tq=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),t_=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tH={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tq,scaleY:t_,scale:t=>(tq(t)+t_(t))/2,rotateX:t=>tX(t$(Math.atan2(t[6],t[5]))),rotateY:t=>tX(t$(Math.atan2(-t[2],t[0]))),rotateZ:tW,rotate:tW,skewX:t=>t$(Math.atan(t[4])),skewY:t=>t$(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tK(t){return+!!t.includes("scale")}function tG(t,e){let i,n;if(!t||"none"===t)return tK(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=tH,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tY,n=e}if(!n)return tK(e);let r=i[e],a=n[1].split(",").map(tJ);return"function"==typeof r?r(a):a[r]}let tZ=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return tG(i,e)};function tJ(t){return parseFloat(t.trim())}let tQ=t=>t===ti||t===tv,t0=new Set(["x","y","z"]),t1=m.filter(t=>!t0.has(t)),t2={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>tG(e,"x"),y:(t,{transform:e})=>tG(e,"y")};t2.translateX=t2.x,t2.translateY=t2.y;let t5=new Set,t3=!1,t4=!1;function t6(){if(t4){let t=Array.from(t5).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return t1.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}t4=!1,t3=!1,t5.forEach(t=>t.complete()),t5.clear()}function t9(){t5.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(t4=!0)})}class t8{constructor(t,e,i,n,s,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(t5.add(this),t3||(t3=!0,S.read(t9),S.resolveKeyframes(t6))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),t5.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,t5.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let t7=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),et=t=>e=>"string"==typeof e&&e.startsWith(t),ee=et("--"),ei=et("var(--"),en=t=>!!ei(t)&&es.test(t.split("/*")[0].trim()),es=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,er=()=>{},ea=()=>{},eo=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,el=t=>e=>e.test(t),eh=[ti,tv,tg,tf,tx,ty,{test:t=>"auto"===t,parse:t=>t}],eu=t=>eh.find(el(t));class ec extends t8{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&en(n=n.trim())){let s=function t(e,i,n=1){ea(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,r]=function(t){let e=eo.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return t7(t)?parseFloat(t):t}return en(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!g.has(i)||2!==t.length)return;let[n,s]=t,r=eu(n),a=eu(s);if(r!==a)if(tQ(r)&&tQ(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tt(n))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!tz.has(e)&&tk(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=tU(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=t2[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=t2[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let ed=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tV.test(t)||"0"===t)&&!t.startsWith("url("));function ep(t){return"function"==typeof t&&"applyToOptions"in t}let em=t=>null!==t;function ef(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(em),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return r&&void 0!==n?n:s[r]}class eg{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=D.now(),this.options={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(t9(),t6()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=D.now(),this.hasAttemptedResolve=!0;let{name:i,type:n,velocity:s,delay:r,onComplete:a,onUpdate:o,isGenerator:l}=this.options;if(!l&&!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=ed(s,e),o=ed(r,e);return er(a===o,`You are trying to animate ${e} from "${s}" to "${r}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ep(i))&&n)}(t,i,n,s))if($.current||!r){o&&o(ef(t,this.options,e)),a&&a(),this.resolveFinishedPromise();return}else this.options.duration=0;let h=this.initPlayback(t,e);!1!==h&&(this._resolved={keyframes:t,finalKeyframe:e,...h},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let ev=(t,e,i)=>t+(e-t)*i;function ey(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ex(t,e){return i=>i>0?e:t}let eb=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},ew=[tp,td,tw],eP=t=>ew.find(e=>e.test(t));function eT(t){let e=eP(t);if(er(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tw&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=ey(o,n,t+1/3),r=ey(o,n,t),a=ey(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let eM=(t,e)=>{let i=eT(t),n=eT(e);if(!i||!n)return ex(t,e);let s={...i};return t=>(s.red=eb(i.red,n.red,t),s.green=eb(i.green,n.green,t),s.blue=eb(i.blue,n.blue,t),s.alpha=ev(i.alpha,n.alpha,t),td.transform(s))},eS=(t,e)=>i=>e(t(i)),eA=(...t)=>t.reduce(eS),ek=new Set(["none","hidden"]);function eC(t,e){return i=>ev(t,e,i)}function eE(t){return"number"==typeof t?eC:"string"==typeof t?en(t)?ex:tP.test(t)?eM:ej:Array.isArray(t)?eD:"object"==typeof t?tP.test(t)?eM:eV:ex}function eD(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>eE(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function eV(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=eE(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let ej=(t,e)=>{let i=tV.createTransformer(e),n=tk(t),s=tk(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?ek.has(t)&&!s.values.length||ek.has(e)&&!n.values.length?function(t,e){return ek.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eA(eD(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,s),s.values),i):(er(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),ex(t,e))};function eR(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?ev(t,e,i):eE(t)(t,e)}function eL(t,e,i){var n,s;let r=Math.max(e-5,0);return n=i-t(r),(s=e-r)?1e3/s*n:0}let eF={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},eB=t=>1e3*t,eI=t=>t/1e3;function eO(t,e){return t*Math.sqrt(1-e*e)}function eN(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let eU={};function ez(t){let e;return()=>(void 0===e&&(e=t()),e)}let e$=function(t,e){let i=ez(t);return()=>eU[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eW=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=t(e/(s-1))+", ";return`linear(${n.substring(0,n.length-2)})`},eY=["duration","bounce"],eX=["stiffness","damping","mass"];function eq(t,e){return e.some(e=>void 0!==t[e])}function e_(t=eF.visualDuration,e=eF.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eF.velocity,stiffness:eF.stiffness,damping:eF.damping,mass:eF.mass,isResolvedFromDuration:!1,...t};if(!eq(t,eX)&&eq(t,eY))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*te(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:eF.mass,stiffness:n,damping:s}}else{let i=function({duration:t=eF.duration,bounce:e=eF.bounce,velocity:i=eF.velocity,mass:n=eF.mass}){let s,r;er(t<=eB(eF.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=te(eF.minDamping,eF.maxDamping,a),t=te(eF.minDuration,eF.maxDuration,eI(t)),a<1?(s=e=>{let n=e*a,s=n*t;return .001-(n-i)/eO(e,a)*Math.exp(-s)},r=e=>{let n=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-n),l=eO(Math.pow(e,2),a);return(n*i+i-r)*o*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=eB(t),isNaN(o))return{stiffness:eF.stiffness,damping:eF.damping,duration:t};{let e=Math.pow(o,2)*n;return{stiffness:e,damping:2*a*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:eF.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-eI(n.velocity||0)}),f=p||0,g=u/(2*Math.sqrt(h*c)),v=o-a,y=eI(Math.sqrt(h/c)),x=5>Math.abs(v);if(s||(s=x?eF.restSpeed.granular:eF.restSpeed.default),r||(r=x?eF.restDelta.granular:eF.restDelta.default),g<1){let t=eO(y,g);i=e=>o-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-y*t)*(v+(f+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),n=Math.min(t*e,300);return o-i*((f+g*y*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let b={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let n=0;g<1&&(n=0===t?eB(f):eL(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(n)<=s&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(eN(b),2e4),e=eW(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function eH({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let c,d,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,v=i*e,y=p+v,x=void 0===a?y:a(y);x!==y&&(v=x-p);let b=t=>-v*Math.exp(-t/n),w=t=>x+b(t),P=t=>{let e=b(t),i=w(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},T=t=>{f(m.value)&&(c=t,d=e_({keyframes:[m.value,g(m.value)],velocity:eL(w,t,m.value),damping:s,stiffness:r,restDelta:h,restSpeed:u}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>=c)?d.next(t-c):(e||P(t),m)}}}e_.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min(eN(n),2e4);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:eI(s)}}(t,100,e_);return t.ease=e$()?e.ease:"easeOut",t.duration=eB(e.duration),t.type="keyframes",t};let eK=Y(.42,0,1,1),eG=Y(0,0,.58,1),eZ=Y(.42,0,.58,1),eJ=t=>Array.isArray(t)&&"number"!=typeof t[0],eQ=t=>Array.isArray(t)&&"number"==typeof t[0],e0={linear:M,easeIn:eK,easeInOut:eZ,easeOut:eG,circIn:Z,circInOut:Q,circOut:J,backIn:H,backInOut:K,backOut:_,anticipate:G},e1=t=>{if(eQ(t)){ea(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return Y(e,i,n,s)}return"string"==typeof t?(ea(void 0!==e0[t],`Invalid easing type '${t}'`),e0[t]):t},e2=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function e5({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var s;let r=eJ(n)?n.map(e1):e1(n),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let r=t.length;if(ea(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],s=i||eR,r=t.length-1;for(let i=0;i<r;i++){let r=s(t[i],t[i+1]);e&&(r=eA(Array.isArray(e)?e[i]||M:e,r)),n.push(r)}return n}(e,n,s),l=o.length,h=i=>{if(a&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=e2(t[n],t[n+1],i);return o[n](s)};return i?e=>h(te(t[0],t[r-1],e)):h}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=e2(0,e,n);t.push(ev(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||eZ).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let e3=t=>{let e=({timestamp:e})=>t(e);return{start:()=>S.update(e,!0),stop:()=>A(e),now:()=>k.isProcessing?k.timestamp:D.now()}},e4={layout:0,mainThread:0,waapi:0},e6={decay:eH,inertia:eH,tween:e5,keyframes:e5,spring:e_},e9=t=>t/100;class e8 extends eg{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:n,keyframes:s}=this.options,r=n?.KeyframeResolver||t8;this.resolver=new r(s,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:a,velocity:o=0}=this.options,l=ep(n)?n:e6[n]||e5;l!==e5&&"number"!=typeof t[0]&&(e=eA(e9,eR(t[0],t[1])),t=[0,100]);let h=l({...this.options,keyframes:t});"mirror"===a&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===h.calculatedDuration&&(h.calculatedDuration=eN(h));let{calculatedDuration:u}=h,c=u+r;return{generator:h,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:u,resolvedDuration:c,totalDuration:c*(s+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;e4.mainThread++,this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:s,mirroredGenerator:r,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:h,resolvedDuration:u}=i;if(null===this.startTime)return s.next(0);let{delay:c,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-h/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let g=this.currentTime-c*(this.speed>=0?1:-1),v=this.speed>=0?g<0:g>h;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=h);let y=this.currentTime,x=s;if(d){let t=Math.min(this.currentTime,h)/u,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/u)):"mirror"===p&&(x=r)),y=te(0,1,i)*u}let b=v?{done:!1,value:o[0]}:x.next(y);a&&(b.value=a(b.value));let{done:w}=b;v||null===l||(w=this.speed>=0?this.currentTime>=h:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&void 0!==n&&(b.value=ef(o,this.options,n)),f&&f(b.value),P&&this.finish(),b}get duration(){let{resolved:t}=this;return t?eI(t.calculatedDuration):0}get time(){return eI(this.currentTime)}set time(t){t=eB(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=eI(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e3,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=i??this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=this.currentTime??0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),e4.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}get finished(){return this.currentFinishedPromise}}let e7=new Set(["opacity","clipPath","filter","transform"]),it=ez(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ie=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,ii={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ie([0,.65,.55,1]),circOut:ie([.55,0,1,.45]),backIn:ie([.31,.01,.66,-.59]),backOut:ie([.33,1.53,.69,.99])};function is(t,e){t.timeline=e,t.onfinish=null}let ir={anticipate:G,backInOut:K,circInOut:Q};class ia extends eg{constructor(t){super(t);let{name:e,motionValue:i,element:n,keyframes:s}=this.options;this.resolver=new ec(s,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:n=300,times:s,ease:r,type:a,motionValue:o,name:l,startTime:h}=this.options;if(!o.owner||!o.owner.current)return!1;if("string"==typeof r&&e$()&&r in ir&&(r=ir[r]),ep((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&e$()||!e||"string"==typeof e&&(e in ii||e$())||eQ(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:o,element:l,...h}=this.options,u=function(t,e){let i=new e8({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},s=[],r=0;for(;!n.done&&r<2e4;)s.push((n=i.sample(r)).value),r+=10;return{times:void 0,keyframes:s,duration:r-10,ease:"linear"}}(t,h);1===(t=u.keyframes).length&&(t[1]=t[0]),n=u.duration,s=u.times,r=u.ease,a="keyframes"}let u=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeInOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let c=function t(e,i){if(e)return"function"==typeof e&&e$()?eW(e,i):eQ(e)?ie(e):Array.isArray(e)?e.map(e=>t(e,i)||ii.easeOut):ii[e]}(o,s);Array.isArray(c)&&(u.easing=c),w.value&&e4.waapi++;let d=t.animate(u,{delay:n,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal",pseudoElement:void 0});return w.value&&d.finished.finally(()=>{e4.waapi--}),d}(o.owner.current,l,t,{...this.options,duration:n,times:s,ease:r});return u.startTime=h??this.calcStartTime(),this.pendingTimeline?(is(u,this.pendingTimeline),this.pendingTimeline=void 0):u.onfinish=()=>{let{onComplete:i}=this.options;o.set(ef(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:u,duration:n,times:s,type:a,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return eI(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return eI(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=eB(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}get finished(){return this.resolved.animation.finished}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return M;let{animation:i}=e;is(i,t)}else this.pendingTimeline=t;return M}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:n,type:s,ease:r,times:a}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:o,element:l,...h}=this.options,u=new e8({...h,keyframes:i,duration:n,type:s,ease:r,times:a,isGenerator:!0}),c=eB(this.time);t.setWithVelocity(u.sample(c-10).value,u.sample(c).value,10)}let{onStop:o}=this.options;o&&o(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return it()&&i&&e7.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}}let io={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ih={type:"keyframes",duration:.8},iu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ic=(t,{keyframes:e})=>e.length>2?ih:f.has(t)?t.startsWith("scale")?il(e[1]):io:iu;function id(t,e){return t?.[e]??t?.default??t}let ip=ez(()=>void 0!==window.ScrollTimeline);class im{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>ip()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class ig extends im{then(t,e){return this.finished.finally(t).then(()=>{})}}let iv=(t,e,i,n={},s,r)=>a=>{let o=id(n,t)||{},l=o.delay||n.delay||0,{elapsed:h=0}=n;h-=eB(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-h,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(o)&&(u={...u,...ic(t,u)}),u.duration&&(u.duration=eB(u.duration)),u.repeatDelay&&(u.repeatDelay=eB(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0===u.delay&&(c=!0)),($.current||P.skipAnimations)&&(c=!0,u.duration=0,u.delay=0),u.allowFlatten=!o.type&&!o.ease,c&&!r&&void 0!==e.get()){let t=ef(u.keyframes,o);if(void 0!==t)return S.update(()=>{u.onUpdate(t),u.onComplete()}),new ig([])}return!r&&ia.supports(u)?new ia(u):new e8(u)};function iy(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;n&&(r=n);let l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(let e in o){let n=t.getValue(e,t.latestValues[e]??null),s=o[e];if(void 0===s||h&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(h,e))continue;let a={delay:i,...id(r||{},e)},u=!1;if(window.MotionHandoffAnimation){let i=t.props[z];if(i){let t=window.MotionHandoffAnimation(i,e,S);null!==t&&(a.startTime=t,u=!0)}}N(t,e),n.start(iv(e,n,s,t.shouldReduceMotion&&g.has(e)?{type:!1}:a,t,u));let c=n.animation;c&&l.push(c)}return a&&Promise.all(l).then(()=>{S.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=p(t,e)||{};for(let e in s={...s,...i}){let i=x(s[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,I(i))}}(t,a)})}),l}function ix(t,e,i={}){let n=p(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(iy(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=1,r){let a=[],o=(t.variantChildren.size-1)*n,l=1===s?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(ib).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(ix(t,e,{...r,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+n,a,o,i)}:()=>Promise.resolve(),{when:o}=s;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function ib(t,e){return t.sortNodePosition(e)}function iw(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function iP(t){return"string"==typeof t||Array.isArray(t)}let iT=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],iM=["initial",...iT],iS=iM.length,iA=[...iT].reverse(),ik=iT.length;function iC(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iE(){return{animate:iC(!0),whileInView:iC(),whileHover:iC(),whileTap:iC(),whileDrag:iC(),whileFocus:iC(),exit:iC()}}class iD{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iV extends iD{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>ix(t,e,i)));else if("string"==typeof e)n=ix(t,e,i);else{let s="function"==typeof e?p(t,e,i.custom):e;n=Promise.all(iy(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iE(),n=!0,s=e=>(i,n)=>{let s=p(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<iS;t++){let n=iM[t],s=e.props[n];(iP(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},l=[],h=new Set,c={},d=1/0;for(let e=0;e<ik;e++){var m,f;let p=iA[e],g=i[p],y=void 0!==a[p]?a[p]:o[p],x=iP(y),b=p===r?g.isActive:null;!1===b&&(d=e);let w=y===o[p]&&y!==a[p]&&x;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...c},!g.isActive&&null===b||!y&&!g.prevProp||u(y)||"boolean"==typeof y)continue;let P=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!iw(f,m)),T=P||p===r&&g.isActive&&!w&&x||e>d&&x,M=!1,S=Array.isArray(y)?y:[y],A=S.reduce(s(p),{});!1===b&&(A={});let{prevResolvedValues:k={}}=g,C={...k,...A},E=e=>{T=!0,h.has(e)&&(M=!0,h.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=A[t],i=k[t];if(c.hasOwnProperty(t))continue;let n=!1;(v(e)&&v(i)?iw(e,i):e===i)?void 0!==e&&h.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(c={...c,...A}),n&&t.blockInitialAnimation&&(T=!1);let D=!(w&&P)||M;T&&D&&l.push(...S.map(t=>({animation:t,options:{type:p}})))}if(h.size){let e={};if("boolean"!=typeof a.initial){let i=p(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let g=!!l.length;return n&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=r(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iE(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();u(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ij=0;class iR extends iD{constructor(){super(...arguments),this.id=ij++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function iL(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iF=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iB(t){return{point:{x:t.pageX,y:t.pageY}}}let iI=t=>e=>iF(e)&&t(e,iB(e));function iO(t,e,i,n){return iL(t,e,iI(i),n)}function iN({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iU(t){return t.max-t.min}function iz(t,e,i,n=.5){t.origin=n,t.originPoint=ev(e.min,e.max,t.origin),t.scale=iU(i)/iU(e),t.translate=ev(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function i$(t,e,i,n){iz(t.x,e.x,i.x,n?n.originX:void 0),iz(t.y,e.y,i.y,n?n.originY:void 0)}function iW(t,e,i){t.min=i.min+e.min,t.max=t.min+iU(e)}function iY(t,e,i){t.min=e.min-i.min,t.max=t.min+iU(e)}function iX(t,e,i){iY(t.x,e.x,i.x),iY(t.y,e.y,i.y)}let iq=()=>({translate:0,scale:1,origin:0,originPoint:0}),i_=()=>({x:iq(),y:iq()}),iH=()=>({min:0,max:0}),iK=()=>({x:iH(),y:iH()});function iG(t){return[t("x"),t("y")]}function iZ(t){return void 0===t||1===t}function iJ({scale:t,scaleX:e,scaleY:i}){return!iZ(t)||!iZ(e)||!iZ(i)}function iQ(t){return iJ(t)||i0(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i0(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i1(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function i2(t,e=0,i=1,n,s){t.min=i1(t.min,e,i,n,s),t.max=i1(t.max,e,i,n,s)}function i5(t,{x:e,y:i}){i2(t.x,e.translate,e.scale,e.originPoint),i2(t.y,i.translate,i.scale,i.originPoint)}function i3(t,e){t.min=t.min+e,t.max=t.max+e}function i4(t,e,i,n,s=.5){let r=ev(t.min,t.max,s);i2(t,e,i,r,n)}function i6(t,e){i4(t.x,e.x,e.scaleX,e.scale,e.originX),i4(t.y,e.y,e.scaleY,e.scale,e.originY)}function i9(t,e){return iN(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let i8=({current:t})=>t?t.ownerDocument.defaultView:null;function i7(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let nt=(t,e)=>Math.abs(t-e);class ne{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=ns(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nt(t.x,e.x)**2+nt(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=k;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ni(e,this.transformPagePoint),S.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=ns("pointercancel"===t.type?this.lastMoveEventInfo:ni(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!iF(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let r=ni(iB(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=k;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,ns(r,this.history)),this.removeListeners=eA(iO(this.contextWindow,"pointermove",this.handlePointerMove),iO(this.contextWindow,"pointerup",this.handlePointerUp),iO(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),A(this.updatePoint)}}function ni(t,e){return e?{point:e(t.point)}:t}function nn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ns({point:t},e){return{point:t,delta:nn(t,nr(e)),offset:nn(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=nr(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>eB(.1)));)i--;if(!n)return{x:0,y:0};let r=eI(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function nr(t){return t[t.length-1]}function na(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function no(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function nl(t,e,i){return{min:nh(t,e),max:nh(t,i)}}function nh(t,e){return"number"==typeof t?t:t[e]||0}let nu={x:!1,y:!1},nc=new WeakMap;class nd{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iK(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new ne(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iB(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nu[t])return null;else return nu[t]=!0,()=>{nu[t]=!1};return nu.x||nu.y?null:(nu.x=nu.y=!0,()=>{nu.x=nu.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iG(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tg.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iU(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&S.postRender(()=>s(t,e)),N(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iG(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:i8(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:s}=this.getProps();s&&S.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!np(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?ev(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?ev(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&i7(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:na(t.x,i,s),y:na(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:nl(t,"left","right"),y:nl(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iG(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i7(e))return!1;let n=e.current;ea(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=i9(t,i),{scroll:s}=e;return s&&(i3(n.x,s.offset.x),i3(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:no(t.x,r.x),y:no(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iN(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iG(a=>{if(!np(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return N(this.visualElement,t),i.start(iv(t,i,0,e,this.visualElement,!1))}stopAnimation(){iG(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iG(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iG(e=>{let{drag:i}=this.getProps();if(!np(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-ev(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i7(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iG(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iU(t),s=iU(e);return s>n?i=e2(e.min,e.max-n,t.min):n>s&&(i=e2(t.min,t.max-s,e.min)),te(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iG(e=>{if(!np(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set(ev(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;nc.set(this.visualElement,this);let t=iO(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i7(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),S.read(e);let s=iL(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iG(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function np(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nm extends iD{constructor(t){super(t),this.removeGroupControls=M,this.removeListeners=M,this.controls=new nd(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||M}unmount(){this.removeGroupControls(),this.removeListeners()}}let nf=t=>(e,i)=>{t&&S.postRender(()=>t(e,i))};class ng extends iD{constructor(){super(...arguments),this.removePointerDownListener=M}onPointerDown(t){this.session=new ne(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i8(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:nf(t),onStart:nf(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&S.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iO(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nv=(0,l.createContext)(null),ny=(0,l.createContext)({}),nx=(0,l.createContext)({}),nb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nw(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nP={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tv.test(t))return t;else t=parseFloat(t);let i=nw(t,e.target.x),n=nw(t,e.target.y);return`${i}% ${n}%`}},nT={},{schedule:nM}=T(queueMicrotask,!1);class nS extends l.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in nk)nT[t]=nk[t],ee(t)&&(nT[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),nb.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,r=i.projection;return r&&(r.isPresent=s,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||S.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),nM.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nA(t){let[e,i]=function(t=!0){let e=(0,l.useContext)(nv);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:s}=e,r=(0,l.useId)();(0,l.useEffect)(()=>{if(t)return s(r)},[t]);let a=(0,l.useCallback)(()=>t&&n&&n(r),[r,n,t]);return!i&&n?[!1,a]:[!0]}(),n=(0,l.useContext)(ny);return(0,a.jsx)(nS,{...t,layoutGroup:n,switchLayoutGroup:(0,l.useContext)(nx),isPresent:e,safeToRemove:i})}let nk={borderRadius:{...nP,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nP,borderTopRightRadius:nP,borderBottomLeftRadius:nP,borderBottomRightRadius:nP,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tV.parse(t);if(n.length>5)return t;let s=tV.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=ev(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}},nC=(t,e)=>t.depth-e.depth;class nE{constructor(){this.children=[],this.isDirty=!1}add(t){V(this.children,t),this.isDirty=!0}remove(t){j(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nC),this.isDirty=!1,this.children.forEach(t)}}function nD(t){let e=O(t)?t.get():t;return y(e)?e.toValue():e}let nV=["TopLeft","TopRight","BottomLeft","BottomRight"],nj=nV.length,nR=t=>"string"==typeof t?parseFloat(t):t,nL=t=>"number"==typeof t||tv.test(t);function nF(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nB=nO(0,.5,J),nI=nO(.5,.95,M);function nO(t,e,i){return n=>n<t?0:n>e?1:i(e2(t,e,n))}function nN(t,e){t.min=e.min,t.max=e.max}function nU(t,e){nN(t.x,e.x),nN(t.y,e.y)}function nz(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function n$(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function nW(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(tg.test(e)&&(e=parseFloat(e),e=ev(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=ev(r.min,r.max,n);t===r&&(o-=e),t.min=n$(t.min,e,i,o,s),t.max=n$(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let nY=["x","scaleX","originX"],nX=["y","scaleY","originY"];function nq(t,e,i,n){nW(t.x,e,nY,i?i.x:void 0,n?n.x:void 0),nW(t.y,e,nX,i?i.y:void 0,n?n.y:void 0)}function n_(t){return 0===t.translate&&1===t.scale}function nH(t){return n_(t.x)&&n_(t.y)}function nK(t,e){return t.min===e.min&&t.max===e.max}function nG(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nZ(t,e){return nG(t.x,e.x)&&nG(t.y,e.y)}function nJ(t){return iU(t.x)/iU(t.y)}function nQ(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class n0{constructor(){this.members=[]}add(t){V(this.members,t),t.scheduleRender()}remove(t){if(j(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let n1={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n2=["","X","Y","Z"],n5={visibility:"hidden"},n3=0;function n4(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n6({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=n3++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,w.value&&(n1.nodes=n1.calculatedTargetDeltas=n1.calculatedProjections=0),this.nodes.forEach(n7),this.nodes.forEach(sa),this.nodes.forEach(so),this.nodes.forEach(st),w.addProjectionMetrics&&w.addProjectionMetrics(n1)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nE)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new R),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(s||n)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=D.now(),n=({timestamp:s})=>{let r=s-i;r>=250&&(A(n),t(r-e))};return S.read(n,!0),()=>A(n)}(n,250),nb.hasAnimatedSinceResize&&(nb.hasAnimatedSinceResize=!1,this.nodes.forEach(sr))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&r&&(n||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||sp,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!nZ(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...id(s,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||sr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,A(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sl),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[z];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",S,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(si);return}this.isUpdating||this.nodes.forEach(sn),this.isUpdating=!1,this.nodes.forEach(ss),this.nodes.forEach(n9),this.nodes.forEach(n8),this.clearAllSnapshots();let t=D.now();k.delta=te(0,1e3/60,t-k.timestamp),k.timestamp=t,k.isProcessing=!0,C.update.process(k),C.preRender.process(k),C.render.process(k),k.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nM.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(se),this.sharedNodes.forEach(sh)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,S.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){S.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iU(this.snapshot.measuredBox.x)||iU(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iK(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nH(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&(e||iQ(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),sg((e=n).x),sg(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iK();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(sy))){let{scroll:t}=this.root;t&&(i3(e.x,t.offset.x),i3(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iK();if(nU(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&nU(e,t),i3(e.x,s.offset.x),i3(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=iK();nU(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i6(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iQ(n.latestValues)&&i6(i,n.latestValues)}return iQ(this.latestValues)&&i6(i,this.latestValues),i}removeTransform(t){let e=iK();nU(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iQ(i.latestValues))continue;iJ(i.latestValues)&&i.updateSnapshot();let n=iK();nU(n,i.measurePageBox()),nq(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iQ(this.latestValues)&&nq(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==k.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=k.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iK(),this.relativeTargetOrigin=iK(),iX(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nU(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iK(),this.targetWithTransforms=iK()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,iW(r.x,a.x,o.x),iW(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nU(this.target,this.layout.layoutBox),i5(this.target,this.targetDelta)):nU(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iK(),this.relativeTargetOrigin=iK(),iX(this.relativeTargetOrigin,this.target,t.target),nU(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}w.value&&n1.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iJ(this.parent.latestValues)||i0(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===k.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;nU(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i6(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,i5(t,r)),n&&iQ(s.latestValues)&&i6(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iK());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nz(this.prevProjectionDelta.x,this.projectionDelta.x),nz(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),i$(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&nQ(this.projectionDelta.x,this.prevProjectionDelta.x)&&nQ(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),w.value&&n1.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=i_(),this.projectionDelta=i_(),this.projectionDeltaWithTransform=i_()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=i_();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iK(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,c=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(sd));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(su(a.x,t.x,n),su(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,d,p,m,f,g;iX(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,sc(p.x,m.x,f.x,g),sc(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,d=i,nK(h.x,d.x)&&nK(h.y,d.y))&&(this.isProjectionDirty=!1),i||(i=iK()),nU(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=ev(0,i.opacity??1,nB(n)),t.opacityExit=ev(e.opacity??1,0,nI(n))):r&&(t.opacity=ev(e.opacity??1,i.opacity??1,n));for(let s=0;s<nj;s++){let r=`border${nV[s]}Radius`,a=nF(e,r),o=nF(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||nL(a)===nL(o)?(t[r]=Math.max(ev(nR(a),nR(o),n),0),(tg.test(o)||tg.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=ev(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(A(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=S.update(()=>{nb.hasAnimatedSinceResize=!0,e4.layout++,this.currentAnimation=function(t,e,i){let n=O(0)?0:I(t);return n.start(iv("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{e4.layout--},onComplete:()=>{e4.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&sv(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iK();let e=iU(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iU(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nU(e,i),i6(e,s),i$(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new n0),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n4("z",t,n,this.animationValues);for(let e=0;e<n2.length;e++)n4(`rotate${n2[e]}`,t,n,this.animationValues),n4(`skew${n2[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return n5;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nD(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nD(t?.pointerEvents)||""),this.hasProjected&&!iQ(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,nT){if(void 0===s[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=nT[t],o="none"===e.transform?s[t]:i(s[t],n);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=n===this?nD(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(si),this.root.sharedNodes.clear()}}}function n9(t){t.updateLayout()}function n8(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?iG(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=iU(n);n.min=i[t].min,n.max=n.min+s}):sv(s,e.layoutBox,i)&&iG(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=iU(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=i_();i$(a,i,e.layoutBox);let o=i_();r?i$(o,t.applyTransform(n,!0),e.measuredBox):i$(o,i,e.layoutBox);let l=!nH(a),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=iK();iX(a,e.layoutBox,s.layoutBox);let o=iK();iX(o,i,r.layoutBox),nZ(a,o)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n7(t){w.value&&n1.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function st(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function se(t){t.clearSnapshot()}function si(t){t.clearMeasurements()}function sn(t){t.isLayoutDirty=!1}function ss(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sa(t){t.resolveTargetDelta()}function so(t){t.calcProjection()}function sl(t){t.resetSkewAndRotation()}function sh(t){t.removeLeadSnapshot()}function su(t,e,i){t.translate=ev(e.translate,0,i),t.scale=ev(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sc(t,e,i,n){t.min=ev(e.min,i.min,n),t.max=ev(e.max,i.max,n)}function sd(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let sp={duration:.45,ease:[.4,0,.1,1]},sm=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),sf=sm("applewebkit/")&&!sm("chrome/")?Math.round:M;function sg(t){t.min=sf(t.min),t.max=sf(t.max)}function sv(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nJ(e)-nJ(i)))}function sy(t){return t!==t.root&&t.scroll?.wasRoot}let sx=n6({attachResizeListener:(t,e)=>iL(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sb={current:void 0},sw=n6({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sb.current){let t=new sx({});t.mount(window),t.setOptions({layoutScroll:!0}),sb.current=t}return sb.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sP(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function sT(t){return!("touch"===t.pointerType||nu.x||nu.y)}function sM(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&S.postRender(()=>s(e,iB(e)))}class sS extends iD{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=sP(t,i),a=t=>{if(!sT(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{sT(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(sM(this.node,e,"Start"),t=>sM(this.node,t,"End"))))}unmount(){}}class sA extends iD{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eA(iL(this.node.current,"focus",()=>this.onFocus()),iL(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let sk=(t,e)=>!!e&&(t===e||sk(t,e.parentElement)),sC=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sE=new WeakSet;function sD(t){return e=>{"Enter"===e.key&&t(e)}}function sV(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sj=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=sD(()=>{if(sE.has(i))return;sV(i,"down");let t=sD(()=>{sV(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sV(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function sR(t){return iF(t)&&!(nu.x||nu.y)}function sL(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&S.postRender(()=>s(e,iB(e)))}class sF extends iD{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=sP(t,i),a=t=>{let n=t.currentTarget;if(!sR(t)||sE.has(n))return;sE.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),sR(t)&&sE.has(n)&&(sE.delete(n),"function"==typeof r&&r(t,{success:e}))},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||sk(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),t instanceof HTMLElement)&&(t.addEventListener("focus",t=>sj(t,s)),sC.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(sL(this.node,e,"Start"),(t,{success:e})=>sL(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sB=new WeakMap,sI=new WeakMap,sO=t=>{let e=sB.get(t.target);e&&e(t)},sN=t=>{t.forEach(sO)},sU={some:0,all:1};class sz extends iD{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sU[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;sI.has(i)||sI.set(i,{});let n=sI.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(sN,{root:t,...e})),n[s]}(e);return sB.set(t,i),n.observe(t),()=>{sB.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let s$=(0,l.createContext)({strict:!1}),sW=(0,l.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),sY=(0,l.createContext)({});function sX(t){return u(t.animate)||iM.some(e=>iP(t[e]))}function sq(t){return!!(sX(t)||t.variants)}function s_(t){return Array.isArray(t)?t.join(" "):t}let sH="undefined"!=typeof window,sK={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sG={};for(let t in sK)sG[t]={isEnabled:e=>sK[t].some(t=>!!e[t])};let sZ=Symbol.for("motionComponentSymbol"),sJ=sH?l.useLayoutEffect:l.useEffect;function sQ(t,{layout:e,layoutId:i}){return f.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!nT[t]||"opacity"===t)}let s0=(t,e)=>e&&"number"==typeof t?e.transform(t):t,s1={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},s2=m.length;function s5(t,e,i){let{style:n,vars:s,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(f.has(t)){a=!0;continue}if(ee(t)){s[t]=i;continue}{let e=s0(i,tI[t]);t.startsWith("origin")?(o=!0,r[t]=e):n[t]=e}}if(!e.transform&&(a||i?n.transform=function(t,e,i){let n="",s=!0;for(let r=0;r<s2;r++){let a=m[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=s0(o,tI[a]);if(!l){s=!1;let e=s1[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;n.transformOrigin=`${t} ${e} ${i}`}}let s3=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function s4(t,e,i){for(let n in e)O(e[n])||sQ(n,i)||(t[n]=e[n])}let s6=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function s9(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||s6.has(t)}let s8=t=>!s9(t);try{!function(t){t&&(s8=e=>e.startsWith("on")?!s9(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let s7=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rt(t){if("string"!=typeof t||t.includes("-"));else if(s7.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let re={offset:"stroke-dashoffset",array:"stroke-dasharray"},ri={offset:"strokeDashoffset",array:"strokeDasharray"};function rn(t,e,i){return"string"==typeof t?t:tv.transform(e+i*t)}function rs(t,{attrX:e,attrY:i,attrScale:n,originX:s,originY:r,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...h},u,c){if(s5(t,h,c),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==s||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let n=rn(e,t.x,t.width),s=rn(i,t.y,t.height);return`${n} ${s}`}(m,void 0!==s?s:.5,void 0!==r?r:.5)),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==a&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?re:ri;t[r.offset]=tv.transform(-n);let a=tv.transform(e),o=tv.transform(i);t[r.array]=`${a} ${o}`}(d,a,o,l,!1)}let rr=()=>({...s3(),attrs:{}}),ra=t=>"string"==typeof t&&"svg"===t.toLowerCase(),ro=t=>(e,i)=>{let n=(0,l.useContext)(sY),s=(0,l.useContext)(nv),r=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,s,r){let a={latestValues:function(t,e,i,n){let s={},r=n(t,{});for(let t in r)s[t]=nD(r[t]);let{initial:a,animate:o}=t,l=sX(t),h=sq(t);e&&h&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?o:a;if(p&&"boolean"!=typeof p&&!u(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=d(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(n,s,r,t),renderState:e()};return i&&(a.onMount=t=>i({props:n,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,n,s);return i?r():function(t){let e=(0,l.useRef)(null);return null===e.current&&(e.current=t()),e.current}(r)};function rl(t,e,i){let{style:n}=t,s={};for(let r in n)(O(n[r])||e.style&&O(e.style[r])||sQ(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}let rh={useVisualState:ro({scrapeMotionValuesFromProps:rl,createRenderState:s3})};function ru(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}function rc(t,{style:e,vars:i},n,s){for(let r in Object.assign(t.style,e,s&&s.getProjectionStyles(n)),i)t.style.setProperty(r,i[r])}let rd=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function rp(t,e,i,n){for(let i in rc(t,e,void 0,n),e.attrs)t.setAttribute(rd.has(i)?i:U(i),e.attrs[i])}function rm(t,e,i){let n=rl(t,e,i);for(let i in t)(O(t[i])||O(e[i]))&&(n[-1!==m.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let rf=["x","y","width","height","cx","cy","r"],rg={useVisualState:ro({scrapeMotionValuesFromProps:rm,createRenderState:rr,onUpdate:({props:t,prevProps:e,current:i,renderState:n,latestValues:s})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in s)if(f.has(t)){r=!0;break}}if(!r)return;let a=!e;if(e)for(let i=0;i<rf.length;i++){let n=rf[i];t[n]!==e[n]&&(a=!0)}a&&S.read(()=>{ru(i,n),S.render(()=>{rs(n,s,ra(i.tagName),t.transformTemplate),rp(i,n)})})}})},rv={current:null},ry={current:!1},rx=[...eh,tP,tV],rb=t=>rx.find(el(t)),rw=new WeakMap,rP=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rT{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=t8,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=D.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,S.render(this.render,!1,!0))};let{latestValues:o,renderState:l,onUpdate:h}=r;this.onUpdate=h,this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=sX(e),this.isVariantNode=sq(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==o[t]&&O(e)&&e.set(o[t],!1)}}mount(t){this.current=t,rw.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ry.current||function(){if(ry.current=!0,sH)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rv.current=t.matches;t.addListener(e),e()}else rv.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),A(this.notifyUpdate),A(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=f.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&S.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sG){let e=sG[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iK()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rP.length;e++){let i=rP[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if(O(s))t.addValue(n,s);else if(O(r))t.addValue(n,I(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,I(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=I(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(t7(i)||tt(i))?i=parseFloat(i):!rb(i)&&tV.test(e)&&(i=tU(t,e)),this.setBaseTarget(t,O(i)?i.get():i)),O(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=d(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||O(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new R),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rM extends rT{constructor(){super(...arguments),this.KeyframeResolver=ec}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;O(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class rS extends rM{constructor(){super(...arguments),this.type="html",this.renderInstance=rc}readValueFromInstance(t,e){if(f.has(e))return tZ(t,e);{let i=window.getComputedStyle(t),n=(ee(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i9(t,e)}build(t,e,i){s5(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return rl(t,e,i)}}class rA extends rM{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iK,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&ru(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(f.has(e)){let t=tN(e);return t&&t.default||0}return e=rd.has(e)?e:U(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rm(t,e,i)}onBindTransform(){this.current&&!this.renderState.dimensions&&S.postRender(this.updateDimensions)}build(t,e,i){rs(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){rp(t,e,i,n)}mount(t){this.isSVGTag=ra(t.tagName),super.mount(t)}}let rk=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((s={animation:{Feature:iV},exit:{Feature:iR},inView:{Feature:sz},tap:{Feature:sF},focus:{Feature:sA},hover:{Feature:sS},pan:{Feature:ng},drag:{Feature:nm,ProjectionNode:sw,MeasureLayout:nA},layout:{ProjectionNode:sw,MeasureLayout:nA}},r=(t,e)=>rt(t)?new rA(e):new rS(e,{allowProjection:t!==l.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:n,Component:s}){function r(t,r){var o,h,u;let c,d={...(0,l.useContext)(sW),...t,layoutId:function({layoutId:t}){let e=(0,l.useContext)(ny).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:p}=d,m=function(t){let{initial:e,animate:i}=function(t,e){if(sX(t)){let{initial:e,animate:i}=t;return{initial:!1===e||iP(e)?e:void 0,animate:iP(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,l.useContext)(sY));return(0,l.useMemo)(()=>({initial:e,animate:i}),[s_(e),s_(i)])}(t),f=n(t,p);if(!p&&sH){h=0,u=0,(0,l.useContext)(s$).strict;let t=function(t){let{drag:e,layout:i}=sG;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);c=t.MeasureLayout,m.visualElement=function(t,e,i,n,s){let{visualElement:r}=(0,l.useContext)(sY),a=(0,l.useContext)(s$),o=(0,l.useContext)(nv),h=(0,l.useContext)(sW).reducedMotion,u=(0,l.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:h}));let c=u.current,d=(0,l.useContext)(nx);c&&!c.projection&&s&&("html"===c.type||"svg"===c.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&i7(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,s,d);let p=(0,l.useRef)(!1);(0,l.useInsertionEffect)(()=>{c&&p.current&&c.update(i,o)});let m=i[z],f=(0,l.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return sJ(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),nM.render(c.render),f.current&&c.animationState&&c.animationState.animateChanges())}),(0,l.useEffect)(()=>{c&&(!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),c}(s,f,d,e,t.ProjectionNode)}return(0,a.jsxs)(sY.Provider,{value:m,children:[c&&m.visualElement?(0,a.jsx)(c,{visualElement:m.visualElement,...d}):null,i(s,t,(o=m.visualElement,(0,l.useCallback)(t=>{t&&f.onMount&&f.onMount(t),o&&(t?o.mount(t):o.unmount()),r&&("function"==typeof r?r(t):i7(r)&&(r.current=t))},[o])),f,p,m.visualElement)]})}t&&function(t){for(let e in t)sG[e]={...sG[e],...t[e]}}(t),r.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;let o=(0,l.forwardRef)(r);return o[sZ]=s,o}({...rt(t)?rg:rh,preloadedFeatures:s,useRender:function(t=!1){return(e,i,n,{latestValues:s},r)=>{let a=(rt(e)?function(t,e,i,n){let s=(0,l.useMemo)(()=>{let i=rr();return rs(i,e,ra(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};s4(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return s4(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,l.useMemo)(()=>{let i=s3();return s5(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,s,r,e),o=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(s8(s)||!0===i&&s9(s)||!e&&!s9(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(i,"string"==typeof e,t),h=e!==l.Fragment?{...o,...a,ref:n}:{},{children:u}=i,c=(0,l.useMemo)(()=>O(u)?u.get():u,[u]);return(0,l.createElement)(e,{...h,children:c})}}(e),createVisualElement:r,Component:t})}));var rC=i(25334),rE=i(8610);function rD(t,e){return(...t)=>{try{return e(...t)}catch{throw Error(void 0)}}}let rV=rD(0,rE.c3);rD(0,rE.kc);var rj={};!function t(e,i,n,s){var r,a,o,l,h,u,c,d,p,m,f,g=!!(e.Worker&&e.Blob&&e.Promise&&e.OffscreenCanvas&&e.OffscreenCanvasRenderingContext2D&&e.HTMLCanvasElement&&e.HTMLCanvasElement.prototype.transferControlToOffscreen&&e.URL&&e.URL.createObjectURL),v="function"==typeof Path2D&&"function"==typeof DOMMatrix;function y(){}function x(t){var n=i.exports.Promise,s=void 0!==n?n:e.Promise;return"function"==typeof s?new s(t):(t(y,y),null)}var b=(r=function(){if(!e.OffscreenCanvas)return!1;var t=new OffscreenCanvas(1,1),i=t.getContext("2d");i.fillRect(0,0,1,1);var n=t.transferToImageBitmap();try{i.createPattern(n,"no-repeat")}catch(t){return!1}return!0}(),a=new Map,{transform:function(t){if(r)return t;if(a.has(t))return a.get(t);var e=new OffscreenCanvas(t.width,t.height);return e.getContext("2d").drawImage(t,0,0),a.set(t,e),e},clear:function(){a.clear()}}),w=(h=Math.floor(1e3/60),u={},c=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(o=function(t){var e=Math.random();return u[e]=requestAnimationFrame(function i(n){c===n||c+h-1<n?(c=n,delete u[e],t()):u[e]=requestAnimationFrame(i)}),e},l=function(t){u[t]&&cancelAnimationFrame(u[t])}):(o=function(t){return setTimeout(t,h)},l=function(t){return clearTimeout(t)}),{frame:o,cancel:l}),P=(m={},function(){if(d)return d;if(!n&&g){var e=["var CONFETTI, SIZE = {}, module = {};","("+t.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{d=new Worker(URL.createObjectURL(new Blob([e])))}catch(t){return"function"==typeof console.warn&&console.warn("\uD83C\uDF8A Could not load worker",t),null}var i=d;function s(t,e){i.postMessage({options:t||{},callback:e})}i.init=function(t){var e=t.transferControlToOffscreen();i.postMessage({canvas:e},[e])},i.fire=function(t,e,n){if(p)return s(t,null),p;var r=Math.random().toString(36).slice(2);return p=x(function(e){function a(t){t.data.callback===r&&(delete m[r],i.removeEventListener("message",a),p=null,b.clear(),n(),e())}i.addEventListener("message",a),s(t,r),m[r]=a.bind(null,{data:{callback:r}})})},i.reset=function(){for(var t in i.postMessage({reset:!0}),m)m[t](),delete m[t]}}return d}),T={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function M(t,e,i){var n,s;return s=t&&null!=t[e]?t[e]:T[e],i?i(s):s}function S(t){return t<0?0:Math.floor(t)}function A(t){return parseInt(t,16)}function k(t){return t.map(C)}function C(t){var e=String(t).replace(/[^0-9a-f]/gi,"");return e.length<6&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),{r:A(e.substring(0,2)),g:A(e.substring(2,4)),b:A(e.substring(4,6))}}function E(t){t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight}function D(t){var e=t.getBoundingClientRect();t.width=e.width,t.height=e.height}function V(t,i){var r,a=!t,o=!!M(i||{},"resize"),l=!1,h=M(i,"disableForReducedMotion",Boolean),u=g&&M(i||{},"useWorker")?P():null,c=a?E:D,d=!!t&&!!u&&!!t.__confetti_initialized,p="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function m(i){var m,f=h||M(i,"disableForReducedMotion",Boolean),g=M(i,"zIndex",Number);if(f&&p)return x(function(t){t()});a&&r?t=r.canvas:a&&!t&&((m=document.createElement("canvas")).style.position="fixed",m.style.top="0px",m.style.left="0px",m.style.pointerEvents="none",m.style.zIndex=g,t=m,document.body.appendChild(t)),o&&!d&&c(t);var y={width:t.width,height:t.height};function P(){if(u){var e={getBoundingClientRect:function(){if(!a)return t.getBoundingClientRect()}};c(e),u.postMessage({resize:{width:e.width,height:e.height}});return}y.width=y.height=null}function T(){r=null,o&&(l=!1,e.removeEventListener("resize",P)),a&&t&&(document.body.contains(t)&&document.body.removeChild(t),t=null,d=!1)}return(u&&!d&&u.init(t),d=!0,u&&(t.__confetti_initialized=!0),o&&!l&&(l=!0,e.addEventListener("resize",P,!1)),u)?u.fire(i,y,T):function(e,i,a){for(var o,l,h,u,d,p,m,f=M(e,"particleCount",S),g=M(e,"angle",Number),y=M(e,"spread",Number),P=M(e,"startVelocity",Number),T=M(e,"decay",Number),A=M(e,"gravity",Number),C=M(e,"drift",Number),E=M(e,"colors",k),D=M(e,"ticks",Number),V=M(e,"shapes"),j=M(e,"scalar"),R=!!M(e,"flat"),L=((o=M(e,"origin",Object)).x=M(o,"x",Number),o.y=M(o,"y",Number),o),F=f,B=[],I=t.width*L.x,O=t.height*L.y;F--;)B.push(function(t){var e=t.angle*(Math.PI/180),i=t.spread*(Math.PI/180);return{x:t.x,y:t.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*t.startVelocity+Math.random()*t.startVelocity,angle2D:-e+(.5*i-Math.random()*i),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:t.color,shape:t.shape,tick:0,totalTicks:t.ticks,decay:t.decay,drift:t.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*t.gravity,ovalScalar:.6,scalar:t.scalar,flat:t.flat}}({x:I,y:O,angle:g,spread:y,startVelocity:P,color:E[F%E.length],shape:V[Math.floor(Math.random()*(V.length-0))+0],ticks:D,decay:T,gravity:A,drift:C,scalar:j,flat:R}));return r?r.addFettis(B):(l=t,d=B.slice(),p=l.getContext("2d"),m=x(function(t){function e(){h=u=null,p.clearRect(0,0,i.width,i.height),b.clear(),a(),t()}h=w.frame(function t(){n&&(i.width!==s.width||i.height!==s.height)&&(i.width=l.width=s.width,i.height=l.height=s.height),i.width||i.height||(c(l),i.width=l.width,i.height=l.height),p.clearRect(0,0,i.width,i.height),(d=d.filter(function(t){return function(t,e){e.x+=Math.cos(e.angle2D)*e.velocity+e.drift,e.y+=Math.sin(e.angle2D)*e.velocity+e.gravity,e.velocity*=e.decay,e.flat?(e.wobble=0,e.wobbleX=e.x+10*e.scalar,e.wobbleY=e.y+10*e.scalar,e.tiltSin=0,e.tiltCos=0,e.random=1):(e.wobble+=e.wobbleSpeed,e.wobbleX=e.x+10*e.scalar*Math.cos(e.wobble),e.wobbleY=e.y+10*e.scalar*Math.sin(e.wobble),e.tiltAngle+=.1,e.tiltSin=Math.sin(e.tiltAngle),e.tiltCos=Math.cos(e.tiltAngle),e.random=Math.random()+2);var i,n,s,r,a,o,l,h,u,c,d,p,m,f,g,y,x=e.tick++/e.totalTicks,w=e.x+e.random*e.tiltCos,P=e.y+e.random*e.tiltSin,T=e.wobbleX+e.random*e.tiltCos,M=e.wobbleY+e.random*e.tiltSin;if(t.fillStyle="rgba("+e.color.r+", "+e.color.g+", "+e.color.b+", "+(1-x)+")",t.beginPath(),v&&"path"===e.shape.type&&"string"==typeof e.shape.path&&Array.isArray(e.shape.matrix)){t.fill((i=e.shape.path,n=e.shape.matrix,s=e.x,r=e.y,a=.1*Math.abs(T-w),o=.1*Math.abs(M-P),l=Math.PI/10*e.wobble,h=new Path2D(i),(u=new Path2D).addPath(h,new DOMMatrix(n)),(c=new Path2D).addPath(u,new DOMMatrix([Math.cos(l)*a,Math.sin(l)*a,-Math.sin(l)*o,Math.cos(l)*o,s,r])),c))}else if("bitmap"===e.shape.type){var S=Math.PI/10*e.wobble,A=.1*Math.abs(T-w),k=.1*Math.abs(M-P),C=e.shape.bitmap.width*e.scalar,E=e.shape.bitmap.height*e.scalar,D=new DOMMatrix([Math.cos(S)*A,Math.sin(S)*A,-Math.sin(S)*k,Math.cos(S)*k,e.x,e.y]);D.multiplySelf(new DOMMatrix(e.shape.matrix));var V=t.createPattern(b.transform(e.shape.bitmap),"no-repeat");V.setTransform(D),t.globalAlpha=1-x,t.fillStyle=V,t.fillRect(e.x-C/2,e.y-E/2,C,E),t.globalAlpha=1}else if("circle"===e.shape)t.ellipse?t.ellipse(e.x,e.y,Math.abs(T-w)*e.ovalScalar,Math.abs(M-P)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI):(d=e.x,p=e.y,m=Math.abs(T-w)*e.ovalScalar,f=Math.abs(M-P)*e.ovalScalar,g=Math.PI/10*e.wobble,y=2*Math.PI,t.save(),t.translate(d,p),t.rotate(g),t.scale(m,f),t.arc(0,0,1,0,y,void 0),t.restore());else if("star"===e.shape)for(var j=Math.PI/2*3,R=4*e.scalar,L=8*e.scalar,F=e.x,B=e.y,I=5,O=Math.PI/5;I--;)F=e.x+Math.cos(j)*L,B=e.y+Math.sin(j)*L,t.lineTo(F,B),j+=O,F=e.x+Math.cos(j)*R,B=e.y+Math.sin(j)*R,t.lineTo(F,B),j+=O;else t.moveTo(Math.floor(e.x),Math.floor(e.y)),t.lineTo(Math.floor(e.wobbleX),Math.floor(P)),t.lineTo(Math.floor(T),Math.floor(M)),t.lineTo(Math.floor(w),Math.floor(e.wobbleY));return t.closePath(),t.fill(),e.tick<e.totalTicks}(p,t)})).length?h=w.frame(t):e()}),u=e}),(r={addFettis:function(t){return d=d.concat(t),m},canvas:l,promise:m,reset:function(){h&&w.cancel(h),u&&u()}}).promise)}(i,y,T)}return m.reset=function(){u&&u.reset(),r&&r.reset()},m}function j(){return f||(f=V(null,{useWorker:!0,resize:!0})),f}i.exports=function(){return j().apply(this,arguments)},i.exports.reset=function(){j().reset()},i.exports.create=V,i.exports.shapeFromPath=function(t){if(!v)throw Error("path confetti are not supported in this browser");"string"==typeof t?n=t:(n=t.path,s=t.matrix);var e=new Path2D(n),i=document.createElement("canvas").getContext("2d");if(!s){for(var n,s,r,a,o=1e3,l=1e3,h=0,u=0,c=0;c<1e3;c+=2)for(var d=0;d<1e3;d+=2)i.isPointInPath(e,c,d,"nonzero")&&(o=Math.min(o,c),l=Math.min(l,d),h=Math.max(h,c),u=Math.max(u,d));r=h-o;var p=Math.min(10/r,10/(a=u-l));s=[p,0,0,p,-Math.round(r/2+o)*p,-Math.round(a/2+l)*p]}return{type:"path",path:n,matrix:s}},i.exports.shapeFromText=function(t){var e,i=1,n="#000000",s='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"==typeof t?e=t:(e=t.text,i="scalar"in t?t.scalar:i,s="fontFamily"in t?t.fontFamily:s,n="color"in t?t.color:n);var r=10*i,a=""+r+"px "+s,o=new OffscreenCanvas(r,r),l=o.getContext("2d");l.font=a;var h=l.measureText(e),u=Math.ceil(h.actualBoundingBoxRight+h.actualBoundingBoxLeft),c=Math.ceil(h.actualBoundingBoxAscent+h.actualBoundingBoxDescent),d=h.actualBoundingBoxLeft+2,p=h.actualBoundingBoxAscent+2;u+=4,c+=4,(l=(o=new OffscreenCanvas(u,c)).getContext("2d")).font=a,l.fillStyle=n,l.fillText(e,d,p);var m=1/i;return{type:"bitmap",bitmap:o.transferToImageBitmap(),matrix:[m,0,0,m,-u*m/2,-c*m/2]}}}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),rj,!1);let rR=rj.exports;rj.exports.create;var rL=i(8730),rF=i(24224),rB=i(49384),rI=i(82348);function rO(...t){return(0,rI.QP)((0,rB.$)(t))}let rN=(0,rF.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function rU({className:t,variant:e,size:i,asChild:n=!1,...s}){let r=n?rL.DX:"button";return(0,a.jsx)(r,{"data-slot":"button",className:rO(rN({variant:e,size:i,className:t})),...s})}var rz=i(16189),r$=h["use".trim()],rW=i(65835),rY=i(39130),rX=i(11310),rq=i(99128);let{Link:r_,redirect:rH,usePathname:rK,useRouter:rG,getPathname:rZ}=function(t){let{Link:e,config:i,getPathname:n,...s}=function(t,e){var i,n,s;let r={...i=e||{},localePrefix:"object"==typeof(s=i.localePrefix)?s:{mode:s||"always"},localeCookie:!!((n=i.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof n&&n},localeDetection:i.localeDetection??!0,alternateLinks:i.alternateLinks??!0},o=r.pathnames,h=(0,l.forwardRef)(function({href:e,locale:i,...n},s){let l,h;"object"==typeof e?(l=e.pathname,h=e.params):l=e;let c=(0,rW._x)(e),d=t(),p=(0,rW.yL)(d)?r$(d):d,m=c?u({locale:i||p,href:null==o?l:{pathname:l,params:h}},null!=i||void 0):l;return(0,a.jsx)(rY.default,{ref:s,href:"object"==typeof e?{...e,pathname:m}:m,locale:i,localeCookie:r.localeCookie,...n})});function u(t,e){let i,{href:n,locale:s}=t;return null==o?"object"==typeof n?(i=n.pathname,n.query&&(i+=(0,rX.Zn)(n.query))):i=n:i=(0,rX.FP)({locale:s,...(0,rX.TK)(n),pathnames:r.pathnames}),(0,rX.x3)(i,s,r,e)}function c(t){return function(e,...i){return t(u(e),...i)}}return{config:r,Link:h,redirect:c(rz.redirect),permanentRedirect:c(rz.permanentRedirect),getPathname:u}}(rE.Ym,t);return{...s,Link:e,usePathname:function(){let t=function(t){let e=(0,rz.usePathname)(),i=(0,rE.Ym)();return(0,l.useMemo)(()=>{if(!e)return e;let n=e,s=(0,rW.XP)(i,t.localePrefix);if((0,rW.wO)(s,e))n=(0,rW.MY)(e,s);else if("as-needed"===t.localePrefix.mode&&t.localePrefix.prefixes){let t=(0,rW.bL)(i);(0,rW.wO)(t,e)&&(n=(0,rW.MY)(e,t))}return n},[t.localePrefix,i,e])}(i),e=(0,rE.Ym)();return(0,l.useMemo)(()=>t&&i.pathnames?(0,rX.aM)(e,t,i.pathnames):t,[e,t])},useRouter:function(){let t=(0,rz.useRouter)(),e=(0,rE.Ym)(),s=(0,rz.usePathname)();return(0,l.useMemo)(()=>{function r(t){return function(r,a){let{locale:o,...l}=a||{},h=[n({href:r,locale:o||e})];Object.keys(l).length>0&&h.push(l),t(...h),(0,rq.A)(i.localeCookie,s,e,o)}}return{...t,push:r(t.push),replace:r(t.replace),prefetch:r(t.prefetch)}},[e,s,t])},getPathname:n}}({locales:["en","cn"],defaultLocale:"en"}),rJ=["titleLowlantency","titleMultimodal","titleEdgeCloud"],rQ={visible:{y:0,opacity:1},hidden:t=>({y:t>0?-150:150,opacity:0})},r0=t=>{let e={origin:{x:t.clientX/window.innerWidth,y:(t.clientY+50)/window.innerHeight},scalar:.6};function i(t,n={}){rR({...e,...n,particleCount:Math.floor(88*t)})}i(.25,{spread:20,startVelocity:20}),i(.2,{spread:35,startVelocity:15}),i(.35,{spread:30,decay:.91,scalar:.4,startVelocity:15}),i(.1,{spread:40,startVelocity:10,decay:.92,scalar:.8}),i(.1,{spread:40,startVelocity:10})};function r1(t){let{className:e}=t,i=rV("homePage"),[n,s]=l.useState(0);return l.useEffect(()=>{let t=setTimeout(()=>{n===rJ.length-1?s(0):s(n+1)},2e3);return()=>clearTimeout(t)},[n]),(0,a.jsx)("div",{className:rO("text-foreground w-full",e),children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-8 pt-4 pb-20 lg:pt-8 lg:pb-60",children:[(0,a.jsx)("div",{children:(0,a.jsx)(rU,{variant:"secondary",size:"sm",className:"gap-2 bg-blue-600/[0.05] text-blue-600 transition-all duration-600 hover:scale-105 hover:bg-blue-600/[0.08] hover:text-blue-500 py-7 sm:py-0",asChild:!0,onClick:t=>r0(t),children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:["\uD83C\uDF89"," ",(0,a.jsx)(r_,{href:"https://github.com/ten-framework/ten-vad",className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2",children:"TEN VAD"}),(0,a.jsx)("span",{className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base",children:"and"}),(0,a.jsx)(r_,{href:"https://github.com/ten-framework/ten-turn-detection",className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2",children:"TEN Turn Detection"}),(0,a.jsx)("span",{className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base",children:"are now part of the TEN open-source ecosystem!"})]})})}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("h1",{className:"font-regular text-center text-5xl tracking-tighter md:text-7xl",children:[(0,a.jsx)("span",{className:"text-spektr-cyan-50 font-medium",children:i("titlePrefix")}),(0,a.jsxs)("span",{className:"relative flex w-full justify-center overflow-hidden text-center leading-tight md:leading-normal",children:["\xa0",rJ.map((t,e)=>(0,a.jsx)(rk.span,{className:"absolute font-bold",initial:"hidden",animate:n===e?"visible":"hidden",variants:rQ,custom:n>e?1:-1,transition:{type:"spring",stiffness:35,duration:.5},children:i(t)},e))]}),(0,a.jsx)("span",{className:"text-spektr-cyan-50 font-medium",children:i("titleSuffix")})]}),(0,a.jsx)("p",{className:"text-muted-foreground max-w-2xl text-center text-lg leading-relaxed font-medium tracking-tight md:text-xl dark:text-gray-300",children:i("heroDescription")})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsx)(rU,{size:"lg",className:"gap-4",asChild:!0,children:(0,a.jsxs)(r_,{href:"https://agent.theten.ai",target:"_blank",children:[i("heroBtnTryTenAgent"),(0,a.jsx)(rC.A,{className:"size-4"})]})}),(0,a.jsx)(rU,{size:"lg",className:"gap-4",variant:"outline",asChild:!0,children:(0,a.jsxs)(r_,{href:"https://huggingface.co/spaces/TEN-framework/ten-agent-demo",target:"_blank",children:[i("huggingFaceSpace"),(0,a.jsx)(rC.A,{className:"size-4"})]})})]}),(0,a.jsxs)("p",{className:"text-muted-foreground/100 max-w-2xl text-center text-sm leading-relaxed font-normal tracking-tight md:text-base dark:text-gray-300",children:[i("supportedBy")," ",(0,a.jsx)(r_,{href:"https://www.agora.io/en/",target:"_blank",className:"text-spektr-cyan-100 underline decoration-gray-300 underline-offset-5 hover:text-[] hover:decoration-[#13C2FF]",children:"Agora"})]})]})})})}let r2=()=>{let{resolvedTheme:t}=(0,o.D)(),[e,i]=(0,l.useState)(!1),[n,s]=(0,l.useState)(!1),r=(0,l.useRef)(null);return((0,l.useEffect)(()=>{i(!0)},[]),(0,l.useEffect)(()=>{r.current&&(s(!1),r.current.currentTime=0,r.current.load(),r.current.play())},[t]),e)?(0,a.jsxs)("video",{ref:r,autoPlay:!0,loop:!0,muted:!0,playsInline:!0,onLoadedData:()=>s(!0),className:`absolute inset-0 z-0 h-full w-full object-cover transition-opacity duration-700 ${n?"opacity-37 dark:opacity-57":"opacity-0"}`,children:[(0,a.jsx)("source",{src:"dark"===t?"https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg-dark.mp4":"https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg2.mp4",type:"video/mp4"}),"Your browser does not support the video tag."]}):null};function r5(){return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"relative flex h-[calc(100dvh-56px)] flex-1 flex-col justify-center overflow-hidden text-center",children:[(0,a.jsx)(r2,{}),(0,a.jsx)(r1,{className:"relative z-10"})]})})}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63488:(t,e,i)=>{"use strict";i.d(e,{P:()=>a,R:()=>r});var n=i(55946);let s=["en","cn"],r={defaultLanguage:"en",languages:s,hideLocale:"default-locale"},a=(0,n.A)({locales:s,defaultLocale:"en"})},74591:(t,e,i)=>{Promise.resolve().then(i.bind(i,40851)),Promise.resolve().then(i.bind(i,78655)),Promise.resolve().then(i.bind(i,69767)),Promise.resolve().then(i.bind(i,78941)),Promise.resolve().then(i.bind(i,36163)),Promise.resolve().then(i.bind(i,24274)),Promise.resolve().then(i.bind(i,3231)),Promise.resolve().then(i.bind(i,48615)),Promise.resolve().then(i.bind(i,28966))},79551:t=>{"use strict";t.exports=require("url")},80621:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>n});let n=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\page.tsx","default")},93846:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>a});var n=i(37413),s=i(29425),r=i(963);async function a({params:t,children:e}){let{lang:i}=await t;return(0,n.jsx)(s.m,{...(0,r.k)(i),className:"pt-0",children:e})}}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),n=e.X(0,[447,825,189,453,569,958],()=>i(11462));module.exports=n})();