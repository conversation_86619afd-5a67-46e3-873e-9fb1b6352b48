/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[lang]/(home)/page";
exports.ids = ["app/[lang]/(home)/page"];
exports.modules = {

/***/ "(rsc)/./app/[lang]/(home)/layout.tsx":
/*!**************************************!*\
  !*** ./app/[lang]/(home)/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fumadocs_ui_layouts_home__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fumadocs-ui/layouts/home */ \"(rsc)/./node_modules/fumadocs-ui/dist/layouts/home.js\");\n/* harmony import */ var _app_layout_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/layout.config */ \"(rsc)/./app/layout.config.tsx\");\n\n\n\nasync function Layout({ params, children }) {\n    const { lang } = await params;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_layouts_home__WEBPACK_IMPORTED_MODULE_1__.HomeLayout, {\n        ...(0,_app_layout_config__WEBPACK_IMPORTED_MODULE_2__.baseOptions)(lang),\n        className: \"pt-0\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xhbmddLyhob21lKS9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUNxRDtBQUNKO0FBRWxDLGVBQWVFLE9BQU8sRUFDbkNDLE1BQU0sRUFDTkMsUUFBUSxFQUlUO0lBQ0MsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBRyxNQUFNRjtJQUV2QixxQkFDRSw4REFBQ0gsZ0VBQVVBO1FBQUUsR0FBR0MsK0RBQVdBLENBQUNJLEtBQUs7UUFBRUMsV0FBVTtrQkFDMUNGOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXGFwcFxcW2xhbmddXFwoaG9tZSlcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEhvbWVMYXlvdXQgfSBmcm9tICdmdW1hZG9jcy11aS9sYXlvdXRzL2hvbWUnXG5pbXBvcnQgeyBiYXNlT3B0aW9ucyB9IGZyb20gJ0AvYXBwL2xheW91dC5jb25maWcnXG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIExheW91dCh7XG4gIHBhcmFtcyxcbiAgY2hpbGRyZW4sXG59OiB7XG4gIHBhcmFtczogUHJvbWlzZTx7IGxhbmc6IHN0cmluZyB9PlxuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59KSB7XG4gIGNvbnN0IHsgbGFuZyB9ID0gYXdhaXQgcGFyYW1zXG5cbiAgcmV0dXJuIChcbiAgICA8SG9tZUxheW91dCB7Li4uYmFzZU9wdGlvbnMobGFuZyl9IGNsYXNzTmFtZT1cInB0LTBcIj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0hvbWVMYXlvdXQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJIb21lTGF5b3V0IiwiYmFzZU9wdGlvbnMiLCJMYXlvdXQiLCJwYXJhbXMiLCJjaGlsZHJlbiIsImxhbmciLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/[lang]/(home)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[lang]/(home)/page.tsx":
/*!************************************!*\
  !*** ./app/[lang]/(home)/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/[lang]/layout.tsx":
/*!*******************************!*\
  !*** ./app/[lang]/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fumadocs-ui/provider */ \"(rsc)/./node_modules/fumadocs-ui/dist/provider/index.js\");\n/* harmony import */ var next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\[lang]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\[lang]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./lib/i18n.ts\");\n/* harmony import */ var _messages_cn_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/messages/cn.json */ \"(rsc)/./messages/cn.json\");\n/* harmony import */ var _messages_en_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/messages/en.json */ \"(rsc)/./messages/en.json\");\n/* harmony import */ var _app_metadata_config__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/metadata.config */ \"(rsc)/./app/metadata.config.ts\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _global_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../global.css */ \"(rsc)/./app/global.css\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst cn = _messages_cn_json__WEBPACK_IMPORTED_MODULE_4__.fuma;\nconst en = _messages_en_json__WEBPACK_IMPORTED_MODULE_5__.fuma;\nconst locales = [\n    {\n        name: 'English',\n        locale: 'en'\n    },\n    {\n        name: '中文',\n        locale: 'cn'\n    }\n];\nasync function generateMetadata({ params: paramsPromise }) {\n    const { lang } = await paramsPromise;\n    return (0,_app_metadata_config__WEBPACK_IMPORTED_MODULE_6__.generateSiteMetadata)({\n        lang\n    });\n}\nasync function Layout({ params, children }) {\n    const { lang } = await params;\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n        locale: lang\n    });\n    if (!(0,next_intl__WEBPACK_IMPORTED_MODULE_10__.hasLocale)(_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.nextIntlRouting.locales, lang)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.notFound)();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: lang,\n        className: (next_font_google_target_css_path_app_lang_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_11___default().className),\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Audiowide&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"flex min-h-screen flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    locale: lang,\n                    messages: messages,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_1__.RootProvider, {\n                        i18n: {\n                            locale: lang,\n                            locales: locales,\n                            translations: {\n                                cn,\n                                en\n                            }[lang]\n                        },\n                        children: [\n                            children,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                src: \"/analytics/scarf-analytics.js\",\n                                strategy: \"afterInteractive\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[lang]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/global.css":
/*!************************!*\
  !*** ./app/global.css ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e2435ebca42e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFsLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXGFwcFxcZ2xvYmFsLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUyNDM1ZWJjYTQyZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/global.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.config.tsx":
/*!*******************************!*\
  !*** ./app/layout.config.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseOptions: () => (/* binding */ baseOptions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_github_star_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/github-star-button */ \"(rsc)/./components/ui/github-star-button.tsx\");\n/* harmony import */ var _components_ui_logo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/logo */ \"(rsc)/./components/ui/logo.tsx\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./lib/i18n.ts\");\n/* harmony import */ var _messages_en_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/messages/en.json */ \"(rsc)/./messages/en.json\");\n/* harmony import */ var _messages_cn_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/messages/cn.json */ \"(rsc)/./messages/cn.json\");\n\n\n\n\n\n\nconst getMessages = (locale)=>{\n    switch(locale){\n        case 'cn':\n            return _messages_cn_json__WEBPACK_IMPORTED_MODULE_5__;\n        case 'en':\n        default:\n            return _messages_en_json__WEBPACK_IMPORTED_MODULE_4__;\n    }\n};\nfunction baseOptions(locale) {\n    const messages = getMessages(locale);\n    return {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n,\n        nav: {\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mt-0.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_logo__WEBPACK_IMPORTED_MODULE_2__.Logo, {\n                    height: 33,\n                    width: 66\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\layout.config.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\layout.config.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this),\n            url: `/${locale}`\n        },\n        links: [\n            {\n                text: messages.nav.docs,\n                url: `/${locale}/docs`,\n                active: 'nested-url'\n            },\n            {\n                text: messages.nav.blog,\n                url: `/${locale}/blog`,\n                active: 'nested-url'\n            },\n            {\n                text: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_github_star_button__WEBPACK_IMPORTED_MODULE_1__.GitHubStarButton, {\n                    repo: \"TEN-framework/ten-framework\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\layout.config.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 15\n                }, this),\n                url: 'https://github.com/TEN-framework/ten-framework',\n                active: 'none'\n            }\n        ],\n        themeSwitch: {\n            enabled: true,\n            mode: 'light-dark-system'\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.config.tsx\n");

/***/ }),

/***/ "(rsc)/./app/metadata.config.ts":
/*!********************************!*\
  !*** ./app/metadata.config.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_DESCRIPTION: () => (/* binding */ DEFAULT_DESCRIPTION),\n/* harmony export */   DEFAULT_TITLE: () => (/* binding */ DEFAULT_TITLE),\n/* harmony export */   KEYWORDS: () => (/* binding */ KEYWORDS),\n/* harmony export */   SITE_URL: () => (/* binding */ SITE_URL),\n/* harmony export */   SOCIAL_HANDLES: () => (/* binding */ SOCIAL_HANDLES),\n/* harmony export */   generateSiteMetadata: () => (/* binding */ generateSiteMetadata)\n/* harmony export */ });\nconst SITE_URL = 'https://theten.ai';\nconst DEFAULT_TITLE = 'Open-source framework for all AI agents.';\nconst DEFAULT_DESCRIPTION = 'TEN is an open-source framework designed for building multimodal conversational AI';\nconst KEYWORDS = [\n    'AI Framework',\n    'Conversational AI',\n    'Multimodal AI',\n    'Real-time AI',\n    'Voice AI',\n    'AI Agents'\n];\nconst SOCIAL_HANDLES = {\n    twitter: '@TenFramework'\n};\nfunction generateSiteMetadata({ title = DEFAULT_TITLE, description = DEFAULT_DESCRIPTION, lang }) {\n    return {\n        metadataBase: new URL(SITE_URL),\n        title: {\n            template: '%s | TEN Framework',\n            default: title\n        },\n        description,\n        keywords: KEYWORDS,\n        authors: [\n            {\n                name: 'TEN Framework Team'\n            }\n        ],\n        openGraph: {\n            title,\n            description,\n            url: SITE_URL,\n            siteName: 'TEN Framework',\n            locale: lang,\n            type: 'website'\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title,\n            description,\n            creator: SOCIAL_HANDLES.twitter\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-video-preview': -1,\n                'max-image-preview': 'large',\n                'max-snippet': -1\n            }\n        },\n        alternates: {\n            canonical: `${SITE_URL}/${lang}`,\n            languages: {\n                'en-US': `${SITE_URL}/en`,\n                'zh-CN': `${SITE_URL}/cn`\n            }\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/metadata.config.ts\n");

/***/ }),

/***/ "(rsc)/./components/ui/github-star-button.tsx":
/*!**********************************************!*\
  !*** ./components/ui/github-star-button.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GitHubStarButton: () => (/* binding */ GitHubStarButton)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const GitHubStarButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call GitHubStarButton() from the server but GitHubStarButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\github-star-button.tsx",
"GitHubStarButton",
);

/***/ }),

/***/ "(rsc)/./components/ui/logo.tsx":
/*!********************************!*\
  !*** ./components/ui/logo.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Logo: () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Logo = ({ height = 32, width })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        id: \"ten-logo\",\n        \"data-name\": \"ten-logo\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 536.03 168.7\",\n        style: {\n            height: `${height}px`,\n            width: width ? `${width}px` : 'auto'\n        },\n        className: \"w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\n          @import url('https://fonts.googleapis.com/css2?family=Audiowide&display=swap');\n          .cls-1 {\n            font-family: 'Audiowide', cursive;\n            font-size: 144px;\n            fill: currentColor;\n            dominant-baseline: middle;\n            text-anchor: start;\n          }\n        `\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                lineNumber: 18,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                id: \"logo-container\",\n                \"data-name\": \"logo-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        id: \"logo-shape\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                            points: \"153.1 3.22 153.3 3.22 150.5 .72 147.3 .72 147.3 .72 34.2 .52 33.4 2.82 0 99.72 28 130.42 63.5 130.32 98.9 27.42 61.5 27.22 36.6 2.82 149.9 3.22 177.5 29.82 133.8 28.22 98.6 130.32 146.2 129.82 180.6 29.92 153.1 3.22\",\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                        className: \"cls-1\",\n                        x: \"200\",\n                        y: \"85\",\n                        children: \"TEN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\logo.tsx\",\n        lineNumber: 7,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/logo.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   nextIntlRouting: () => (/* binding */ nextIntlRouting)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\");\n\nconst LOCALES = [\n    'en',\n    'cn'\n];\nconst DEFAULT_LOCALE = 'en';\nconst i18n = {\n    defaultLanguage: DEFAULT_LOCALE,\n    languages: LOCALES,\n    hideLocale: 'default-locale'\n};\nconst nextIntlRouting = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: LOCALES,\n    // Used when no locale matches\n    defaultLocale: DEFAULT_LOCALE\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDaUQ7QUFFakQsTUFBTUMsVUFBVTtJQUFDO0lBQU07Q0FBSztBQUM1QixNQUFNQyxpQkFBaUI7QUFFaEIsTUFBTUMsT0FBbUI7SUFDOUJDLGlCQUFpQkY7SUFDakJHLFdBQVdKO0lBQ1hLLFlBQVk7QUFDZCxFQUFDO0FBRU0sTUFBTUMsa0JBQWtCUCw2REFBYUEsQ0FBQztJQUMzQywyQ0FBMkM7SUFDM0NRLFNBQVNQO0lBQ1QsOEJBQThCO0lBQzlCUSxlQUFlUDtBQUNqQixHQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbGliXFxpMThuLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgSTE4bkNvbmZpZyB9IGZyb20gJ2Z1bWFkb2NzLWNvcmUvaTE4bidcbmltcG9ydCB7IGRlZmluZVJvdXRpbmcgfSBmcm9tICduZXh0LWludGwvcm91dGluZydcblxuY29uc3QgTE9DQUxFUyA9IFsnZW4nLCAnY24nXVxuY29uc3QgREVGQVVMVF9MT0NBTEUgPSAnZW4nXG5cbmV4cG9ydCBjb25zdCBpMThuOiBJMThuQ29uZmlnID0ge1xuICBkZWZhdWx0TGFuZ3VhZ2U6IERFRkFVTFRfTE9DQUxFLFxuICBsYW5ndWFnZXM6IExPQ0FMRVMsXG4gIGhpZGVMb2NhbGU6ICdkZWZhdWx0LWxvY2FsZScsXG59XG5cbmV4cG9ydCBjb25zdCBuZXh0SW50bFJvdXRpbmcgPSBkZWZpbmVSb3V0aW5nKHtcbiAgLy8gQSBsaXN0IG9mIGFsbCBsb2NhbGVzIHRoYXQgYXJlIHN1cHBvcnRlZFxuICBsb2NhbGVzOiBMT0NBTEVTLFxuICAvLyBVc2VkIHdoZW4gbm8gbG9jYWxlIG1hdGNoZXNcbiAgZGVmYXVsdExvY2FsZTogREVGQVVMVF9MT0NBTEUsXG59KVxuIl0sIm5hbWVzIjpbImRlZmluZVJvdXRpbmciLCJMT0NBTEVTIiwiREVGQVVMVF9MT0NBTEUiLCJpMThuIiwiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VzIiwiaGlkZUxvY2FsZSIsIm5leHRJbnRsUm91dGluZyIsImxvY2FsZXMiLCJkZWZhdWx0TG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./lib/next-intl-requests.ts":
/*!***********************************!*\
  !*** ./lib/next-intl-requests.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./lib/i18n.ts\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ requestLocale })=>{\n    // Typically corresponds to the `[locale]` segment\n    const requested = await requestLocale;\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.hasLocale)(_lib_i18n__WEBPACK_IMPORTED_MODULE_0__.nextIntlRouting.locales, requested) ? requested : _lib_i18n__WEBPACK_IMPORTED_MODULE_0__.nextIntlRouting.defaultLocale;\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbmV4dC1pbnRsLXJlcXVlc3RzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUQ7QUFDZDtBQUNPO0FBRTVDLGlFQUFlQSw0REFBZ0JBLENBQUMsT0FBTyxFQUFFRyxhQUFhLEVBQUU7SUFDdEQsa0RBQWtEO0lBQ2xELE1BQU1DLFlBQVksTUFBTUQ7SUFDeEIsTUFBTUUsU0FBU0osb0RBQVNBLENBQUNDLHNEQUFlQSxDQUFDSSxPQUFPLEVBQUVGLGFBQzlDQSxZQUNBRixzREFBZUEsQ0FBQ0ssYUFBYTtJQUVqQyxPQUFPO1FBQ0xGO1FBQ0FHLFVBQVUsQ0FBQyxNQUFNLHlFQUFPLEdBQWEsRUFBRUgsT0FBTyxNQUFNLEdBQUdJLE9BQU87SUFDaEU7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbGliXFxuZXh0LWludGwtcmVxdWVzdHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0UmVxdWVzdENvbmZpZyB9IGZyb20gJ25leHQtaW50bC9zZXJ2ZXInXG5pbXBvcnQgeyBoYXNMb2NhbGUgfSBmcm9tICduZXh0LWludGwnXG5pbXBvcnQgeyBuZXh0SW50bFJvdXRpbmcgfSBmcm9tICdAL2xpYi9pMThuJ1xuXG5leHBvcnQgZGVmYXVsdCBnZXRSZXF1ZXN0Q29uZmlnKGFzeW5jICh7IHJlcXVlc3RMb2NhbGUgfSkgPT4ge1xuICAvLyBUeXBpY2FsbHkgY29ycmVzcG9uZHMgdG8gdGhlIGBbbG9jYWxlXWAgc2VnbWVudFxuICBjb25zdCByZXF1ZXN0ZWQgPSBhd2FpdCByZXF1ZXN0TG9jYWxlXG4gIGNvbnN0IGxvY2FsZSA9IGhhc0xvY2FsZShuZXh0SW50bFJvdXRpbmcubG9jYWxlcywgcmVxdWVzdGVkKVxuICAgID8gcmVxdWVzdGVkXG4gICAgOiBuZXh0SW50bFJvdXRpbmcuZGVmYXVsdExvY2FsZVxuXG4gIHJldHVybiB7XG4gICAgbG9jYWxlLFxuICAgIG1lc3NhZ2VzOiAoYXdhaXQgaW1wb3J0KGAuLi9tZXNzYWdlcy8ke2xvY2FsZX0uanNvbmApKS5kZWZhdWx0LFxuICB9XG59KVxuIl0sIm5hbWVzIjpbImdldFJlcXVlc3RDb25maWciLCJoYXNMb2NhbGUiLCJuZXh0SW50bFJvdXRpbmciLCJyZXF1ZXN0TG9jYWxlIiwicmVxdWVzdGVkIiwibG9jYWxlIiwibG9jYWxlcyIsImRlZmF1bHRMb2NhbGUiLCJtZXNzYWdlcyIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/next-intl-requests.ts\n");

/***/ }),

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./cn.json": "(rsc)/./messages/cn.json",
	"./en.json": "(rsc)/./messages/en.json"
};

function webpackAsyncContext(req) {
	return Promise.resolve().then(() => {
		if(!__webpack_require__.o(map, req)) {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		}

		var id = map[req];
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./messages/cn.json":
/*!**************************!*\
  !*** ./messages/cn.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"nav":{"docs":"文档","blog":"博客"},"fuma":{"search":"搜索","searchNoResult":"未找到结果","toc":"目录","tocNoHeadings":"没有标题","lastUpdate":"最后更新","chooseLanguage":"选择语言","nextPage":"下一页","previousPage":"上一页","chooseTheme":"选择主题","editOnGithub":"在 GitHub 上编辑"},"blog":{"latestPosts":"最新博客文章","discoverLatestArticles":"发现我们关于 TEN framework、AI 开发等方面的最新文章","readMore":"阅读更多","backToBlog":"返回博客","writtenBy":"作者","publishedOn":"发布于"},"homePage":{"titlePrefix":"搭建","titleRealtime":"实时","titleMultimodal":"可定制","titleLowlantency":"低延迟","titleHighperformance":"高性能","titleEdgeCloud":"可边缘云","titleSuffix":"语音 AI Agent","readLaunchArticle":"阅读我们的博客了解更多","heroDescription":"TEN 是一个用于搭建实时多模态的对话式 AI 引擎的开源框架","heroBtnTryTenAgent":"体验 TEN Agent","heroBtnReadDoc":"文档","bannerAnnouncement":"欢迎 VAD 和 Turn Detection 加入 TEN 开源全家桶!","huggingFaceSpace":"体验语音检测和打断","supportedBy":"共同支持来自 TEN 社区"}}');

/***/ }),

/***/ "(rsc)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"nav":{"docs":"Documentation","blog":"Blog"},"fuma":{"search":"Search","searchNoResult":"No results found","toc":"Table of Contents","tocNoHeadings":"No headings found","lastUpdate":"Last Updated","chooseLanguage":"Choose Language","nextPage":"Next Page","previousPage":"Previous Page","chooseTheme":"Choose Theme","editOnGithub":"Edit on GitHub"},"blog":{"latestPosts":"Latest Blog Posts","discoverLatestArticles":"Discover our latest articles about TEN Framework, TEN Agent and Conversational AI","readMore":"Read more","backToBlog":"Back to Blog","writtenBy":"Written by","publishedOn":"Published on"},"homePage":{"titlePrefix":"Build","titleLowlantency":"Real-Time","titleMultimodal":"Customizable","titleHighperformance":"High-performance","titleEdgeCloud":"Low-Latency","titleSuffix":"Voice AI Agents","readLaunchArticle":"Read more on our blog","heroDescription":"An open-source framework designed for building conversational AI","supportedBy":"supported by the TEN community and","heroBtnTryTenAgent":"Talk to TEN Agent","huggingFaceSpace":"Try VAD and Turn Detection","heroBtnReadDoc":"Documentation","bannerAnnouncement":"are now part of the TEN open-source ecosystem!"}}');

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/layout.tsx */ \"(rsc)/./app/[lang]/layout.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/layout.tsx */ \"(rsc)/./app/[lang]/(home)/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/page.tsx */ \"(rsc)/./app/[lang]/(home)/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[lang]',\n        {\n        children: [\n        '(home)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module3, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[lang]/(home)/page\",\n        pathname: \"/[lang]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/page.tsx */ \"(rsc)/./app/[lang]/(home)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RlZXBJbnNpZ2h0JTVDJTVDRG93bmxvYWRzJTVDJTVDcG9ydGFsLW1haW4lNUMlNUNwb3J0YWwtbWFpbiU1QyU1Q2FwcCU1QyU1QyU1QmxhbmclNUQlNUMlNUMoaG9tZSklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQStIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEZWVwSW5zaWdodFxcXFxEb3dubG9hZHNcXFxccG9ydGFsLW1haW5cXFxccG9ydGFsLW1haW5cXFxcYXBwXFxcXFtsYW5nXVxcXFwoaG9tZSlcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Ccomponents%5C%5Cui%5C%5Cgithub-star-button.tsx%22%2C%22ids%22%3A%5B%22GitHubStarButton%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Ccomponents%5C%5Cui%5C%5Cgithub-star-button.tsx%22%2C%22ids%22%3A%5B%22GitHubStarButton%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/github-star-button.tsx */ \"(rsc)/./components/ui/github-star-button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-core/dist/link.js */ \"(rsc)/./node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/contexts/layout.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/contexts/layout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(rsc)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(rsc)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Ccomponents%5C%5Cui%5C%5Cgithub-star-button.tsx%22%2C%22ids%22%3A%5B%22GitHubStarButton%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/provider/index.js */ \"(rsc)/./node_modules/fumadocs-ui/dist/provider/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__ ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"2000x2000\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPW1keCZwYWdlRXh0ZW5zaW9ucz1tZCZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjIwMDB4MjAwMFwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/[lang]/(home)/_components.tsx":
/*!*******************************************!*\
  !*** ./app/[lang]/(home)/_components.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero),\n/* harmony export */   ProjectsShowcase: () => (/* binding */ ProjectsShowcase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var motion_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! motion/react */ \"(ssr)/./node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! canvas-confetti */ \"(ssr)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/next-intl-navigation */ \"(ssr)/./lib/next-intl-navigation.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants */ \"(ssr)/./constants/index.ts\");\n/* harmony import */ var _sample_projects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./sample-projects */ \"(ssr)/./app/[lang]/(home)/sample-projects.ts\");\n/* __next_internal_client_entry_do_not_use__ ProjectsShowcase,Hero auto */ \n\n\n\n\n\n\n\n\n\n\nconst TITLES = [\n    'titleLowlantency',\n    'titleMultimodal',\n    'titleEdgeCloud'\n];\nconst titleVariants = {\n    visible: {\n        y: 0,\n        opacity: 1\n    },\n    hidden: (direction)=>({\n            y: direction > 0 ? -150 : 150,\n            opacity: 0\n        })\n};\nconst createConfetti = (e)=>{\n    const count = 88;\n    const defaults = {\n        origin: {\n            x: e.clientX / window.innerWidth,\n            y: (e.clientY + 50) / window.innerHeight\n        },\n        scalar: 0.6\n    };\n    function fire(particleRatio, opts = {}) {\n        (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            ...defaults,\n            ...opts,\n            particleCount: Math.floor(count * particleRatio)\n        });\n    }\n    fire(0.25, {\n        spread: 20,\n        startVelocity: 20\n    });\n    fire(0.2, {\n        spread: 35,\n        startVelocity: 15\n    });\n    fire(0.35, {\n        spread: 30,\n        decay: 0.91,\n        scalar: 0.4,\n        startVelocity: 15\n    });\n    fire(0.1, {\n        spread: 40,\n        startVelocity: 10,\n        decay: 0.92,\n        scalar: 0.8\n    });\n    fire(0.1, {\n        spread: 40,\n        startVelocity: 10\n    });\n};\nfunction ProjectsShowcase(props) {\n    const { className } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('w-full py-20 bg-gray-50/50 dark:bg-gray-900/50', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-4xl font-bold tracking-tight text-black\",\n                            children: \"From the Community\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-black mx-auto max-w-2xl text-lg\",\n                            children: \"Discover amazing projects built with TEN Framework\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                    children: _sample_projects__WEBPACK_IMPORTED_MODULE_7__.SAMPLE_PROJECTS.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                            href: project.href,\n                            className: \"group relative overflow-hidden rounded-2xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video w-full overflow-hidden bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-full items-center justify-center text-gray-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: \"Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block rounded-full bg-blue-100 dark:bg-blue-900/30 px-3 py-1 text-xs font-medium text-blue-700 dark:text-blue-300\",\n                                                    children: project.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        project.remixes,\n                                                        \" Remixes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-2 text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                            children: project.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-4 text-sm text-gray-600 dark:text-gray-300 line-clamp-2\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                    children: project.author.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: project.author\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, project.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        size: \"lg\",\n                        className: \"gap-2\",\n                        children: [\n                            \"View All Projects\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\nfunction Hero(props) {\n    const { className } = props;\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)('homePage');\n    const [titleNumber, setTitleNumber] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"Hero.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"Hero.useEffect.timeoutId\": ()=>{\n                    if (titleNumber === TITLES.length - 1) {\n                        setTitleNumber(0);\n                    } else {\n                        setTitleNumber(titleNumber + 1);\n                    }\n                }\n            }[\"Hero.useEffect.timeoutId\"], 2000);\n            return ({\n                \"Hero.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        titleNumber\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('text-foreground w-full', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center gap-8 pt-4 pb-20 lg:pt-8 lg:pb-60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"secondary\",\n                            size: \"sm\",\n                            className: \"gap-2 bg-blue-600/[0.05] text-blue-600 transition-all duration-600 hover:scale-105 hover:bg-blue-600/[0.08] hover:text-blue-500 py-7 sm:py-0\",\n                            asChild: true,\n                            onClick: (e)=>createConfetti(e),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    \"\\uD83C\\uDF89\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                        href: _constants__WEBPACK_IMPORTED_MODULE_6__.URL_TEN_VAD,\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2\",\n                                        children: \"TEN VAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base\",\n                                        children: \"and\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                        href: _constants__WEBPACK_IMPORTED_MODULE_6__.URL_TEN_TURN_DETECTION,\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2\",\n                                        children: \"TEN Turn Detection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base\",\n                                        children: \"are now part of the TEN open-source ecosystem!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"font-regular text-center text-5xl tracking-tighter md:text-7xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-spektr-cyan-50 font-medium\",\n                                        children: t('titlePrefix')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative flex w-full justify-center overflow-hidden text-center leading-tight md:leading-normal\",\n                                        children: [\n                                            \"\\xa0\",\n                                            TITLES.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion_react__WEBPACK_IMPORTED_MODULE_10__.motion.span, {\n                                                    className: \"absolute font-bold\",\n                                                    initial: \"hidden\",\n                                                    animate: titleNumber === index ? 'visible' : 'hidden',\n                                                    variants: titleVariants,\n                                                    custom: titleNumber > index ? 1 : -1,\n                                                    transition: {\n                                                        type: 'spring',\n                                                        stiffness: 35,\n                                                        duration: 0.5\n                                                    },\n                                                    children: t(title)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-spektr-cyan-50 font-medium\",\n                                        children: t('titleSuffix')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground max-w-2xl text-center text-lg leading-relaxed font-medium tracking-tight md:text-xl dark:text-gray-300\",\n                                children: t('heroDescription')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"gap-4\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                    href: _constants__WEBPACK_IMPORTED_MODULE_6__.URL_TEN_AGENT,\n                                    target: \"_blank\",\n                                    children: [\n                                        t('heroBtnTryTenAgent'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"gap-4\",\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                    href: _constants__WEBPACK_IMPORTED_MODULE_6__.HUGGING_FACE_SPACE,\n                                    target: \"_blank\",\n                                    children: [\n                                        t('huggingFaceSpace'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground/100 max-w-2xl text-center text-sm leading-relaxed font-normal tracking-tight md:text-base dark:text-gray-300\",\n                        children: [\n                            t('supportedBy'),\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_next_intl_navigation__WEBPACK_IMPORTED_MODULE_5__.Link, {\n                                href: \"https://www.agora.io/en/\",\n                                target: \"_blank\",\n                                className: \"text-spektr-cyan-100 underline decoration-gray-300 underline-offset-5 hover:text-[] hover:decoration-[#13C2FF]\",\n                                children: \"Agora\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\_components.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[lang]/(home)/_components.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[lang]/(home)/page.tsx":
/*!************************************!*\
  !*** ./app/[lang]/(home)/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lang_home_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/[lang]/(home)/_components */ \"(ssr)/./app/[lang]/(home)/_components.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundVideo = ()=>{\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BackgroundVideo.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"BackgroundVideo.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BackgroundVideo.useEffect\": ()=>{\n            if (videoRef.current) {\n                // Reset loaded state when theme changes\n                setIsLoaded(false);\n                // Reset the video to start playing from beginning\n                videoRef.current.currentTime = 0;\n                videoRef.current.load();\n                videoRef.current.play();\n            }\n        }\n    }[\"BackgroundVideo.useEffect\"], [\n        resolvedTheme\n    ]);\n    if (!mounted) return null;\n    const videoSrc = resolvedTheme === 'dark' ? 'https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg-dark.mp4' : 'https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg2.mp4';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n        ref: videoRef,\n        autoPlay: true,\n        loop: true,\n        muted: true,\n        playsInline: true,\n        onLoadedData: ()=>setIsLoaded(true),\n        className: `absolute inset-0 z-0 h-full w-full object-cover transition-opacity duration-700 ${isLoaded ? 'opacity-37 dark:opacity-57' : 'opacity-0'}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                src: videoSrc,\n                type: \"video/mp4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            \"Your browser does not support the video tag.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex h-[calc(100dvh-56px)] flex-1 flex-col justify-center overflow-hidden text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundVideo, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lang_home_components__WEBPACK_IMPORTED_MODULE_3__.Hero, {\n                    className: \"relative z-10\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\[lang]\\\\(home)\\\\page.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[lang]/(home)/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[lang]/(home)/sample-projects.ts":
/*!**********************************************!*\
  !*** ./app/[lang]/(home)/sample-projects.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SAMPLE_PROJECTS: () => (/* binding */ SAMPLE_PROJECTS)\n/* harmony export */ });\n// Sample project data for the ProjectsShowcase component\nconst SAMPLE_PROJECTS = [\n    {\n        id: 1,\n        title: \"TEN Agent Voice Assistant\",\n        description: \"Real-time multimodal AI agent with voice capabilities\",\n        category: \"AI Agent\",\n        author: \"TEN Team\",\n        remixes: \"2.1k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 2,\n        title: \"Real-time Translation Bot\",\n        description: \"Multi-language translation with voice synthesis\",\n        category: \"Translation\",\n        author: \"Community\",\n        remixes: \"1.8k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 3,\n        title: \"Smart Meeting Assistant\",\n        description: \"AI-powered meeting transcription and summarization\",\n        category: \"Productivity\",\n        author: \"DevTeam\",\n        remixes: \"1.5k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 4,\n        title: \"Interactive Storyteller\",\n        description: \"AI that creates and narrates interactive stories\",\n        category: \"Entertainment\",\n        author: \"StoryAI\",\n        remixes: \"2.3k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 5,\n        title: \"Code Review Assistant\",\n        description: \"AI-powered code analysis and suggestions\",\n        category: \"Developer Tools\",\n        author: \"CodeBot\",\n        remixes: \"1.9k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    },\n    {\n        id: 6,\n        title: \"Health Monitoring Agent\",\n        description: \"Personal health assistant with voice interaction\",\n        category: \"Healthcare\",\n        author: \"HealthTech\",\n        remixes: \"1.2k\",\n        image: \"/api/placeholder/300/200\",\n        href: \"#\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[lang]/(home)/sample-projects.ts\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n            destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n            outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n            secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n            ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n            link: 'text-primary underline-offset-4 hover:underline'\n        },\n        size: {\n            default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n            sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n            lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n            icon: 'size-9'\n        }\n    },\n    defaultVariants: {\n        variant: 'default',\n        size: 'default'\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : 'button';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/github-star-button.tsx":
/*!**********************************************!*\
  !*** ./components/ui/github-star-button.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GitHubStarButton: () => (/* binding */ GitHubStarButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Sparkles,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Sparkles,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ GitHubStarButton auto */ \n\n\nfunction useCountAnimation(endValue, duration = 8000) {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const countRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const startTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useCountAnimation.useEffect\": ()=>{\n            if (!endValue) return;\n            const animate = {\n                \"useCountAnimation.useEffect.animate\": (currentTime)=>{\n                    if (startTimeRef.current === null) {\n                        startTimeRef.current = currentTime;\n                    }\n                    const elapsed = currentTime - startTimeRef.current;\n                    const progress = Math.min(elapsed / duration, 1);\n                    const easeOutExpo = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);\n                    const startValue = countRef.current;\n                    const newValue = Math.floor(startValue + (endValue - startValue) * easeOutExpo);\n                    countRef.current = newValue;\n                    setCount(newValue);\n                    if (progress < 1) {\n                        requestAnimationFrame(animate);\n                    }\n                }\n            }[\"useCountAnimation.useEffect.animate\"];\n            startTimeRef.current = null;\n            requestAnimationFrame(animate);\n            return ({\n                \"useCountAnimation.useEffect\": ()=>{\n                    startTimeRef.current = null;\n                }\n            })[\"useCountAnimation.useEffect\"];\n        }\n    }[\"useCountAnimation.useEffect\"], [\n        endValue,\n        duration\n    ]);\n    return count;\n}\nfunction GitHubStarButton({ repo, className = '' }) {\n    const [starCount, setStarCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const animatedCount = useCountAnimation(starCount);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GitHubStarButton.useEffect\": ()=>{\n            const fetchStarCount = {\n                \"GitHubStarButton.useEffect.fetchStarCount\": async ()=>{\n                    try {\n                        // Use our API route instead of direct GitHub API calls\n                        const response = await fetch(`/api/github-stars?repo=${encodeURIComponent(repo)}`);\n                        if (response.ok) {\n                            const data = await response.json();\n                            setStarCount(data.stargazers_count);\n                        // Only show sparkles for fresh data, not cached\n                        // Note: Sparkle animation removed\n                        } else {\n                            // Handle API errors (including rate limits)\n                            const errorData = await response.json().catch({\n                                \"GitHubStarButton.useEffect.fetchStarCount\": ()=>({})\n                            }[\"GitHubStarButton.useEffect.fetchStarCount\"]);\n                            if (errorData.from_cache) {\n                                console.info('Using cached star count due to API error:', errorData.error || 'Unknown error');\n                            } else {\n                                console.warn('GitHub star count API failed, using fallback:', errorData.error || 'Unknown error');\n                            }\n                            // Use fallback count from API response (either cached or default)\n                            setStarCount(errorData.stargazers_count || 1000);\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch star count:', error);\n                        // Fallback: show a placeholder count\n                        setStarCount(1000);\n                    }\n                }\n            }[\"GitHubStarButton.useEffect.fetchStarCount\"];\n            fetchStarCount();\n        }\n    }[\"GitHubStarButton.useEffect\"], [\n        repo\n    ]);\n    const formatStarCount = (count)=>count.toLocaleString();\n    const handleClick = ()=>{\n        window.open(`https://github.com/${repo}`, '_blank', 'noopener,noreferrer');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            !isHovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -inset-2 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"absolute top-0 left-0 h-3 w-3 text-yellow-400 animate-ping\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"absolute top-0 right-0 h-2 w-2 text-blue-400 animate-ping animation-delay-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"absolute bottom-0 left-2 h-2 w-2 text-purple-400 animate-ping animation-delay-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"absolute bottom-0 right-2 h-3 w-3 text-green-400 animate-ping animation-delay-700\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleClick,\n                onMouseEnter: ()=>setIsHovered(true),\n                onMouseLeave: ()=>setIsHovered(false),\n                className: `group relative inline-flex items-center gap-2 rounded-md bg-gradient-to-r from-gray-900 to-black px-3 py-1.5 text-sm font-medium text-white transition-all duration-300 hover:from-gray-800 hover:to-gray-900 hover:scale-105 cursor-pointer ${className}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        viewBox: \"0 0 24 24\",\n                        className: \"h-4 w-4 relative z-10\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"dark:text-gray-300 relative z-10\",\n                        children: \"open source\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: `h-4 w-4 relative z-10 transition-all duration-300 ${isHovered ? 'fill-transparent stroke-yellow-500 scale-110' : 'fill-transparent stroke-white'}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `inline-block w-16 rounded bg-gradient-to-r from-gray-700 to-gray-600 px-2 py-0.5 text-center text-xs font-bold relative z-10 transition-all duration-300 ${isHovered ? 'from-yellow-600 to-yellow-500 text-white scale-105' : ''}`,\n                        children: formatStarCount(animatedCount)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    isHovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50\",\n                        children: \"⭐ Star this repo!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\components\\\\ui\\\\github-star-button.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2dpdGh1Yi1zdGFyLWJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDTjtBQU83QyxTQUFTSyxrQkFBa0JDLFFBQXVCLEVBQUVDLFdBQW1CLElBQUk7SUFDekUsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdULCtDQUFRQSxDQUFDO0lBQ25DLE1BQU1VLFdBQVdSLDZDQUFNQSxDQUFTO0lBQ2hDLE1BQU1TLGVBQWVULDZDQUFNQSxDQUFnQjtJQUUzQ0QsZ0RBQVNBO3VDQUFDO1lBQ1IsSUFBSSxDQUFDSyxVQUFVO1lBRWYsTUFBTU07dURBQVUsQ0FBQ0M7b0JBQ2YsSUFBSUYsYUFBYUcsT0FBTyxLQUFLLE1BQU07d0JBQ2pDSCxhQUFhRyxPQUFPLEdBQUdEO29CQUN6QjtvQkFFQSxNQUFNRSxVQUFVRixjQUFjRixhQUFhRyxPQUFPO29CQUNsRCxNQUFNRSxXQUFXQyxLQUFLQyxHQUFHLENBQUNILFVBQVVSLFVBQVU7b0JBQzlDLE1BQU1ZLGNBQWNILGFBQWEsSUFBSSxJQUFJLElBQUlDLEtBQUtHLEdBQUcsQ0FBQyxHQUFHLENBQUMsS0FBS0o7b0JBRS9ELE1BQU1LLGFBQWFYLFNBQVNJLE9BQU87b0JBQ25DLE1BQU1RLFdBQVdMLEtBQUtNLEtBQUssQ0FBQ0YsYUFBYSxDQUFDZixXQUFXZSxVQUFTLElBQUtGO29CQUNuRVQsU0FBU0ksT0FBTyxHQUFHUTtvQkFDbkJiLFNBQVNhO29CQUVULElBQUlOLFdBQVcsR0FBRzt3QkFDaEJRLHNCQUFzQlo7b0JBQ3hCO2dCQUNGOztZQUVBRCxhQUFhRyxPQUFPLEdBQUc7WUFDdkJVLHNCQUFzQlo7WUFFdEI7K0NBQU87b0JBQ0xELGFBQWFHLE9BQU8sR0FBRztnQkFDekI7O1FBQ0Y7c0NBQUc7UUFBQ1I7UUFBVUM7S0FBUztJQUV2QixPQUFPQztBQUNUO0FBRU8sU0FBU2lCLGlCQUFpQixFQUFFQyxJQUFJLEVBQUVDLFlBQVksRUFBRSxFQUF5QjtJQUM5RSxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBRzdCLCtDQUFRQSxDQUFnQjtJQUMxRCxNQUFNLENBQUM4QixXQUFXQyxhQUFhLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNZ0MsZ0JBQWdCM0Isa0JBQWtCdUI7SUFFeEMzQixnREFBU0E7c0NBQUM7WUFDUixNQUFNZ0M7NkRBQWlCO29CQUNyQixJQUFJO3dCQUNGLHVEQUF1RDt3QkFDdkQsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLENBQUMsdUJBQXVCLEVBQUVDLG1CQUFtQlYsT0FBTzt3QkFFakYsSUFBSVEsU0FBU0csRUFBRSxFQUFFOzRCQUNmLE1BQU1DLE9BQU8sTUFBTUosU0FBU0ssSUFBSTs0QkFDaENWLGFBQWFTLEtBQUtFLGdCQUFnQjt3QkFFbEMsZ0RBQWdEO3dCQUNoRCxrQ0FBa0M7d0JBQ3BDLE9BQU87NEJBQ0wsNENBQTRDOzRCQUM1QyxNQUFNQyxZQUFZLE1BQU1QLFNBQVNLLElBQUksR0FBR0csS0FBSzs2RUFBQyxJQUFPLEVBQUM7OzRCQUV0RCxJQUFJRCxVQUFVRSxVQUFVLEVBQUU7Z0NBQ3hCQyxRQUFRQyxJQUFJLENBQUMsNkNBQTZDSixVQUFVSyxLQUFLLElBQUk7NEJBQy9FLE9BQU87Z0NBQ0xGLFFBQVFHLElBQUksQ0FBQyxpREFBaUROLFVBQVVLLEtBQUssSUFBSTs0QkFDbkY7NEJBRUEsa0VBQWtFOzRCQUNsRWpCLGFBQWFZLFVBQVVELGdCQUFnQixJQUFJO3dCQUM3QztvQkFDRixFQUFFLE9BQU9NLE9BQU87d0JBQ2RGLFFBQVFFLEtBQUssQ0FBQywrQkFBK0JBO3dCQUM3QyxxQ0FBcUM7d0JBQ3JDakIsYUFBYTtvQkFDZjtnQkFDRjs7WUFFQUk7UUFDRjtxQ0FBRztRQUFDUDtLQUFLO0lBRVQsTUFBTXNCLGtCQUFrQixDQUFDeEMsUUFBa0JBLE1BQU15QyxjQUFjO0lBRS9ELE1BQU1DLGNBQWM7UUFDbEJDLE9BQU9DLElBQUksQ0FBQyxDQUFDLG1CQUFtQixFQUFFMUIsTUFBTSxFQUFFLFVBQVU7SUFDdEQ7SUFFQSxxQkFDRSw4REFBQzJCO1FBQUkxQixXQUFVOztZQUVaLENBQUNHLDJCQUNBLDhEQUFDdUI7Z0JBQUkxQixXQUFVOztrQ0FDYiw4REFBQ3ZCLHlGQUFRQTt3QkFBQ3VCLFdBQVU7Ozs7OztrQ0FDcEIsOERBQUN2Qix5RkFBUUE7d0JBQUN1QixXQUFVOzs7Ozs7a0NBQ3BCLDhEQUFDdkIseUZBQVFBO3dCQUFDdUIsV0FBVTs7Ozs7O2tDQUNwQiw4REFBQ3ZCLHlGQUFRQTt3QkFBQ3VCLFdBQVU7Ozs7Ozs7Ozs7OzswQkFJeEIsOERBQUMyQjtnQkFDQ0MsU0FBU0w7Z0JBQ1RNLGNBQWMsSUFBTXpCLGFBQWE7Z0JBQ2pDMEIsY0FBYyxJQUFNMUIsYUFBYTtnQkFDakNKLFdBQVcsQ0FBQyw2T0FBNk8sRUFBRUEsV0FBVzs7a0NBR3RRLDhEQUFDK0I7d0JBQUlDLFNBQVE7d0JBQVloQyxXQUFVO3dCQUF3QmlDLE1BQUs7a0NBQzlELDRFQUFDQzs0QkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7a0NBRVYsOERBQUNDO3dCQUFLcEMsV0FBVTtrQ0FBbUM7Ozs7OztrQ0FDbkQsOERBQUN4Qix5RkFBSUE7d0JBQUN3QixXQUFXLENBQUMsa0RBQWtELEVBQUVHLFlBQVksaURBQWlELGlDQUFpQzs7Ozs7O2tDQUNwSyw4REFBQ2lDO3dCQUFLcEMsV0FBVyxDQUFDLHlKQUF5SixFQUFFRyxZQUFZLHVEQUF1RCxJQUFJO2tDQUNqUGtCLGdCQUFnQmhCOzs7Ozs7b0JBSWxCRiwyQkFDQyw4REFBQ3VCO3dCQUFJMUIsV0FBVTtrQ0FBb047Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU83TyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXGNvbXBvbmVudHNcXHVpXFxnaXRodWItc3Rhci1idXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFN0YXIsIFNwYXJrbGVzIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgR2l0SHViU3RhckJ1dHRvblByb3BzIHtcbiAgcmVwbzogc3RyaW5nIC8vIEZvcm1hdDogXCJvd25lci9yZXBvXCJcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmZ1bmN0aW9uIHVzZUNvdW50QW5pbWF0aW9uKGVuZFZhbHVlOiBudW1iZXIgfCBudWxsLCBkdXJhdGlvbjogbnVtYmVyID0gODAwMCkge1xuICBjb25zdCBbY291bnQsIHNldENvdW50XSA9IHVzZVN0YXRlKDApXG4gIGNvbnN0IGNvdW50UmVmID0gdXNlUmVmPG51bWJlcj4oMClcbiAgY29uc3Qgc3RhcnRUaW1lUmVmID0gdXNlUmVmPG51bWJlciB8IG51bGw+KG51bGwpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWVuZFZhbHVlKSByZXR1cm5cblxuICAgIGNvbnN0IGFuaW1hdGUgPSAoY3VycmVudFRpbWU6IG51bWJlcikgPT4ge1xuICAgICAgaWYgKHN0YXJ0VGltZVJlZi5jdXJyZW50ID09PSBudWxsKSB7XG4gICAgICAgIHN0YXJ0VGltZVJlZi5jdXJyZW50ID0gY3VycmVudFRpbWVcbiAgICAgIH1cblxuICAgICAgY29uc3QgZWxhcHNlZCA9IGN1cnJlbnRUaW1lIC0gc3RhcnRUaW1lUmVmLmN1cnJlbnRcbiAgICAgIGNvbnN0IHByb2dyZXNzID0gTWF0aC5taW4oZWxhcHNlZCAvIGR1cmF0aW9uLCAxKVxuICAgICAgY29uc3QgZWFzZU91dEV4cG8gPSBwcm9ncmVzcyA9PT0gMSA/IDEgOiAxIC0gTWF0aC5wb3coMiwgLTEwICogcHJvZ3Jlc3MpXG4gICAgICBcbiAgICAgIGNvbnN0IHN0YXJ0VmFsdWUgPSBjb3VudFJlZi5jdXJyZW50XG4gICAgICBjb25zdCBuZXdWYWx1ZSA9IE1hdGguZmxvb3Ioc3RhcnRWYWx1ZSArIChlbmRWYWx1ZSAtIHN0YXJ0VmFsdWUpICogZWFzZU91dEV4cG8pXG4gICAgICBjb3VudFJlZi5jdXJyZW50ID0gbmV3VmFsdWVcbiAgICAgIHNldENvdW50KG5ld1ZhbHVlKVxuXG4gICAgICBpZiAocHJvZ3Jlc3MgPCAxKSB7XG4gICAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShhbmltYXRlKVxuICAgICAgfVxuICAgIH1cblxuICAgIHN0YXJ0VGltZVJlZi5jdXJyZW50ID0gbnVsbFxuICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShhbmltYXRlKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHN0YXJ0VGltZVJlZi5jdXJyZW50ID0gbnVsbFxuICAgIH1cbiAgfSwgW2VuZFZhbHVlLCBkdXJhdGlvbl0pXG5cbiAgcmV0dXJuIGNvdW50XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBHaXRIdWJTdGFyQnV0dG9uKHsgcmVwbywgY2xhc3NOYW1lID0gJycgfTogR2l0SHViU3RhckJ1dHRvblByb3BzKSB7XG4gIGNvbnN0IFtzdGFyQ291bnQsIHNldFN0YXJDb3VudF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbaXNIb3ZlcmVkLCBzZXRJc0hvdmVyZWRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IGFuaW1hdGVkQ291bnQgPSB1c2VDb3VudEFuaW1hdGlvbihzdGFyQ291bnQpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBmZXRjaFN0YXJDb3VudCA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIFVzZSBvdXIgQVBJIHJvdXRlIGluc3RlYWQgb2YgZGlyZWN0IEdpdEh1YiBBUEkgY2FsbHNcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9naXRodWItc3RhcnM/cmVwbz0ke2VuY29kZVVSSUNvbXBvbmVudChyZXBvKX1gKVxuICAgICAgICBcbiAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICAgIHNldFN0YXJDb3VudChkYXRhLnN0YXJnYXplcnNfY291bnQpXG4gICAgICAgICAgXG4gICAgICAgICAgLy8gT25seSBzaG93IHNwYXJrbGVzIGZvciBmcmVzaCBkYXRhLCBub3QgY2FjaGVkXG4gICAgICAgICAgLy8gTm90ZTogU3BhcmtsZSBhbmltYXRpb24gcmVtb3ZlZFxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIEhhbmRsZSBBUEkgZXJyb3JzIChpbmNsdWRpbmcgcmF0ZSBsaW1pdHMpXG4gICAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpXG4gICAgICAgICAgXG4gICAgICAgICAgaWYgKGVycm9yRGF0YS5mcm9tX2NhY2hlKSB7XG4gICAgICAgICAgICBjb25zb2xlLmluZm8oJ1VzaW5nIGNhY2hlZCBzdGFyIGNvdW50IGR1ZSB0byBBUEkgZXJyb3I6JywgZXJyb3JEYXRhLmVycm9yIHx8ICdVbmtub3duIGVycm9yJylcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCdHaXRIdWIgc3RhciBjb3VudCBBUEkgZmFpbGVkLCB1c2luZyBmYWxsYmFjazonLCBlcnJvckRhdGEuZXJyb3IgfHwgJ1Vua25vd24gZXJyb3InKVxuICAgICAgICAgIH1cbiAgICAgICAgICBcbiAgICAgICAgICAvLyBVc2UgZmFsbGJhY2sgY291bnQgZnJvbSBBUEkgcmVzcG9uc2UgKGVpdGhlciBjYWNoZWQgb3IgZGVmYXVsdClcbiAgICAgICAgICBzZXRTdGFyQ291bnQoZXJyb3JEYXRhLnN0YXJnYXplcnNfY291bnQgfHwgMTAwMClcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHN0YXIgY291bnQ6JywgZXJyb3IpXG4gICAgICAgIC8vIEZhbGxiYWNrOiBzaG93IGEgcGxhY2Vob2xkZXIgY291bnRcbiAgICAgICAgc2V0U3RhckNvdW50KDEwMDApXG4gICAgICB9XG4gICAgfVxuXG4gICAgZmV0Y2hTdGFyQ291bnQoKVxuICB9LCBbcmVwb10pXG5cbiAgY29uc3QgZm9ybWF0U3RhckNvdW50ID0gKGNvdW50OiBudW1iZXIpID0+IGNvdW50LnRvTG9jYWxlU3RyaW5nKClcblxuICBjb25zdCBoYW5kbGVDbGljayA9ICgpID0+IHtcbiAgICB3aW5kb3cub3BlbihgaHR0cHM6Ly9naXRodWIuY29tLyR7cmVwb31gLCAnX2JsYW5rJywgJ25vb3BlbmVyLG5vcmVmZXJyZXInKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICB7LyogU3BhcmtsZXMgZWZmZWN0ICovfVxuICAgICAgeyFpc0hvdmVyZWQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC1pbnNldC0yIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIGgtMyB3LTMgdGV4dC15ZWxsb3ctNDAwIGFuaW1hdGUtcGluZ1wiIC8+XG4gICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIHJpZ2h0LTAgaC0yIHctMiB0ZXh0LWJsdWUtNDAwIGFuaW1hdGUtcGluZyBhbmltYXRpb24tZGVsYXktMzAwXCIgLz5cbiAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0yIGgtMiB3LTIgdGV4dC1wdXJwbGUtNDAwIGFuaW1hdGUtcGluZyBhbmltYXRpb24tZGVsYXktNTAwXCIgLz5cbiAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgcmlnaHQtMiBoLTMgdy0zIHRleHQtZ3JlZW4tNDAwIGFuaW1hdGUtcGluZyBhbmltYXRpb24tZGVsYXktNzAwXCIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgICAgXG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldElzSG92ZXJlZCh0cnVlKX1cbiAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRJc0hvdmVyZWQoZmFsc2UpfVxuICAgICAgICBjbGFzc05hbWU9e2Bncm91cCByZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcm91bmRlZC1tZCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JheS05MDAgdG8tYmxhY2sgcHgtMyBweS0xLjUgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3Zlcjpmcm9tLWdyYXktODAwIGhvdmVyOnRvLWdyYXktOTAwIGhvdmVyOnNjYWxlLTEwNSBjdXJzb3ItcG9pbnRlciAke2NsYXNzTmFtZX1gfVxuICAgICAgPlxuICAgICAgICB7LyogR2l0SHViIGljb24gKi99XG4gICAgICAgIDxzdmcgdmlld0JveD1cIjAgMCAyNCAyNFwiIGNsYXNzTmFtZT1cImgtNCB3LTQgcmVsYXRpdmUgei0xMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICA8cGF0aCBkPVwiTTEyIDBDNS4zNyAwIDAgNS4zNyAwIDEyYzAgNS4zMSAzLjQzNSA5Ljc5NSA4LjIwNSAxMS4zODUuNi4xMDUuODI1LS4yNTUuODI1LS41NyAwLS4yODUtLjAxNS0xLjIzLS4wMTUtMi4yMzUtMy4wMTUuNTU1LTMuNzk1LS43MzUtNC4wMzUtMS40MS0uMTM1LS4zNDUtLjcyLTEuNDEtMS4yMy0xLjY5NS0uNDItLjIyNS0xLjAyLS43OC0uMDE1LS43OTUuOTQ1LS4wMTUgMS42Mi44NyAxLjg0NSAxLjIzIDEuMDggMS44MTUgMi44MDUgMS4zMDUgMy40OTUuOTkuMTA1LS43OC40Mi0xLjMwNS43NjUtMS42MDUtMi42Ny0uMy01LjQ2LTEuMzM1LTUuNDYtNS45MjUgMC0xLjMwNS40NjUtMi4zODUgMS4yMy0zLjIyNS0uMTItLjMtLjU0LTEuNTMuMTItMy4xOCAwIDAgMS4wMDUtLjMxNSAzLjMgMS4yMy45Ni0uMjcgMS45OC0uNDA1IDMtLjQwNXMyLjA0LjEzNSAzIC40MDVjMi4yOTUtMS41NiAzLjMtMS4yMyAzLjMtMS4yMy42NiAxLjY1LjI0IDIuODguMTIgMy4xOC43NjUuODQgMS4yMyAxLjkwNSAxLjIzIDMuMjI1IDAgNC42MDUtMi44MDUgNS42MjUtNS40NzUgNS45MjUuNDM1LjM3NS44MSAxLjA5NS44MSAyLjIyIDAgMS42MDUtLjAxNSAyLjg5NS0uMDE1IDMuMyAwIC4zMTUuMjI1LjY5LjgyNS41N0ExMi4wMiAxMi4wMiAwIDAwMjQgMTJjMC02LjYzLTUuMzctMTItMTItMTJ6XCIgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImRhcms6dGV4dC1ncmF5LTMwMCByZWxhdGl2ZSB6LTEwXCI+b3BlbiBzb3VyY2U8L3NwYW4+XG4gICAgICAgIDxTdGFyIGNsYXNzTmFtZT17YGgtNCB3LTQgcmVsYXRpdmUgei0xMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtpc0hvdmVyZWQgPyAnZmlsbC10cmFuc3BhcmVudCBzdHJva2UteWVsbG93LTUwMCBzY2FsZS0xMTAnIDogJ2ZpbGwtdHJhbnNwYXJlbnQgc3Ryb2tlLXdoaXRlJ31gfSAvPlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtYmxvY2sgdy0xNiByb3VuZGVkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmF5LTcwMCB0by1ncmF5LTYwMCBweC0yIHB5LTAuNSB0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtYm9sZCByZWxhdGl2ZSB6LTEwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke2lzSG92ZXJlZCA/ICdmcm9tLXllbGxvdy02MDAgdG8teWVsbG93LTUwMCB0ZXh0LXdoaXRlIHNjYWxlLTEwNScgOiAnJ31gfT5cbiAgICAgICAgICB7Zm9ybWF0U3RhckNvdW50KGFuaW1hdGVkQ291bnQpfVxuICAgICAgICA8L3NwYW4+XG4gICAgICAgIFxuICAgICAgICB7LyogVG9vbHRpcCAqL31cbiAgICAgICAge2lzSG92ZXJlZCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTggbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgYmctYmxhY2sgdGV4dC13aGl0ZSB0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkIHdoaXRlc3BhY2Utbm93cmFwIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIHBvaW50ZXItZXZlbnRzLW5vbmUgei01MFwiPlxuICAgICAgICAgICAg4q2QIFN0YXIgdGhpcyByZXBvIVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9idXR0b24+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIlN0YXIiLCJTcGFya2xlcyIsInVzZUNvdW50QW5pbWF0aW9uIiwiZW5kVmFsdWUiLCJkdXJhdGlvbiIsImNvdW50Iiwic2V0Q291bnQiLCJjb3VudFJlZiIsInN0YXJ0VGltZVJlZiIsImFuaW1hdGUiLCJjdXJyZW50VGltZSIsImN1cnJlbnQiLCJlbGFwc2VkIiwicHJvZ3Jlc3MiLCJNYXRoIiwibWluIiwiZWFzZU91dEV4cG8iLCJwb3ciLCJzdGFydFZhbHVlIiwibmV3VmFsdWUiLCJmbG9vciIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsIkdpdEh1YlN0YXJCdXR0b24iLCJyZXBvIiwiY2xhc3NOYW1lIiwic3RhckNvdW50Iiwic2V0U3RhckNvdW50IiwiaXNIb3ZlcmVkIiwic2V0SXNIb3ZlcmVkIiwiYW5pbWF0ZWRDb3VudCIsImZldGNoU3RhckNvdW50IiwicmVzcG9uc2UiLCJmZXRjaCIsImVuY29kZVVSSUNvbXBvbmVudCIsIm9rIiwiZGF0YSIsImpzb24iLCJzdGFyZ2F6ZXJzX2NvdW50IiwiZXJyb3JEYXRhIiwiY2F0Y2giLCJmcm9tX2NhY2hlIiwiY29uc29sZSIsImluZm8iLCJlcnJvciIsIndhcm4iLCJmb3JtYXRTdGFyQ291bnQiLCJ0b0xvY2FsZVN0cmluZyIsImhhbmRsZUNsaWNrIiwid2luZG93Iiwib3BlbiIsImRpdiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJzdmciLCJ2aWV3Qm94IiwiZmlsbCIsInBhdGgiLCJkIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/github-star-button.tsx\n");

/***/ }),

/***/ "(ssr)/./constants/index.ts":
/*!****************************!*\
  !*** ./constants/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOG: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.BLOG),\n/* harmony export */   HUGGING_FACE_SPACE: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.HUGGING_FACE_SPACE),\n/* harmony export */   URL_TEN_AGENT: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_AGENT),\n/* harmony export */   URL_TEN_FAMILY: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_FAMILY),\n/* harmony export */   URL_TEN_FRAMEWORK_DOC: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_FRAMEWORK_DOC),\n/* harmony export */   URL_TEN_TURN_DETECTION: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_TURN_DETECTION),\n/* harmony export */   URL_TEN_VAD: () => (/* reexport safe */ _constants_url__WEBPACK_IMPORTED_MODULE_0__.URL_TEN_VAD)\n/* harmony export */ });\n/* harmony import */ var _constants_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants/url */ \"(ssr)/./constants/url.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb25zdGFudHMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxjb25zdGFudHNcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJ0AvY29uc3RhbnRzL3VybCdcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./constants/index.ts\n");

/***/ }),

/***/ "(ssr)/./constants/url.ts":
/*!**************************!*\
  !*** ./constants/url.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOG: () => (/* binding */ BLOG),\n/* harmony export */   HUGGING_FACE_SPACE: () => (/* binding */ HUGGING_FACE_SPACE),\n/* harmony export */   URL_TEN_AGENT: () => (/* binding */ URL_TEN_AGENT),\n/* harmony export */   URL_TEN_FAMILY: () => (/* binding */ URL_TEN_FAMILY),\n/* harmony export */   URL_TEN_FRAMEWORK_DOC: () => (/* binding */ URL_TEN_FRAMEWORK_DOC),\n/* harmony export */   URL_TEN_TURN_DETECTION: () => (/* binding */ URL_TEN_TURN_DETECTION),\n/* harmony export */   URL_TEN_VAD: () => (/* binding */ URL_TEN_VAD)\n/* harmony export */ });\nconst URL_TEN_AGENT = 'https://agent.theten.ai';\nconst BLOG = '/blog';\nconst URL_TEN_FRAMEWORK_DOC = '/docs/ten_framework/concept_overview';\nconst HUGGING_FACE_SPACE = 'https://huggingface.co/spaces/TEN-framework/ten-agent-demo';\nconst URL_TEN_TURN_DETECTION = 'https://github.com/ten-framework/ten-turn-detection';\nconst URL_TEN_FAMILY = 'https://github.com/ten-framework?view_as=public';\nconst URL_TEN_VAD = 'https://github.com/ten-framework/ten-vad';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb25zdGFudHMvdXJsLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBTyxNQUFNQSxnQkFBZ0IsMEJBQXlCO0FBQy9DLE1BQU1DLE9BQU8sUUFBTztBQUNwQixNQUFNQyx3QkFBd0IsdUNBQXNDO0FBQ3BFLE1BQU1DLHFCQUNYLDZEQUE0RDtBQUN2RCxNQUFNQyx5QkFDWCxzREFBcUQ7QUFDaEQsTUFBTUMsaUJBQWlCLGtEQUFpRDtBQUN4RSxNQUFNQyxjQUFjLDJDQUEwQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXGNvbnN0YW50c1xcdXJsLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBVUkxfVEVOX0FHRU5UID0gJ2h0dHBzOi8vYWdlbnQudGhldGVuLmFpJ1xuZXhwb3J0IGNvbnN0IEJMT0cgPSAnL2Jsb2cnXG5leHBvcnQgY29uc3QgVVJMX1RFTl9GUkFNRVdPUktfRE9DID0gJy9kb2NzL3Rlbl9mcmFtZXdvcmsvY29uY2VwdF9vdmVydmlldydcbmV4cG9ydCBjb25zdCBIVUdHSU5HX0ZBQ0VfU1BBQ0UgPVxuICAnaHR0cHM6Ly9odWdnaW5nZmFjZS5jby9zcGFjZXMvVEVOLWZyYW1ld29yay90ZW4tYWdlbnQtZGVtbydcbmV4cG9ydCBjb25zdCBVUkxfVEVOX1RVUk5fREVURUNUSU9OID1cbiAgJ2h0dHBzOi8vZ2l0aHViLmNvbS90ZW4tZnJhbWV3b3JrL3Rlbi10dXJuLWRldGVjdGlvbidcbmV4cG9ydCBjb25zdCBVUkxfVEVOX0ZBTUlMWSA9ICdodHRwczovL2dpdGh1Yi5jb20vdGVuLWZyYW1ld29yaz92aWV3X2FzPXB1YmxpYydcbmV4cG9ydCBjb25zdCBVUkxfVEVOX1ZBRCA9ICdodHRwczovL2dpdGh1Yi5jb20vdGVuLWZyYW1ld29yay90ZW4tdmFkJ1xuIl0sIm5hbWVzIjpbIlVSTF9URU5fQUdFTlQiLCJCTE9HIiwiVVJMX1RFTl9GUkFNRVdPUktfRE9DIiwiSFVHR0lOR19GQUNFX1NQQUNFIiwiVVJMX1RFTl9UVVJOX0RFVEVDVElPTiIsIlVSTF9URU5fRkFNSUxZIiwiVVJMX1RFTl9WQUQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./constants/url.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   nextIntlRouting: () => (/* binding */ nextIntlRouting)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\");\n\nconst LOCALES = [\n    'en',\n    'cn'\n];\nconst DEFAULT_LOCALE = 'en';\nconst i18n = {\n    defaultLanguage: DEFAULT_LOCALE,\n    languages: LOCALES,\n    hideLocale: 'default-locale'\n};\nconst nextIntlRouting = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    // A list of all locales that are supported\n    locales: LOCALES,\n    // Used when no locale matches\n    defaultLocale: DEFAULT_LOCALE\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDaUQ7QUFFakQsTUFBTUMsVUFBVTtJQUFDO0lBQU07Q0FBSztBQUM1QixNQUFNQyxpQkFBaUI7QUFFaEIsTUFBTUMsT0FBbUI7SUFDOUJDLGlCQUFpQkY7SUFDakJHLFdBQVdKO0lBQ1hLLFlBQVk7QUFDZCxFQUFDO0FBRU0sTUFBTUMsa0JBQWtCUCw2REFBYUEsQ0FBQztJQUMzQywyQ0FBMkM7SUFDM0NRLFNBQVNQO0lBQ1QsOEJBQThCO0lBQzlCUSxlQUFlUDtBQUNqQixHQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbGliXFxpMThuLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgSTE4bkNvbmZpZyB9IGZyb20gJ2Z1bWFkb2NzLWNvcmUvaTE4bidcbmltcG9ydCB7IGRlZmluZVJvdXRpbmcgfSBmcm9tICduZXh0LWludGwvcm91dGluZydcblxuY29uc3QgTE9DQUxFUyA9IFsnZW4nLCAnY24nXVxuY29uc3QgREVGQVVMVF9MT0NBTEUgPSAnZW4nXG5cbmV4cG9ydCBjb25zdCBpMThuOiBJMThuQ29uZmlnID0ge1xuICBkZWZhdWx0TGFuZ3VhZ2U6IERFRkFVTFRfTE9DQUxFLFxuICBsYW5ndWFnZXM6IExPQ0FMRVMsXG4gIGhpZGVMb2NhbGU6ICdkZWZhdWx0LWxvY2FsZScsXG59XG5cbmV4cG9ydCBjb25zdCBuZXh0SW50bFJvdXRpbmcgPSBkZWZpbmVSb3V0aW5nKHtcbiAgLy8gQSBsaXN0IG9mIGFsbCBsb2NhbGVzIHRoYXQgYXJlIHN1cHBvcnRlZFxuICBsb2NhbGVzOiBMT0NBTEVTLFxuICAvLyBVc2VkIHdoZW4gbm8gbG9jYWxlIG1hdGNoZXNcbiAgZGVmYXVsdExvY2FsZTogREVGQVVMVF9MT0NBTEUsXG59KVxuIl0sIm5hbWVzIjpbImRlZmluZVJvdXRpbmciLCJMT0NBTEVTIiwiREVGQVVMVF9MT0NBTEUiLCJpMThuIiwiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VzIiwiaGlkZUxvY2FsZSIsIm5leHRJbnRsUm91dGluZyIsImxvY2FsZXMiLCJkZWZhdWx0TG9jYWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/next-intl-navigation.ts":
/*!*************************************!*\
  !*** ./lib/next-intl-navigation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   getPathname: () => (/* binding */ getPathname),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/navigation */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n\n\n// Lightweight wrappers around Next.js' navigation\n// APIs that consider the routing configuration\nconst { Link, redirect, usePathname, useRouter, getPathname } = (0,next_intl_navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_lib_i18n__WEBPACK_IMPORTED_MODULE_0__.nextIntlRouting);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvbmV4dC1pbnRsLW5hdmlnYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1RDtBQUNYO0FBRTVDLGtEQUFrRDtBQUNsRCwrQ0FBK0M7QUFDeEMsTUFBTSxFQUFFRSxJQUFJLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxTQUFTLEVBQUVDLFdBQVcsRUFBRSxHQUNsRU4sZ0VBQWdCQSxDQUFDQyxzREFBZUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXGxpYlxcbmV4dC1pbnRsLW5hdmlnYXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlTmF2aWdhdGlvbiB9IGZyb20gJ25leHQtaW50bC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgbmV4dEludGxSb3V0aW5nIH0gZnJvbSAnQC9saWIvaTE4bidcblxuLy8gTGlnaHR3ZWlnaHQgd3JhcHBlcnMgYXJvdW5kIE5leHQuanMnIG5hdmlnYXRpb25cbi8vIEFQSXMgdGhhdCBjb25zaWRlciB0aGUgcm91dGluZyBjb25maWd1cmF0aW9uXG5leHBvcnQgY29uc3QgeyBMaW5rLCByZWRpcmVjdCwgdXNlUGF0aG5hbWUsIHVzZVJvdXRlciwgZ2V0UGF0aG5hbWUgfSA9XG4gIGNyZWF0ZU5hdmlnYXRpb24obmV4dEludGxSb3V0aW5nKVxuIl0sIm5hbWVzIjpbImNyZWF0ZU5hdmlnYXRpb24iLCJuZXh0SW50bFJvdXRpbmciLCJMaW5rIiwicmVkaXJlY3QiLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciIsImdldFBhdGhuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/next-intl-navigation.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tICdjbHN4J1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJ1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/(home)/page.tsx */ \"(ssr)/./app/[lang]/(home)/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RlZXBJbnNpZ2h0JTVDJTVDRG93bmxvYWRzJTVDJTVDcG9ydGFsLW1haW4lNUMlNUNwb3J0YWwtbWFpbiU1QyU1Q2FwcCU1QyU1QyU1QmxhbmclNUQlNUMlNUMoaG9tZSklNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQStIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEZWVwSW5zaWdodFxcXFxEb3dubG9hZHNcXFxccG9ydGFsLW1haW5cXFxccG9ydGFsLW1haW5cXFxcYXBwXFxcXFtsYW5nXVxcXFwoaG9tZSlcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5C%5Blang%5D%5C%5C(home)%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Ccomponents%5C%5Cui%5C%5Cgithub-star-button.tsx%22%2C%22ids%22%3A%5B%22GitHubStarButton%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Ccomponents%5C%5Cui%5C%5Cgithub-star-button.tsx%22%2C%22ids%22%3A%5B%22GitHubStarButton%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/github-star-button.tsx */ \"(ssr)/./components/ui/github-star-button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-core/dist/link.js */ \"(ssr)/./node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/components/layout/language-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/components/layout/search-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/contexts/layout.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/contexts/layout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(ssr)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/layouts/home/<USER>/ \"(ssr)/./node_modules/fumadocs-ui/dist/layouts/home/<USER>"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Ccomponents%5C%5Cui%5C%5Cgithub-star-button.tsx%22%2C%22ids%22%3A%5B%22GitHubStarButton%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-core%5C%5Cdist%5C%5Clink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Clanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Csearch-toggle.js%22%2C%22ids%22%3A%5B%22SearchToggle%22%2C%22LargeSearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccomponents%5C%5Clayout%5C%5Ctheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Ccontexts%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cmenu.js%22%2C%22ids%22%3A%5B%22Menu%22%2C%22MenuTrigger%22%2C%22MenuContent%22%2C%22MenuLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Clayouts%5C%5Chome%5C%5Cnavbar.js%22%2C%22ids%22%3A%5B%22Navbar%22%2C%22NavbarMenuLink%22%2C%22NavbarMenu%22%2C%22NavbarMenuTrigger%22%2C%22NavbarMenuContent%22%2C%22NavbarLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/fumadocs-ui/dist/provider/index.js */ \"(ssr)/./node_modules/fumadocs-ui/dist/provider/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cfumadocs-ui%5C%5Cdist%5C%5Cprovider%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5C%5Blang%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Capp%5C%5Cglobal.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDeepInsight%5C%5CDownloads%5C%5Cportal-main%5C%5Cportal-main%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/fumadocs-core","vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/fumadocs-ui","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/next-intl","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/@formatjs","vendor-chunks/@floating-ui","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/get-nonce","vendor-chunks/motion","vendor-chunks/canvas-confetti"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blang%5D%2F(home)%2Fpage&page=%2F%5Blang%5D%2F(home)%2Fpage&appPaths=%2F%5Blang%5D%2F(home)%2Fpage&pagePath=private-next-app-dir%2F%5Blang%5D%2F(home)%2Fpage.tsx&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();