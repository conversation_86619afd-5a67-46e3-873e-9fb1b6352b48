"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[737],{2085:(e,r,t)=>{t.d(r,{F:()=>a});var n=t(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,a=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:i}=r,s=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let l=o(r)||o(n);return a[e][l]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return l(e,s,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2596:(e,r,t)=>{t.d(r,{$:()=>n});function n(){for(var e,r,t=0,n="",o=arguments.length;t<o;t++)(e=arguments[t])&&(r=function e(r){var t,n,o="";if("string"==typeof r||"number"==typeof r)o+=r;else if("object"==typeof r)if(Array.isArray(r)){var l=r.length;for(t=0;t<l;t++)r[t]&&(n=e(r[t]))&&(o&&(o+=" "),o+=n)}else for(n in r)r[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=r);return n}},2829:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(3536).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},3536:(e,r,t)=>{t.d(r,{A:()=>c});var n=t(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:a,className:d="",children:c,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:r,...s,width:o,height:o,stroke:t,strokeWidth:a?24*Number(l)/Number(o):l,className:i("lucide",d),...f},[...u.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),c=(e,r)=>{let t=(0,n.forwardRef)((t,l)=>{let{className:s,...c}=t;return(0,n.createElement)(d,{ref:l,iconNode:r,className:i("lucide-".concat(o(a(e))),"lucide-".concat(e),s),...c})});return t.displayName=a(e),t}},5675:(e,r,t)=>{t.d(r,{FK:()=>V,$H:()=>q,Gl:()=>$});var n=t(5155),o=t(2115),l=t(3655),a=t(8905),i=t(6081),s=t(6101),d=t(9033),c=t(4315),u=t(2712),f=t(5185),p="ScrollArea",[h,v]=(0,i.A)(p),[w,m]=h(p),g=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:a="hover",dir:i,scrollHideDelay:d=600,...u}=e,[f,p]=o.useState(null),[h,v]=o.useState(null),[m,g]=o.useState(null),[b,y]=o.useState(null),[x,S]=o.useState(null),[C,R]=o.useState(0),[E,N]=o.useState(0),[j,T]=o.useState(!1),[A,L]=o.useState(!1),P=(0,s.s)(r,e=>p(e)),_=(0,c.jH)(i);return(0,n.jsx)(w,{scope:t,type:a,dir:_,scrollHideDelay:d,scrollArea:f,viewport:h,onViewportChange:v,content:m,onContentChange:g,scrollbarX:b,onScrollbarXChange:y,scrollbarXEnabled:j,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:S,scrollbarYEnabled:A,onScrollbarYEnabledChange:L,onCornerWidthChange:R,onCornerHeightChange:N,children:(0,n.jsx)(l.sG.div,{dir:_,...u,ref:P,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":E+"px",...e.style}})})});g.displayName=p;var b="ScrollAreaViewport",y=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:a,nonce:i,...d}=e,c=m(b,t),u=o.useRef(null),f=(0,s.s)(r,u,c.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,n.jsx)(l.sG.div,{"data-radix-scroll-area-viewport":"",...d,ref:f,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});y.displayName=b;var x="ScrollAreaScrollbar",S=o.forwardRef((e,r)=>{let{forceMount:t,...l}=e,a=m(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=a,d="horizontal"===e.orientation;return o.useEffect(()=>(d?i(!0):s(!0),()=>{d?i(!1):s(!1)}),[d,i,s]),"hover"===a.type?(0,n.jsx)(C,{...l,ref:r,forceMount:t}):"scroll"===a.type?(0,n.jsx)(R,{...l,ref:r,forceMount:t}):"auto"===a.type?(0,n.jsx)(E,{...l,ref:r,forceMount:t}):"always"===a.type?(0,n.jsx)(N,{...l,ref:r}):null});S.displayName=x;var C=o.forwardRef((e,r)=>{let{forceMount:t,...l}=e,i=m(x,e.__scopeScrollArea),[s,d]=o.useState(!1);return o.useEffect(()=>{let e=i.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),d(!0)},n=()=>{r=window.setTimeout(()=>d(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,n.jsx)(a.C,{present:t||s,children:(0,n.jsx)(E,{"data-state":s?"visible":"hidden",...l,ref:r})})}),R=o.forwardRef((e,r)=>{var t,l;let{forceMount:i,...s}=e,d=m(x,e.__scopeScrollArea),c="horizontal"===e.orientation,u=G(()=>h("SCROLL_END"),100),[p,h]=(t="hidden",l={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>{let t=l[e][r];return null!=t?t:e},t));return o.useEffect(()=>{if("idle"===p){let e=window.setTimeout(()=>h("HIDE"),d.scrollHideDelay);return()=>window.clearTimeout(e)}},[p,d.scrollHideDelay,h]),o.useEffect(()=>{let e=d.viewport,r=c?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(h("SCROLL"),u()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[d.viewport,c,h,u]),(0,n.jsx)(a.C,{present:i||"hidden"!==p,children:(0,n.jsx)(N,{"data-state":"hidden"===p?"hidden":"visible",...s,ref:r,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),E=o.forwardRef((e,r)=>{let t=m(x,e.__scopeScrollArea),{forceMount:l,...i}=e,[s,d]=o.useState(!1),c="horizontal"===e.orientation,u=G(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;d(c?e:r)}},10);return B(t.viewport,u),B(t.content,u),(0,n.jsx)(a.C,{present:l||s,children:(0,n.jsx)(N,{"data-state":s?"visible":"hidden",...i,ref:r})})}),N=o.forwardRef((e,r)=>{let{orientation:t="vertical",...l}=e,a=m(x,e.__scopeScrollArea),i=o.useRef(null),s=o.useRef(0),[d,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=I(d.viewport,d.content),f={...l,sizes:d,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,r){return function(e,r,t){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=U(t),l=r||o/2,a=t.scrollbar.paddingStart+l,i=t.scrollbar.size-t.scrollbar.paddingEnd-(o-l),s=t.content-t.viewport;return X([a,i],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,d,r)}return"horizontal"===t?(0,n.jsx)(j,{...f,ref:r,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=F(a.viewport.scrollLeft,d,a.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=p(e,a.dir))}}):"vertical"===t?(0,n.jsx)(T,{...f,ref:r,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=F(a.viewport.scrollTop,d);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=p(e))}}):null}),j=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:l,...a}=e,i=m(x,e.__scopeScrollArea),[d,c]=o.useState(),u=o.useRef(null),f=(0,s.s)(r,u,i.onScrollbarXChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,n.jsx)(P,{"data-orientation":"horizontal",...a,ref:f,sizes:t,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":U(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(i.viewport){let n=i.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{u.current&&i.viewport&&d&&l({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:H(d.paddingLeft),paddingEnd:H(d.paddingRight)}})}})}),T=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:l,...a}=e,i=m(x,e.__scopeScrollArea),[d,c]=o.useState(),u=o.useRef(null),f=(0,s.s)(r,u,i.onScrollbarYChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,n.jsx)(P,{"data-orientation":"vertical",...a,ref:f,sizes:t,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":U(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(i.viewport){let n=i.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{u.current&&i.viewport&&d&&l({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:H(d.paddingTop),paddingEnd:H(d.paddingBottom)}})}})}),[A,L]=h(x),P=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:a,hasThumb:i,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:p,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:g,...b}=e,y=m(x,t),[S,C]=o.useState(null),R=(0,s.s)(r,e=>C(e)),E=o.useRef(null),N=o.useRef(""),j=y.viewport,T=a.content-a.viewport,L=(0,d.c)(w),P=(0,d.c)(h),_=G(g,10);function D(e){E.current&&v({x:e.clientX-E.current.left,y:e.clientY-E.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;(null==S?void 0:S.contains(r))&&L(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[j,S,T,L]),o.useEffect(P,[a,P]),B(S,_),B(y.content,_),(0,n.jsx)(A,{scope:t,scrollbar:S,hasThumb:i,onThumbChange:(0,d.c)(c),onThumbPointerUp:(0,d.c)(u),onThumbPositionChange:P,onThumbPointerDown:(0,d.c)(p),children:(0,n.jsx)(l.sG.div,{...b,ref:R,style:{position:"absolute",...b.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),E.current=S.getBoundingClientRect(),N.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:(0,f.m)(e.onPointerMove,D),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=N.current,y.viewport&&(y.viewport.style.scrollBehavior=""),E.current=null})})})}),_="ScrollAreaThumb",D=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=L(_,e.__scopeScrollArea);return(0,n.jsx)(a.C,{present:t||l.hasThumb,children:(0,n.jsx)(k,{ref:r,...o})})}),k=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:a,...i}=e,d=m(_,t),c=L(_,t),{onThumbPositionChange:u}=c,p=(0,s.s)(r,e=>c.onThumbChange(e)),h=o.useRef(void 0),v=G(()=>{h.current&&(h.current(),h.current=void 0)},100);return o.useEffect(()=>{let e=d.viewport;if(e){let r=()=>{v(),h.current||(h.current=Y(e,u),u())};return u(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[d.viewport,v,u]),(0,n.jsx)(l.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;c.onThumbPointerDown({x:t,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,c.onThumbPointerUp)})});D.displayName=_;var z="ScrollAreaCorner",W=o.forwardRef((e,r)=>{let t=m(z,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,n.jsx)(O,{...e,ref:r}):null});W.displayName=z;var O=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...a}=e,i=m(z,t),[s,d]=o.useState(0),[c,u]=o.useState(0),f=!!(s&&c);return B(i.scrollbarX,()=>{var e;let r=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(r),u(r)}),B(i.scrollbarY,()=>{var e;let r=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(r),d(r)}),f?(0,n.jsx)(l.sG.div,{...a,ref:r,style:{width:s,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function I(e,r){let t=e/r;return isNaN(t)?0:t}function U(e){let r=I(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function F(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=U(r),o=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-o,a=r.content-r.viewport,i=function(e,[r,t]){return Math.min(t,Math.max(r,e))}(e,"ltr"===t?[0,a]:[-1*a,0]);return X([0,a],[0,l-n])(i)}function X(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var Y=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},a=t.left!==l.left,i=t.top!==l.top;(a||i)&&r(),t=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function G(e,r){let t=(0,d.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function B(e,r){let t=(0,d.c)(r);(0,u.N)(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}var M=t(9688);let V=o.forwardRef(({className:e,children:r,...t},o)=>(0,n.jsxs)(g,{ref:o,className:(0,M.QP)("overflow-hidden",e),...t,children:[r,(0,n.jsx)(W,{}),(0,n.jsx)(q,{orientation:"vertical"})]}));V.displayName=g.displayName;let $=o.forwardRef(({className:e,children:r,...t},o)=>(0,n.jsx)(y,{ref:o,className:(0,M.QP)("size-full rounded-[inherit]",e),...t,children:r}));$.displayName=y.displayName;let q=o.forwardRef(({className:e,orientation:r="vertical",...t},o)=>(0,n.jsx)(S,{ref:o,orientation:r,className:(0,M.QP)("flex select-none data-[state=hidden]:animate-fd-fade-out","vertical"===r&&"h-full w-1.5","horizontal"===r&&"h-1.5 flex-col",e),...t,children:(0,n.jsx)(D,{className:"relative flex-1 rounded-full bg-fd-border"})}));q.displayName=S.displayName},7936:(e,r,t)=>{t.d(r,{r:()=>n});let n=(0,t(2085).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 p-1 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5"}}})},8758:(e,r,t)=>{t.d(r,{Nt:()=>N,Ke:()=>T,R6:()=>j});var n=t(5155),o=t(2115),l=t(5185),a=t(6081),i=t(5845),s=t(2712),d=t(6101),c=t(3655),u=t(8905),f=t(1285),p="Collapsible",[h,v]=(0,a.A)(p),[w,m]=h(p),g=o.forwardRef((e,r)=>{let{__scopeCollapsible:t,open:l,defaultOpen:a,disabled:s,onOpenChange:d,...u}=e,[h,v]=(0,i.i)({prop:l,defaultProp:null!=a&&a,onChange:d,caller:p});return(0,n.jsx)(w,{scope:t,disabled:s,contentId:(0,f.B)(),open:h,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),children:(0,n.jsx)(c.sG.div,{"data-state":R(h),"data-disabled":s?"":void 0,...u,ref:r})})});g.displayName=p;var b="CollapsibleTrigger",y=o.forwardRef((e,r)=>{let{__scopeCollapsible:t,...o}=e,a=m(b,t);return(0,n.jsx)(c.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":R(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...o,ref:r,onClick:(0,l.m)(e.onClick,a.onOpenToggle)})});y.displayName=b;var x="CollapsibleContent",S=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,l=m(x,e.__scopeCollapsible);return(0,n.jsx)(u.C,{present:t||l.open,children:e=>{let{present:t}=e;return(0,n.jsx)(C,{...o,ref:r,present:t})}})});S.displayName=x;var C=o.forwardRef((e,r)=>{let{__scopeCollapsible:t,present:l,children:a,...i}=e,u=m(x,t),[f,p]=o.useState(l),h=o.useRef(null),v=(0,d.s)(r,h),w=o.useRef(0),g=w.current,b=o.useRef(0),y=b.current,S=u.open||f,C=o.useRef(S),E=o.useRef(void 0);return o.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.N)(()=>{let e=h.current;if(e){E.current=E.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let r=e.getBoundingClientRect();w.current=r.height,b.current=r.width,C.current||(e.style.transitionDuration=E.current.transitionDuration,e.style.animationName=E.current.animationName),p(l)}},[u.open,l]),(0,n.jsx)(c.sG.div,{"data-state":R(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!S,...i,ref:v,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":y?"".concat(y,"px"):void 0,...e.style},children:S&&a})});function R(e){return e?"open":"closed"}var E=t(9688);let N=g,j=y,T=(0,o.forwardRef)((e,r)=>{let{children:t,...l}=e,[a,i]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{i(!0)},[]),(0,n.jsx)(S,{ref:r,...l,className:(0,E.QP)("overflow-hidden",a&&"data-[state=closed]:animate-fd-collapsible-up data-[state=open]:animate-fd-collapsible-down",l.className),children:t})});T.displayName=S.displayName},9949:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a});var n=t(9600),o=t(2115),l=t(5155),a=(0,o.forwardRef)(({href:e="#",external:r=!(e.startsWith("/")||e.startsWith("#")||e.startsWith(".")),prefetch:t,...o},a)=>r?(0,l.jsx)("a",{ref:a,href:e,rel:"noreferrer noopener",target:"_blank",...o,children:o.children}):(0,l.jsx)(n.N_,{ref:a,href:e,prefetch:t,...o}));a.displayName="Link",t(9189)}}]);