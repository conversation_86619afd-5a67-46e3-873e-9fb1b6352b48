"use strict";exports.id=510,exports.ids=[510],exports.modules={60510:(e,t,r)=>{r.r(t),r.d(t,{Primitive:()=>a,Tab:()=>ea,Tabs:()=>et});var a={};r.r(a),r.d(a,{Tabs:()=>q,TabsContent:()=>W,TabsList:()=>J,TabsTrigger:()=>Y});var o=r(60687),n=r(43210),s=r(82348),i=r(70569),l=r(11273),u=r(9510),d=r(98599),c=r(96963),f=r(14163),m=r(13495),p=r(65551),v=r(43),b="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[x,g,y]=(0,u.N)(w),[T,j]=(0,l.A)(w,[y]),[I,R]=T(w),C=n.forwardRef((e,t)=>(0,o.jsx)(x.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(x.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(N,{...e,ref:t})})}));C.displayName=w;var N=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:s=!1,dir:l,currentTabStopId:u,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:x,onEntryFocus:y,preventScrollOnEntryFocus:T=!1,...j}=e,R=n.useRef(null),C=(0,d.s)(t,R),N=(0,v.jH)(l),[F,A]=(0,p.i)({prop:u,defaultProp:c??null,onChange:x,caller:w}),[D,M]=n.useState(!1),P=(0,m.c)(y),S=g(r),G=n.useRef(!1),[K,L]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(b,P),()=>e.removeEventListener(b,P)},[P]),(0,o.jsx)(I,{scope:r,orientation:a,dir:N,loop:s,currentTabStopId:F,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>M(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,o.jsx)(f.sG.div,{tabIndex:D||0===K?-1:0,"data-orientation":a,...j,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{G.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!G.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(b,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===F),...e].filter(Boolean).map(e=>e.ref.current),T)}}G.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>M(!1))})})}),F="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:s=!1,tabStopId:l,children:u,...d}=e,m=(0,c.B)(),p=l||m,v=R(F,r),b=v.currentTabStopId===p,h=g(r),{onFocusableItemAdd:w,onFocusableItemRemove:y,currentTabStopId:T}=v;return n.useEffect(()=>{if(a)return w(),()=>y()},[a,w,y]),(0,o.jsx)(x.ItemSlot,{scope:r,id:p,focusable:a,active:s,children:(0,o.jsx)(f.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let o=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>E(r))}}),children:"function"==typeof u?u({isCurrentTabStop:b,hasTabStop:null!=T}):u})})});A.displayName=F;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e,t=!1){let r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var M=r(46059),P="Tabs",[S,G]=(0,l.A)(P,[j]),K=j(),[L,k]=S(P),Q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:s,orientation:i="horizontal",dir:l,activationMode:u="automatic",...d}=e,m=(0,v.jH)(l),[b,h]=(0,p.i)({prop:a,onChange:n,defaultProp:s??"",caller:P});return(0,o.jsx)(L,{scope:r,baseId:(0,c.B)(),value:b,onValueChange:h,orientation:i,dir:m,activationMode:u,children:(0,o.jsx)(f.sG.div,{dir:m,"data-orientation":i,...d,ref:t})})});Q.displayName=P;var B="TabsList",V=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,s=k(B,r),i=K(r);return(0,o.jsx)(C,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:a,children:(0,o.jsx)(f.sG.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:t})})});V.displayName=B;var $="TabsTrigger",_=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...s}=e,l=k($,r),u=K(r),d=U(l.baseId,a),c=z(l.baseId,a),m=a===l.value;return(0,o.jsx)(A,{asChild:!0,...u,focusable:!n,active:m,children:(0,o.jsx)(f.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":c,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:d,...s,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||n||!e||l.onValueChange(a)})})})});_.displayName=$;var H="TabsContent",O=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:s,children:i,...l}=e,u=k(H,r),d=U(u.baseId,a),c=z(u.baseId,a),m=a===u.value,p=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(M.C,{present:s||m,children:({present:r})=>(0,o.jsx)(f.sG.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:c,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&i})})});function U(e,t){return`${e}-trigger-${t}`}function z(e,t){return`${e}-content-${t}`}O.displayName=H;let q=n.forwardRef((e,t)=>(0,o.jsx)(Q,{ref:t,...e,className:(0,s.QP)("flex flex-col overflow-hidden rounded-xl border bg-fd-card",e.className)}));q.displayName="Tabs";let J=n.forwardRef((e,t)=>(0,o.jsx)(V,{ref:t,...e,className:(0,s.QP)("flex flex-row items-end gap-4 overflow-x-auto bg-fd-secondary px-4 text-fd-muted-foreground",e.className)}));J.displayName="TabsList";let Y=n.forwardRef((e,t)=>(0,o.jsx)(_,{ref:t,...e,className:(0,s.QP)("whitespace-nowrap border-b border-transparent py-2 text-sm font-medium transition-colors hover:text-fd-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-fd-primary data-[state=active]:text-fd-primary",e.className)}));Y.displayName="TabsTrigger";let W=n.forwardRef((e,t)=>(0,o.jsx)(O,{ref:t,...e,className:(0,s.QP)("p-4",e.className)}));W.displayName="TabsContent";var X=r(24249);let Z=new Map,ee=(0,n.createContext)(null);function et({groupId:e,items:t=[],persist:r=!1,defaultIndex:a=0,updateAnchor:i=!1,...l}){let u=(0,n.useMemo)(()=>t.map(e=>er(e)),[t]),[d,c]=(0,n.useState)(u[a]),f=(0,n.useMemo)(()=>new Map,[]),m=(0,n.useMemo)(()=>[],[t]);return(0,X.J)(e=>{u.includes(e)&&c(e)}),(0,o.jsxs)(q,{value:d,onValueChange:t=>{if(i){let e=f.get(t);e&&window.history.replaceState(null,"",`#${e}`)}e?(Z.get(e)?.forEach(e=>{e(t)}),r?localStorage.setItem(e,t):sessionStorage.setItem(e,t)):c(t)},...l,className:(0,s.QP)("my-4",l.className),children:[(0,o.jsx)(J,{children:u.map((e,r)=>(0,o.jsx)(Y,{value:e,children:t[r]},e))}),(0,o.jsx)(ee.Provider,{value:(0,n.useMemo)(()=>({items:t,valueToIdMap:f,collection:m}),[f,m,t]),children:l.children})]})}function er(e){return e.toLowerCase().replace(/\s/,"-")}function ea({value:e,className:t,...r}){let a=(0,n.useContext)(ee),i=e??a?.items.at(function(){let e=(0,n.useId)(),t=(0,n.useContext)(ee);if(!t)throw Error("You must wrap your component in <Tabs>");let r=t.collection;return(0,n.useMemo)(()=>{let t=r.indexOf(e);-1!==t&&r.splice(t,1),r.includes(e)||r.push(e)},[r]),r.indexOf(e)}());if(!i)throw Error("Failed to resolve tab `value`, please pass a `value` prop to the Tab component.");let l=er(i);return r.id&&a&&a.valueToIdMap.set(l,r.id),(0,o.jsx)(W,{value:l,className:(0,s.QP)("prose-no-margin [&>figure:only-child]:-m-4 [&>figure:only-child]:rounded-none [&>figure:only-child]:border-none",t),...r,children:r.children})}}};