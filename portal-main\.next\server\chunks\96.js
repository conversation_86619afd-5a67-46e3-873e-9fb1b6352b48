"use strict";exports.id=96,exports.ids=[96],exports.modules={7255:(e,r,t)=>{t.d(r,{C:()=>a,Z:()=>i});var o=t(37413),n=t(69767),l=t(8974);function a(e){return(0,o.jsx)("div",{...e,className:(0,l.QP)("grid grid-cols-2 gap-4 @container",e.className),children:e.children})}function i({icon:e,title:r,description:t,...a}){let i=a.href?n.default:"div";return(0,o.jsxs)(i,{...a,"data-card":!0,className:(0,l.QP)("block rounded-lg border bg-fd-card p-4 text-fd-card-foreground shadow-md transition-colors @max-lg:col-span-full",a.href&&"hover:bg-fd-accent/80",a.className),children:[e?(0,o.jsx)("div",{className:"not-prose mb-2 w-fit rounded-md border bg-fd-muted p-1.5 text-fd-muted-foreground [&_svg]:size-4",children:e}):null,(0,o.jsx)("h3",{className:"not-prose mb-1 text-sm font-medium",children:r}),t?(0,o.jsx)("p",{className:"!my-0 text-sm text-fd-muted-foreground",children:t}):null,a.children?(0,o.jsx)("div",{className:"text-sm text-fd-muted-foreground prose-no-margin",children:a.children}):null]})}},19956:(e,r,t)=>{t.d(r,{Image:()=>n});var o=t(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call FrameworkProvider() from the server but FrameworkProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","FrameworkProvider");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","Image");(0,o.registerClientReference)(function(){throw Error("Attempted to call Link() from the server but Link is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","Link"),(0,o.registerClientReference)(function(){throw Error("Attempted to call createContext() from the server but createContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","createContext"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useParams() from the server but useParams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","useParams"),(0,o.registerClientReference)(function(){throw Error("Attempted to call usePathname() from the server but usePathname is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","usePathname"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useRouter() from the server but useRouter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\framework\\index.js","useRouter")},20383:(e,r,t)=>{t.d(r,{CodeBlock:()=>l,Pre:()=>n});var o=t(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call Pre() from the server but Pre is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\codeblock.js","Pre"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call CodeBlock() from the server but CodeBlock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\codeblock.js","CodeBlock")},29013:(e,r,t)=>{t.d(r,{f:()=>f,A:()=>p});var o=t(37413),n=t(69767),l=t(19956),a=t(7255),i=t(32440);let s=(0,t(26373).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var d=t(8974);function c({as:e,className:r,...t}){let n=e??"h1";return t.id?(0,o.jsxs)(n,{className:(0,d.QP)("flex scroll-m-28 flex-row items-center gap-2",r),...t,children:[(0,o.jsx)("a",{"data-card":"",href:`#${t.id}`,className:"peer",children:t.children}),(0,o.jsx)(s,{"aria-label":"Link to section",className:"size-3.5 shrink-0 text-fd-muted-foreground opacity-0 transition-opacity peer-hover:opacity-100"})]}):(0,o.jsx)(n,{className:r,...t})}var u=t(20383);let p={pre:e=>(0,o.jsx)(u.CodeBlock,{...e,children:(0,o.jsx)(u.Pre,{children:e.children})}),Card:a.Z,Cards:a.C,a:n.default,img:function(e){return(0,o.jsx)(l.Image,{sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 70vw, 900px",...e,src:e.src,className:(0,d.QP)("rounded-lg",e.className)})},h1:e=>(0,o.jsx)(c,{as:"h1",...e}),h2:e=>(0,o.jsx)(c,{as:"h2",...e}),h3:e=>(0,o.jsx)(c,{as:"h3",...e}),h4:e=>(0,o.jsx)(c,{as:"h4",...e}),h5:e=>(0,o.jsx)(c,{as:"h5",...e}),h6:e=>(0,o.jsx)(c,{as:"h6",...e}),table:function(e){return(0,o.jsx)("div",{className:"relative overflow-auto prose-no-margin my-6",children:(0,o.jsx)("table",{...e})})},Callout:i.P};function f(e,r,t=p.a){return async function({href:n,...l}){if(n&&n.startsWith(".")){let t=e.getPageByHref(n,{dir:r.file.dirname});t&&(n=t.hash?`${t.page.url}#${t.hash}`:t.page.url)}return(0,o.jsx)(t,{href:n,...l})}}},31035:(e,r,t)=>{t.d(r,{CodeBlock:()=>p,Pre:()=>u});var o=t(60687),n=t(62688);let l=(0,n.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),a=(0,n.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var i=t(43210),s=t(82348),d=t(88968),c=t(22880);let u=(0,i.forwardRef)(({className:e,...r},t)=>(0,o.jsx)("pre",{ref:t,className:(0,s.QP)("p-4 focus-visible:outline-none",e),...r,children:r.children}));u.displayName="Pre";let p=(0,i.forwardRef)(({title:e,allowCopy:r=!0,keepBackground:t=!1,icon:n,viewportProps:l,...a},c)=>{let u=(0,i.useRef)(null),p=(0,i.useCallback)(()=>{let e=u.current?.getElementsByTagName("pre").item(0);if(!e)return;let r=e.cloneNode(!0);r.querySelectorAll(".nd-copy-ignore").forEach(e=>{e.remove()}),navigator.clipboard.writeText(r.textContent??"")},[]);return(0,o.jsxs)("figure",{ref:c,...a,className:(0,s.QP)("not-prose group fd-codeblock relative my-4 overflow-hidden rounded-lg border bg-fd-secondary/50 text-sm",t&&"bg-(--shiki-light-bg) dark:bg-(--shiki-dark-bg)",a.className),children:[e?(0,o.jsxs)("div",{className:"flex items-center gap-2 border-b bg-fd-muted px-4 py-1.5",children:[n?(0,o.jsx)("div",{className:"text-fd-muted-foreground [&_svg]:size-3.5",dangerouslySetInnerHTML:"string"==typeof n?{__html:n}:void 0,children:"string"!=typeof n?n:null}):null,(0,o.jsx)("figcaption",{className:"flex-1 truncate text-fd-muted-foreground",children:e}),r?(0,o.jsx)(f,{className:"-me-2",onCopy:p}):null]}):r&&(0,o.jsx)(f,{className:"absolute right-2 top-2 z-[2] backdrop-blur-md",onCopy:p}),(0,o.jsxs)(d.FK,{ref:u,dir:"ltr",children:[(0,o.jsx)(d.Gl,{...l,className:(0,s.QP)("max-h-[600px]",l?.className),children:a.children}),(0,o.jsx)(d.$H,{orientation:"horizontal"})]})]})});function f({className:e,onCopy:r,...t}){let[n,d]=function(e){let[r,t]=(0,i.useState)(!1),o=(0,i.useRef)(null),n=(0,i.useRef)(e);return n.current=e,[r,(0,i.useCallback)(()=>{o.current&&window.clearTimeout(o.current),o.current=window.setTimeout(()=>{t(!1)},1500),n.current(),t(!0)},[])]}(r);return(0,o.jsxs)("button",{type:"button",className:(0,s.QP)((0,c.r)({color:"ghost"}),"transition-opacity group-hover:opacity-100 [&_svg]:size-3.5",!n&&"[@media(hover:hover)]:opacity-0",e),"aria-label":n?"Copied Text":"Copy Text",onClick:d,...t,children:[(0,o.jsx)(l,{className:(0,s.QP)("transition-transform",!n&&"scale-0")}),(0,o.jsx)(a,{className:(0,s.QP)("absolute transition-transform",n&&"scale-0")})]})}p.displayName="CodeBlock"},77379:(e,r,t)=>{t.d(r,{Nt:()=>S,Ke:()=>E,R6:()=>P});var o=t(60687),n=t(43210),l=t(70569),a=t(11273),i=t(65551),s=t(66156),d=t(98599),c=t(14163),u=t(46059),p=t(96963),f="Collapsible",[m,h]=(0,a.A)(f),[v,w]=m(f),g=n.forwardRef((e,r)=>{let{__scopeCollapsible:t,open:l,defaultOpen:a,disabled:s,onOpenChange:d,...u}=e,[m,h]=(0,i.i)({prop:l,defaultProp:a??!1,onChange:d,caller:f});return(0,o.jsx)(v,{scope:t,disabled:s,contentId:(0,p.B)(),open:m,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),children:(0,o.jsx)(c.sG.div,{"data-state":R(m),"data-disabled":s?"":void 0,...u,ref:r})})});g.displayName=f;var b="CollapsibleTrigger",x=n.forwardRef((e,r)=>{let{__scopeCollapsible:t,...n}=e,a=w(b,t);return(0,o.jsx)(c.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":R(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:r,onClick:(0,l.m)(e.onClick,a.onOpenToggle)})});x.displayName=b;var C="CollapsibleContent",y=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=w(C,e.__scopeCollapsible);return(0,o.jsx)(u.C,{present:t||l.open,children:({present:e})=>(0,o.jsx)(j,{...n,ref:r,present:e})})});y.displayName=C;var j=n.forwardRef((e,r)=>{let{__scopeCollapsible:t,present:l,children:a,...i}=e,u=w(C,t),[p,f]=n.useState(l),m=n.useRef(null),h=(0,d.s)(r,m),v=n.useRef(0),g=v.current,b=n.useRef(0),x=b.current,y=u.open||p,j=n.useRef(y),N=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.N)(()=>{let e=m.current;if(e){N.current=N.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let r=e.getBoundingClientRect();v.current=r.height,b.current=r.width,j.current||(e.style.transitionDuration=N.current.transitionDuration,e.style.animationName=N.current.animationName),f(l)}},[u.open,l]),(0,o.jsx)(c.sG.div,{"data-state":R(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!y,...i,ref:h,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":x?`${x}px`:void 0,...e.style},children:y&&a})});function R(e){return e?"open":"closed"}var N=t(82348);let S=g,P=x,E=(0,n.forwardRef)(({children:e,...r},t)=>{let[l,a]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{a(!0)},[]),(0,o.jsx)(y,{ref:t,...r,className:(0,N.QP)("overflow-hidden",l&&"data-[state=closed]:animate-fd-collapsible-up data-[state=open]:animate-fd-collapsible-down",r.className),children:e})});E.displayName=y.displayName},78272:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},88968:(e,r,t)=>{t.d(r,{FK:()=>G,$H:()=>q,Gl:()=>$});var o=t(60687),n=t(43210),l=t(14163),a=t(46059),i=t(11273),s=t(98599),d=t(13495),c=t(43),u=t(66156),p=t(70569),f="ScrollArea",[m,h]=(0,i.A)(f),[v,w]=m(f),g=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:a="hover",dir:i,scrollHideDelay:d=600,...u}=e,[p,f]=n.useState(null),[m,h]=n.useState(null),[w,g]=n.useState(null),[b,x]=n.useState(null),[C,y]=n.useState(null),[j,R]=n.useState(0),[N,S]=n.useState(0),[P,E]=n.useState(!1),[k,T]=n.useState(!1),D=(0,s.s)(r,e=>f(e)),A=(0,c.jH)(i);return(0,o.jsx)(v,{scope:t,type:a,dir:A,scrollHideDelay:d,scrollArea:p,viewport:m,onViewportChange:h,content:w,onContentChange:g,scrollbarX:b,onScrollbarXChange:x,scrollbarXEnabled:P,onScrollbarXEnabledChange:E,scrollbarY:C,onScrollbarYChange:y,scrollbarYEnabled:k,onScrollbarYEnabledChange:T,onCornerWidthChange:R,onCornerHeightChange:S,children:(0,o.jsx)(l.sG.div,{dir:A,...u,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":j+"px","--radix-scroll-area-corner-height":N+"px",...e.style}})})});g.displayName=f;var b="ScrollAreaViewport",x=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:a,nonce:i,...d}=e,c=w(b,t),u=n.useRef(null),p=(0,s.s)(r,u,c.onViewportChange);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,o.jsx)(l.sG.div,{"data-radix-scroll-area-viewport":"",...d,ref:p,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,o.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});x.displayName=b;var C="ScrollAreaScrollbar",y=n.forwardRef((e,r)=>{let{forceMount:t,...l}=e,a=w(C,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=a,d="horizontal"===e.orientation;return n.useEffect(()=>(d?i(!0):s(!0),()=>{d?i(!1):s(!1)}),[d,i,s]),"hover"===a.type?(0,o.jsx)(j,{...l,ref:r,forceMount:t}):"scroll"===a.type?(0,o.jsx)(R,{...l,ref:r,forceMount:t}):"auto"===a.type?(0,o.jsx)(N,{...l,ref:r,forceMount:t}):"always"===a.type?(0,o.jsx)(S,{...l,ref:r}):null});y.displayName=C;var j=n.forwardRef((e,r)=>{let{forceMount:t,...l}=e,i=w(C,e.__scopeScrollArea),[s,d]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),d(!0)},o=()=>{r=window.setTimeout(()=>d(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[i.scrollArea,i.scrollHideDelay]),(0,o.jsx)(a.C,{present:t||s,children:(0,o.jsx)(N,{"data-state":s?"visible":"hidden",...l,ref:r})})}),R=n.forwardRef((e,r)=>{var t,l;let{forceMount:i,...s}=e,d=w(C,e.__scopeScrollArea),c="horizontal"===e.orientation,u=M(()=>m("SCROLL_END"),100),[f,m]=(t="hidden",l={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,r)=>l[e][r]??e,t));return n.useEffect(()=>{if("idle"===f){let e=window.setTimeout(()=>m("HIDE"),d.scrollHideDelay);return()=>window.clearTimeout(e)}},[f,d.scrollHideDelay,m]),n.useEffect(()=>{let e=d.viewport,r=c?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(m("SCROLL"),u()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[d.viewport,c,m,u]),(0,o.jsx)(a.C,{present:i||"hidden"!==f,children:(0,o.jsx)(S,{"data-state":"hidden"===f?"hidden":"visible",...s,ref:r,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>m("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>m("POINTER_LEAVE"))})})}),N=n.forwardRef((e,r)=>{let t=w(C,e.__scopeScrollArea),{forceMount:l,...i}=e,[s,d]=n.useState(!1),c="horizontal"===e.orientation,u=M(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;d(c?e:r)}},10);return X(t.viewport,u),X(t.content,u),(0,o.jsx)(a.C,{present:l||s,children:(0,o.jsx)(S,{"data-state":s?"visible":"hidden",...i,ref:r})})}),S=n.forwardRef((e,r)=>{let{orientation:t="vertical",...l}=e,a=w(C,e.__scopeScrollArea),i=n.useRef(null),s=n.useRef(0),[d,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=B(d.viewport,d.content),p={...l,sizes:d,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function f(e,r){return function(e,r,t,o="ltr"){let n=W(t),l=r||n/2,a=t.scrollbar.paddingStart+l,i=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return Q([a,i],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,s.current,d,r)}return"horizontal"===t?(0,o.jsx)(P,{...p,ref:r,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=O(a.viewport.scrollLeft,d,a.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=f(e,a.dir))}}):"vertical"===t?(0,o.jsx)(E,{...p,ref:r,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=O(a.viewport.scrollTop,d);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=f(e))}}):null}),P=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:l,...a}=e,i=w(C,e.__scopeScrollArea),[d,c]=n.useState(),u=n.useRef(null),p=(0,s.s)(r,u,i.onScrollbarXChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,o.jsx)(D,{"data-orientation":"horizontal",...a,ref:p,sizes:t,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":W(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{u.current&&i.viewport&&d&&l({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:U(d.paddingLeft),paddingEnd:U(d.paddingRight)}})}})}),E=n.forwardRef((e,r)=>{let{sizes:t,onSizesChange:l,...a}=e,i=w(C,e.__scopeScrollArea),[d,c]=n.useState(),u=n.useRef(null),p=(0,s.s)(r,u,i.onScrollbarYChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,o.jsx)(D,{"data-orientation":"vertical",...a,ref:p,sizes:t,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":W(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{u.current&&i.viewport&&d&&l({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:U(d.paddingTop),paddingEnd:U(d.paddingBottom)}})}})}),[k,T]=m(C),D=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:a,hasThumb:i,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:f,onThumbPositionChange:m,onDragScroll:h,onWheelScroll:v,onResize:g,...b}=e,x=w(C,t),[y,j]=n.useState(null),R=(0,s.s)(r,e=>j(e)),N=n.useRef(null),S=n.useRef(""),P=x.viewport,E=a.content-a.viewport,T=(0,d.c)(v),D=(0,d.c)(m),A=M(g,10);function _(e){N.current&&h({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return n.useEffect(()=>{let e=e=>{let r=e.target;y?.contains(r)&&T(e,E)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[P,y,E,T]),n.useEffect(D,[a,D]),X(y,A),X(x.content,A),(0,o.jsx)(k,{scope:t,scrollbar:y,hasThumb:i,onThumbChange:(0,d.c)(c),onThumbPointerUp:(0,d.c)(u),onThumbPositionChange:D,onThumbPointerDown:(0,d.c)(f),children:(0,o.jsx)(l.sG.div,{...b,ref:R,style:{position:"absolute",...b.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=y.getBoundingClientRect(),S.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),_(e))}),onPointerMove:(0,p.m)(e.onPointerMove,_),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=S.current,x.viewport&&(x.viewport.style.scrollBehavior=""),N.current=null})})})}),A="ScrollAreaThumb",_=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=T(A,e.__scopeScrollArea);return(0,o.jsx)(a.C,{present:t||l.hasThumb,children:(0,o.jsx)(L,{ref:r,...n})})}),L=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:a,...i}=e,d=w(A,t),c=T(A,t),{onThumbPositionChange:u}=c,f=(0,s.s)(r,e=>c.onThumbChange(e)),m=n.useRef(void 0),h=M(()=>{m.current&&(m.current(),m.current=void 0)},100);return n.useEffect(()=>{let e=d.viewport;if(e){let r=()=>{h(),m.current||(m.current=F(e,u),u())};return u(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[d.viewport,h,u]),(0,o.jsx)(l.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;c.onThumbPointerDown({x:t,y:o})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});_.displayName=A;var I="ScrollAreaCorner",z=n.forwardRef((e,r)=>{let t=w(I,e.__scopeScrollArea),n=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&n?(0,o.jsx)(H,{...e,ref:r}):null});z.displayName=I;var H=n.forwardRef((e,r)=>{let{__scopeScrollArea:t,...a}=e,i=w(I,t),[s,d]=n.useState(0),[c,u]=n.useState(0),p=!!(s&&c);return X(i.scrollbarX,()=>{let e=i.scrollbarX?.offsetHeight||0;i.onCornerHeightChange(e),u(e)}),X(i.scrollbarY,()=>{let e=i.scrollbarY?.offsetWidth||0;i.onCornerWidthChange(e),d(e)}),p?(0,o.jsx)(l.sG.div,{...a,ref:r,style:{width:s,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function U(e){return e?parseInt(e,10):0}function B(e,r){let t=e/r;return isNaN(t)?0:t}function W(e){let r=B(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function O(e,r,t="ltr"){let o=W(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,a=r.content-r.viewport,i=function(e,[r,t]){return Math.min(t,Math.max(r,e))}(e,"ltr"===t?[0,a]:[-1*a,0]);return Q([0,a],[0,l-o])(i)}function Q(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var F=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},o=0;return!function n(){let l={left:e.scrollLeft,top:e.scrollTop},a=t.left!==l.left,i=t.top!==l.top;(a||i)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function M(e,r){let t=(0,d.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(t,r)},[t,r])}function X(e,r){let t=(0,d.c)(r);(0,u.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var Y=t(82348);let G=n.forwardRef(({className:e,children:r,...t},n)=>(0,o.jsxs)(g,{ref:n,className:(0,Y.QP)("overflow-hidden",e),...t,children:[r,(0,o.jsx)(z,{}),(0,o.jsx)(q,{orientation:"vertical"})]}));G.displayName=g.displayName;let $=n.forwardRef(({className:e,children:r,...t},n)=>(0,o.jsx)(x,{ref:n,className:(0,Y.QP)("size-full rounded-[inherit]",e),...t,children:r}));$.displayName=x.displayName;let q=n.forwardRef(({className:e,orientation:r="vertical",...t},n)=>(0,o.jsx)(y,{ref:n,orientation:r,className:(0,Y.QP)("flex select-none data-[state=hidden]:animate-fd-fade-out","vertical"===r&&"h-full w-1.5","horizontal"===r&&"h-1.5 flex-col",e),...t,children:(0,o.jsx)(_,{className:"relative flex-1 rounded-full bg-fd-border"})}));q.displayName=y.displayName}};