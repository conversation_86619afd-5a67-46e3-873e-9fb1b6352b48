"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_orama-cloud-USLSOSXS_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: () => (/* binding */ removeUndefined)\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstS0FPRU1DVEkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXGZ1bWFkb2NzLWNvcmVcXGRpc3RcXGNodW5rLUtBT0VNQ1RJLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9yZW1vdmUtdW5kZWZpbmVkLnRzXG5mdW5jdGlvbiByZW1vdmVVbmRlZmluZWQodmFsdWUsIGRlZXAgPSBmYWxzZSkge1xuICBjb25zdCBvYmogPSB2YWx1ZTtcbiAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgIGlmIChvYmpba2V5XSA9PT0gdm9pZCAwKSBkZWxldGUgb2JqW2tleV07XG4gICAgaWYgKGRlZXAgJiYgdHlwZW9mIG9ialtrZXldID09PSBcIm9iamVjdFwiICYmIG9ialtrZXldICE9PSBudWxsKSB7XG4gICAgICByZW1vdmVVbmRlZmluZWQob2JqW2tleV0sIGRlZXApO1xuICAgIH0gZWxzZSBpZiAoZGVlcCAmJiBBcnJheS5pc0FycmF5KG9ialtrZXldKSkge1xuICAgICAgb2JqW2tleV0uZm9yRWFjaCgodikgPT4gcmVtb3ZlVW5kZWZpbmVkKHYsIGRlZXApKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQge1xuICByZW1vdmVVbmRlZmluZWRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/orama-cloud-USLSOSXS.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/orama-cloud-USLSOSXS.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, tag, options) {\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {} } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    if (index === \"crawler\") {\n      for (const hit of result2.hits) {\n        const doc = hit.document;\n        list.push(\n          {\n            id: hit.id,\n            type: \"page\",\n            content: doc.title,\n            url: doc.path\n          },\n          {\n            id: \"page\" + hit.id,\n            type: \"text\",\n            content: doc.content,\n            url: doc.path\n          }\n        );\n      }\n      return list;\n    }\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3Qvb3JhbWEtY2xvdWQtVVNMU09TWFMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRTZCO0FBQ0E7O0FBRTdCO0FBQ0E7QUFDQTtBQUNBLFVBQVUsc0RBQXNEO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1FQUFlO0FBQzFCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxRQUFRLEdBQUcsZUFBZTtBQUMzRCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFHRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcZnVtYWRvY3MtY29yZVxcZGlzdFxcb3JhbWEtY2xvdWQtVVNMU09TWFMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgcmVtb3ZlVW5kZWZpbmVkXG59IGZyb20gXCIuL2NodW5rLUtBT0VNQ1RJLmpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLU1MS0dBQk1LLmpzXCI7XG5cbi8vIHNyYy9zZWFyY2gvY2xpZW50L29yYW1hLWNsb3VkLnRzXG5hc3luYyBmdW5jdGlvbiBzZWFyY2hEb2NzKHF1ZXJ5LCB0YWcsIG9wdGlvbnMpIHtcbiAgY29uc3QgbGlzdCA9IFtdO1xuICBjb25zdCB7IGluZGV4ID0gXCJkZWZhdWx0XCIsIGNsaWVudCwgcGFyYW1zOiBleHRyYVBhcmFtcyA9IHt9IH0gPSBvcHRpb25zO1xuICBpZiAoaW5kZXggPT09IFwiY3Jhd2xlclwiKSB7XG4gICAgY29uc3QgcmVzdWx0MiA9IGF3YWl0IGNsaWVudC5zZWFyY2goe1xuICAgICAgLi4uZXh0cmFQYXJhbXMsXG4gICAgICB0ZXJtOiBxdWVyeSxcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGNhdGVnb3J5OiB0YWcgPyB7XG4gICAgICAgICAgZXE6IHRhZy5zbGljZSgwLCAxKS50b1VwcGVyQ2FzZSgpICsgdGFnLnNsaWNlKDEpXG4gICAgICAgIH0gOiB2b2lkIDAsXG4gICAgICAgIC4uLmV4dHJhUGFyYW1zLndoZXJlXG4gICAgICB9LFxuICAgICAgbGltaXQ6IDEwXG4gICAgfSk7XG4gICAgaWYgKCFyZXN1bHQyKSByZXR1cm4gbGlzdDtcbiAgICBpZiAoaW5kZXggPT09IFwiY3Jhd2xlclwiKSB7XG4gICAgICBmb3IgKGNvbnN0IGhpdCBvZiByZXN1bHQyLmhpdHMpIHtcbiAgICAgICAgY29uc3QgZG9jID0gaGl0LmRvY3VtZW50O1xuICAgICAgICBsaXN0LnB1c2goXG4gICAgICAgICAge1xuICAgICAgICAgICAgaWQ6IGhpdC5pZCxcbiAgICAgICAgICAgIHR5cGU6IFwicGFnZVwiLFxuICAgICAgICAgICAgY29udGVudDogZG9jLnRpdGxlLFxuICAgICAgICAgICAgdXJsOiBkb2MucGF0aFxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgaWQ6IFwicGFnZVwiICsgaGl0LmlkLFxuICAgICAgICAgICAgdHlwZTogXCJ0ZXh0XCIsXG4gICAgICAgICAgICBjb250ZW50OiBkb2MuY29udGVudCxcbiAgICAgICAgICAgIHVybDogZG9jLnBhdGhcbiAgICAgICAgICB9XG4gICAgICAgICk7XG4gICAgICB9XG4gICAgICByZXR1cm4gbGlzdDtcbiAgICB9XG4gIH1cbiAgY29uc3QgcGFyYW1zID0ge1xuICAgIC4uLmV4dHJhUGFyYW1zLFxuICAgIHRlcm06IHF1ZXJ5LFxuICAgIHdoZXJlOiByZW1vdmVVbmRlZmluZWQoe1xuICAgICAgdGFnLFxuICAgICAgLi4uZXh0cmFQYXJhbXMud2hlcmVcbiAgICB9KSxcbiAgICBncm91cEJ5OiB7XG4gICAgICBwcm9wZXJ0aWVzOiBbXCJwYWdlX2lkXCJdLFxuICAgICAgbWF4UmVzdWx0OiA3LFxuICAgICAgLi4uZXh0cmFQYXJhbXMuZ3JvdXBCeVxuICAgIH1cbiAgfTtcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnNlYXJjaChwYXJhbXMpO1xuICBpZiAoIXJlc3VsdCB8fCAhcmVzdWx0Lmdyb3VwcykgcmV0dXJuIGxpc3Q7XG4gIGZvciAoY29uc3QgaXRlbSBvZiByZXN1bHQuZ3JvdXBzKSB7XG4gICAgbGV0IGFkZGVkSGVhZCA9IGZhbHNlO1xuICAgIGZvciAoY29uc3QgaGl0IG9mIGl0ZW0ucmVzdWx0KSB7XG4gICAgICBjb25zdCBkb2MgPSBoaXQuZG9jdW1lbnQ7XG4gICAgICBpZiAoIWFkZGVkSGVhZCkge1xuICAgICAgICBsaXN0LnB1c2goe1xuICAgICAgICAgIGlkOiBkb2MucGFnZV9pZCxcbiAgICAgICAgICB0eXBlOiBcInBhZ2VcIixcbiAgICAgICAgICBjb250ZW50OiBkb2MudGl0bGUsXG4gICAgICAgICAgdXJsOiBkb2MudXJsXG4gICAgICAgIH0pO1xuICAgICAgICBhZGRlZEhlYWQgPSB0cnVlO1xuICAgICAgfVxuICAgICAgbGlzdC5wdXNoKHtcbiAgICAgICAgaWQ6IGRvYy5pZCxcbiAgICAgICAgY29udGVudDogZG9jLmNvbnRlbnQsXG4gICAgICAgIHR5cGU6IGRvYy5jb250ZW50ID09PSBkb2Muc2VjdGlvbiA/IFwiaGVhZGluZ1wiIDogXCJ0ZXh0XCIsXG4gICAgICAgIHVybDogZG9jLnNlY3Rpb25faWQgPyBgJHtkb2MudXJsfSMke2RvYy5zZWN0aW9uX2lkfWAgOiBkb2MudXJsXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGxpc3Q7XG59XG5leHBvcnQge1xuICBzZWFyY2hEb2NzXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/orama-cloud-USLSOSXS.js\n"));

/***/ })

}]);