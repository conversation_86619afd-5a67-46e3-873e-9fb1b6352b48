"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[996],{1615:(e,t,a)=>{a.d(t,{fetchDocs:()=>s}),a(9189);var r=new Map;async function s(e,t,a,s){let c=new URLSearchParams;c.set("query",e),t&&c.set("locale",t),a&&c.set("tag",a);let n=`${s.api??"/api/search"}?${c}`,i=r.get(n);if(i)return i;let h=await fetch(n);if(!h.ok)throw Error(await h.text());let l=await h.json();return r.set(n,l),l}}}]);