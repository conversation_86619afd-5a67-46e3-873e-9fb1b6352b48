(()=>{var e={};e.id=309,e.ids=[309],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3512:(e,t,r)=>{"use strict";r.d(t,{TOCItems:()=>D,TOCScrollArea:()=>p,Toc:()=>c,k:()=>m});var o=r(60687),n=r(92534),s=r(43210),u=r(82348),i=r(72527),a=r(23149),l=r(88968),d=r(353);function c(e){let{toc:t}=(0,d.v)();return(0,o.jsx)("div",{id:"nd-toc",...e,className:(0,u.QP)("sticky top-[calc(var(--fd-banner-height)+var(--fd-nav-height))] h-(--fd-toc-height) pb-2 pt-12",t,e.className),style:{...e.style,"--fd-toc-height":"calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"},children:(0,o.jsx)("div",{className:"flex h-full w-(--fd-toc-width) max-w-full flex-col gap-3 pe-4",children:e.children})})}function m(){let{text:e}=(0,i.s9)();return(0,o.jsx)("div",{className:"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground",children:e.tocNoHeadings})}function p({isMenu:e,...t}){let r=(0,s.useRef)(null);return(0,o.jsx)(l.FK,{...t,className:(0,u.QP)("flex flex-col ps-px",t.className),children:(0,o.jsx)(l.Gl,{ref:r,className:(0,u.QP)("relative min-h-0 text-sm",e&&"[mask-image:linear-gradient(to_bottom,transparent,white_16px,white_calc(100%-16px),transparent)] px-4 md:px-6 py-2"),children:(0,o.jsx)(n.N2,{containerRef:r,children:t.children})})})}function D({items:e}){let t=(0,s.useRef)(null);return 0===e.length?(0,o.jsx)(m,{}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.j,{containerRef:t,className:"absolute top-(--fd-top) h-(--fd-height) w-px bg-fd-primary transition-all"}),(0,o.jsx)("div",{ref:t,className:"flex flex-col border-s border-fd-foreground/10",children:e.map(e=>(0,o.jsx)(f,{item:e},e.url))})]})}function f({item:e}){return(0,o.jsx)(n.Cz,{href:e.url,className:(0,u.QP)("prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",e.depth<=2&&"ps-3",3===e.depth&&"ps-6",e.depth>=4&&"ps-8"),children:e.title})}},8801:(e,t,r)=>{"use strict";r.d(t,{BaseLinkItem:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call BaseLinkItem() from the server but BaseLinkItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\links.js","BaseLinkItem")},10555:(e,t,r)=>{"use strict";r.d(t,{Breadcrumb:()=>T,Footer:()=>N,LastUpdate:()=>B,PageArticle:()=>w,PageBody:()=>j,TocPopover:()=>y,TocPopoverContent:()=>E,TocPopoverTrigger:()=>v});var o=r(60687),n=r(43210),s=r(78272),u=r(62688);let i=(0,u.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),a=(0,u.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var l=r(46250),d=r(82348),c=r(72527),m=r(79667),p=r(40463),D=r(66218),f=r(96768),h=r(353),F=r(349),C=r(24249),x=r(77379),b=r(92534);let g=(0,D.q6)("TocPopoverContext");function v({items:e,...t}){let{text:r}=(0,c.s9)(),{open:u}=g.use(),i=b.R3(),a=(0,n.useMemo)(()=>e.findIndex(e=>i===e.url.slice(1)),[e,i]),l=(0,m.L)().at(-1),p=-1!==a&&!u;return(0,o.jsxs)(x.R6,{...t,className:(0,d.QP)("flex flex-row items-center text-sm text-fd-muted-foreground gap-2.5 px-4 py-2.5 text-start focus-visible:outline-none [&_svg]:shrink-0 [&_svg]:size-4 md:px-6",t.className),children:[(0,o.jsx)(A,{value:(a+1)/e.length,max:1,className:(0,d.QP)(u&&"text-fd-primary")}),(0,o.jsxs)("span",{className:(0,d.QP)("grid flex-1 *:row-start-1 *:col-start-1",u&&"text-fd-foreground"),children:[(0,o.jsx)("span",{className:(0,d.QP)("truncate transition-all",p&&"opacity-0 -translate-y-full pointer-events-none"),children:l?.name??r.toc}),(0,o.jsx)("span",{className:(0,d.QP)("truncate transition-all",!p&&"opacity-0 translate-y-full pointer-events-none"),children:e[a]?.title})]}),(0,o.jsx)(s.A,{className:(0,d.QP)("transition-transform",u&&"rotate-180")})]})}function A({value:e,strokeWidth:t=2,size:r=24,min:n=0,max:s=100,...u}){let i=e<n?n:e>s?s:e,a=(r-t)/2,l=2*Math.PI*a,d=i/s*l,c={cx:r/2,cy:r/2,r:a,fill:"none",strokeWidth:t};return(0,o.jsxs)("svg",{role:"progressbar",viewBox:`0 0 ${r} ${r}`,"aria-valuenow":i,"aria-valuemin":n,"aria-valuemax":s,...u,children:[(0,o.jsx)("circle",{...c,className:"stroke-current/25"}),(0,o.jsx)("circle",{...c,stroke:"currentColor",strokeDasharray:l,strokeDashoffset:l-d,strokeLinecap:"round",transform:`rotate(-90 ${r/2} ${r/2})`,className:"transition-all"})]})}function E(e){return(0,o.jsx)(x.Ke,{"data-toc-popover":"",...e,className:(0,d.QP)("flex flex-col max-h-[50vh]",e.className),children:e.children})}function y(e){let t=(0,n.useRef)(null),[r,s]=(0,n.useState)(!1),u=(0,p.c)(),{tocNav:i}=(0,h.v)(),{isTransparent:a}=(0,h.h)();return(0,C.J)(e=>{r&&t.current&&!t.current.contains(e.target)&&s(!1)}),(0,o.jsx)("div",{...e,className:(0,d.QP)("sticky overflow-visible z-10",i,e.className),style:{...e.style,top:"calc(var(--fd-banner-height) + var(--fd-nav-height))"},children:(0,o.jsx)(g.Provider,{value:(0,n.useMemo)(()=>({open:r,setOpen:s}),[s,r]),children:(0,o.jsx)(x.Nt,{open:r,onOpenChange:s,asChild:!0,children:(0,o.jsx)("header",{ref:t,id:"nd-tocnav",...e,className:(0,d.QP)("border-b border-fd-foreground/10 backdrop-blur-sm transition-colors",(!a||r)&&"bg-fd-background/80",r&&"shadow-lg",u.open&&"max-md:hidden"),children:e.children})})})})}function j(e){let{page:t}=(0,h.v)();return(0,o.jsx)("div",{id:"nd-page",...e,className:(0,d.QP)("flex w-full min-w-0 flex-col",t,e.className),children:e.children})}function w(e){let{article:t}=(0,h.v)();return(0,o.jsx)("article",{...e,className:(0,d.QP)("flex w-full flex-1 flex-col gap-6 px-4 md:px-6 pt-8 md:pt-12 xl:px-12 xl:mx-auto",t,e.className),children:e.children})}function B(e){let{text:t}=(0,c.s9)(),[r,s]=(0,n.useState)("");return(0,o.jsxs)("p",{className:"text-sm text-fd-muted-foreground",children:[t.lastUpdate," ",r]})}let P=new WeakMap;function N({items:e}){let{root:t}=(0,m.t)(),r=(0,D.a8)(),{previous:s,next:u}=(0,n.useMemo)(()=>{if(e)return e;let o=P.get(t)??function e(t){let r=[];return t.forEach(t=>{if("folder"===t.type){t.index&&r.push(t.index),r.push(...e(t.children));return}"page"!==t.type||t.external||r.push(t)}),r}(t.children);P.set(t,o);let n=o.findIndex(e=>(0,F.$)(e.url,r,!1));return -1===n?{}:{previous:o[n-1],next:o[n+1]}},[e,r,t]);return(0,o.jsxs)("div",{className:(0,d.QP)("@container grid gap-4 pb-6",s&&u?"grid-cols-2":"grid-cols-1"),children:[s?(0,o.jsx)(I,{item:s,index:0}):null,u?(0,o.jsx)(I,{item:u,index:1}):null]})}function I({item:e,index:t}){let{text:r}=(0,c.s9)();return(0,o.jsxs)(l.default,{href:e.url,className:(0,d.QP)("flex flex-col gap-2 rounded-lg border p-4 text-sm transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground @max-lg:col-span-full",1===t&&"text-end"),children:[(0,o.jsxs)("div",{className:(0,d.QP)("inline-flex items-center gap-1.5 font-medium",1===t&&"flex-row-reverse"),children:[(0,o.jsx)(0===t?i:a,{className:"-mx-1 size-4 shrink-0 rtl:rotate-180"}),(0,o.jsx)("p",{children:e.name})]}),(0,o.jsx)("p",{className:"text-fd-muted-foreground truncate",children:e.description??(0===t?r.previousPage:r.nextPage)})]})}function T(e){let t=(0,m.L)(),{root:r}=(0,m.t)(),s=(0,n.useMemo)(()=>(0,f.Pp)(r,t,{includePage:e.includePage??!1,...e}),[e,t,r]);return 0===s.length?null:(0,o.jsx)("div",{className:"flex flex-row items-center gap-1.5 text-[15px] text-fd-muted-foreground",children:s.map((e,t)=>{let r=(0,d.QP)("truncate",t===s.length-1&&"text-fd-primary font-medium");return(0,o.jsxs)(n.Fragment,{children:[0!==t&&(0,o.jsx)("span",{className:"text-fd-foreground/30",children:"/"}),e.url?(0,o.jsx)(l.default,{href:e.url,className:(0,d.QP)(r,"transition-opacity hover:opacity-80"),children:e.name}):(0,o.jsx)("span",{className:r,children:e.name})]},t)})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20057:(e,t,r)=>{"use strict";r.d(t,{Breadcrumb:()=>c,Footer:()=>d,LastUpdate:()=>l,PageArticle:()=>a,PageBody:()=>i,TocPopover:()=>u,TocPopoverContent:()=>s,TocPopoverTrigger:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call TocPopoverTrigger() from the server but TocPopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","TocPopoverTrigger"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call TocPopoverContent() from the server but TocPopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","TocPopoverContent"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call TocPopover() from the server but TocPopover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","TocPopover"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call PageBody() from the server but PageBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","PageBody"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call PageArticle() from the server but PageArticle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","PageArticle"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call LastUpdate() from the server but LastUpdate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","LastUpdate"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","Footer"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\page-client.js","Breadcrumb")},21241:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>I});var o=r(37413),n=r(61120),s=r(85752),u=r(77300),i=r(37609),a=r(8974),l=r(39614);let d=(0,r(26373).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);var c=r(47909),m=r(88598),p=r(8801),D=r(78941),f=r(24274),h=r(78189);let F=(e,t)=>t.icon?{...e,icon:(0,o.jsx)("div",{className:"rounded-md border bg-fd-secondary p-1 shadow-md [&_svg]:size-5",children:t.icon})}:e;function C(e,{transform:t=F}={}){return function e(r){let o=[];if(r.root){let e=function e(t,r=new Set){for(let o of(t.index&&r.add(t.index.url),t.children))"page"!==o.type||o.external||r.add(o.url),"folder"===o.type&&e(o,r);return r}(r);if(e.size>0){let n={url:e.values().next().value??"",title:r.name,icon:r.icon,description:r.description,urls:e},s=t?t(n,r):n;s&&o.push(s)}}for(let t of r.children)"folder"===t.type&&o.push(...e(t));return o}(e)}let x={"--fd-layout-offset":"max(calc(50vw - var(--fd-layout-width) / 2), 0px)"};function b({item:e,...t}){return"menu"===e.type?(0,o.jsxs)(u.SidebarFolder,{...t,children:[e.url?(0,o.jsxs)(u.SidebarFolderLink,{href:e.url,children:[e.icon,e.text]}):(0,o.jsxs)(u.SidebarFolderTrigger,{children:[e.icon,e.text]}),(0,o.jsx)(u.SidebarFolderContent,{children:e.items.map((e,t)=>(0,o.jsx)(b,{item:e},t))})]}):"custom"===e.type?(0,o.jsx)("div",{...t,children:e.children}):(0,o.jsx)(u.SidebarItem,{href:e.url,icon:e.icon,external:e.external,...t,children:e.text})}var g=r(39987),v=r(3231),A=r(24703),E=r(69767),y=r(36163);function j({tabMode:e="sidebar",nav:{transparentMode:t,...r}={},sidebar:{collapsible:m=!0,tabs:p,banner:h,footer:F,components:y,...j}={},i18n:B=!1,disableThemeSwitch:P=!1,themeSwitch:N={enabled:!P},searchToggle:I,...T}){let k=r.mode??"auto",S=(0,s.CY)(T.links??[],T.githubUrl),R=(0,n.useMemo)(()=>{var e,t;return e=p,t=T.tree,(Array.isArray(e)?e:"object"==typeof e?C(t,e):!1!==e?C(t):void 0)??[]},[p,T.tree]),_=m?u.CollapsibleSidebar:u.Sidebar,Q=(0,a.QP)("[--fd-nav-height:calc(var(--spacing)*14)] [--fd-tocnav-height:36px] md:[--fd-sidebar-width:286px] xl:[--fd-toc-width:286px] xl:[--fd-tocnav-height:0px]",R.length>0&&"navbar"===e&&"lg:[--fd-nav-height:calc(var(--spacing)*24)]"),U={tocNav:(0,a.QP)("xl:hidden"),toc:(0,a.QP)("max-xl:hidden"),page:(0,a.QP)("mt-(--fd-nav-height)")};return(0,o.jsx)(i.TreeContextProvider,{tree:T.tree,children:(0,o.jsx)(v.NavProvider,{transparentMode:t,children:(0,o.jsxs)("main",{id:"nd-docs-layout",...T.containerProps,className:(0,a.QP)("flex w-full flex-1 flex-row pe-(--fd-layout-offset)",Q,T.containerProps?.className),style:{...x,...T.containerProps?.style},children:[(0,o.jsxs)(_,{...j,className:(0,a.QP)("md:ps-(--fd-layout-offset)","top"===k?"bg-transparent":"md:[--fd-nav-height:0px]",j.className),inner:{className:(0,a.QP)("top"===k?"md:pt-2.5":"md:pt-3.5","navbar"===e&&"md:pt-0")},children:[(0,o.jsxs)(u.SidebarHeader,{children:["auto"===k&&(0,o.jsxs)("div",{className:"flex flex-row justify-between max-md:hidden",children:[(0,o.jsx)(E.default,{href:r.url??"/",className:"inline-flex items-center gap-2.5 font-medium",children:r.title}),(0,o.jsx)(u.SidebarCollapseTrigger,{className:(0,a.QP)((0,l.r)({color:"ghost",size:"icon-sm"}),"text-fd-muted-foreground mb-auto"),children:(0,o.jsx)(d,{})})]}),r.children,h,"sidebar"===e&&R.length>0?(0,o.jsx)(A.RootToggle,{options:R}):null]}),(0,o.jsxs)(u.SidebarViewport,{children:["navbar"===e&&R.map((e,t)=>(0,o.jsx)(g.SidebarLayoutTab,{item:e,className:(0,a.QP)("lg:hidden",t===R.length-1&&"mb-4")},e.url)),S.map((e,t)=>(0,o.jsx)(b,{item:e,className:(0,a.QP)("lg:hidden",t===S.length-1&&"mb-4")},t)),(0,o.jsx)(u.SidebarPageTree,{components:y})]}),(0,o.jsxs)(u.SidebarFooter,{className:(0,a.QP)("flex flex-row items-center",!F&&"md:hidden"),children:[B?(0,o.jsx)(D.LanguageToggle,{className:"me-auto md:hidden",children:(0,o.jsx)(c.A,{className:"size-5 text-fd-muted-foreground"})}):null,(0,s.NI)(N,(0,o.jsx)(f.ThemeToggle,{className:"md:hidden",mode:N?.mode??"light-dark-system"})),F]})]}),(0,o.jsx)(w,{mode:r.mode,nav:(0,o.jsx)(E.default,{href:r.url??"/",className:(0,a.QP)("inline-flex items-center gap-2.5 font-semibold","auto"===k&&"md:hidden"),children:r.title}),links:S,i18n:B,sidebarCollapsible:m,searchToggle:I,tabs:"navbar"==e?R:[],children:r.children}),(0,o.jsx)(v.StylesProvider,{...U,children:T.children})]})})})}function w({mode:e="auto",nav:t,sidebarCollapsible:r=!1,links:n,themeSwitch:i,searchToggle:m,i18n:h=!1,tabs:F,children:C}){return(0,o.jsxs)(g.Navbar,{mode:e,children:[(0,o.jsxs)("div",{className:(0,a.QP)("flex flex-row border-b border-fd-foreground/10 px-4 h-14","auto"===e&&"md:px-6"),children:[(0,o.jsxs)("div",{className:(0,a.QP)("flex flex-row items-center","top"===e&&"flex-1 pe-4"),children:[r&&"auto"===e?(0,o.jsx)(u.SidebarCollapseTrigger,{className:(0,a.QP)((0,l.r)({color:"ghost",size:"icon-sm"}),"text-fd-muted-foreground -ms-1.5 me-2 data-[collapsed=false]:hidden max-md:hidden"),children:(0,o.jsx)(d,{})}):null,t]}),(0,s.Q0)("lg",m,(0,o.jsx)(y.LargeSearchToggle,{hideIfDisabled:!0,className:(0,a.QP)("w-full my-auto rounded-xl max-md:hidden","top"===e?"max-w-sm px-2":"max-w-[240px]")})),(0,o.jsxs)("div",{className:"flex flex-1 flex-row items-center justify-end",children:[(0,o.jsx)("div",{className:"flex flex-row items-center gap-6 px-4 empty:hidden max-lg:hidden",children:n.filter(e=>"icon"!==e.type).map((e,t)=>(0,o.jsx)(B,{item:e,className:"text-sm text-fd-muted-foreground transition-colors hover:text-fd-accent-foreground"},t))}),C,(0,s.Q0)("sm",m,(0,o.jsx)(y.SearchToggle,{hideIfDisabled:!0,className:"md:hidden"})),(0,o.jsx)(g.NavbarSidebarTrigger,{className:"-me-1.5 md:hidden"}),n.filter(e=>"icon"===e.type).map((e,t)=>(0,o.jsx)(p.BaseLinkItem,{item:e,className:(0,a.QP)((0,l.r)({size:"icon-sm",color:"ghost"}),"text-fd-muted-foreground max-lg:hidden"),"aria-label":e.label,children:e.icon},t)),h?(0,o.jsx)(D.LanguageToggle,{className:"max-md:hidden",children:(0,o.jsx)(c.A,{className:"size-4.5 text-fd-muted-foreground"})}):null,(0,s.NI)(i,(0,o.jsx)(f.ThemeToggle,{className:"ms-2 max-md:hidden",mode:i?.mode??"light-dark-system"})),r&&"top"===e?(0,o.jsx)(u.SidebarCollapseTrigger,{className:(0,a.QP)((0,l.r)({color:"secondary",size:"icon-sm"}),"ms-2 text-fd-muted-foreground rounded-full max-md:hidden"),children:(0,o.jsx)(d,{})}):null]})]}),F.length>0?(0,o.jsx)(g.LayoutTabs,{className:"px-6 border-b border-fd-foreground/10 h-10 max-lg:hidden",children:F.map(e=>(0,o.jsx)(g.LayoutTab,{...e},e.url))}):null]})}function B({item:e,...t}){return"menu"===e.type?(0,o.jsxs)(h.Popover,{children:[(0,o.jsxs)(h.PopoverTrigger,{...t,className:(0,a.QP)("inline-flex items-center gap-1.5",t.className),children:[e.text,(0,o.jsx)(m.A,{className:"size-3"})]}),(0,o.jsx)(h.PopoverContent,{className:"flex flex-col",children:e.items.map((e,t)=>"custom"===e.type?(0,o.jsx)(n.Fragment,{children:e.children},t):(0,o.jsxs)(p.BaseLinkItem,{item:e,className:"inline-flex items-center gap-2 rounded-md p-2 text-start hover:bg-fd-accent hover:text-fd-accent-foreground data-[active=true]:text-fd-primary [&_svg]:size-4",children:[e.icon,e.text]},t))})]}):"custom"===e.type?e.children:(0,o.jsx)(p.BaseLinkItem,{item:e,...t,children:e.text})}var P=r(963),N=r(92897);async function I({params:e,children:t}){let{lang:r}=await e;return(0,o.jsx)(j,{...(0,P.k)(r),tabMode:"sidebar",tree:N.s.pageTree[r],children:t})}},23149:(e,t,r)=>{"use strict";r.d(t,{j:()=>d});var o=r(60687),n=r(43210),s=r(92534),u=r(5261),i=r(24249);function a(e,t){if(0===t.length||0===e.clientHeight)return[0,0];let r=Number.MAX_VALUE,o=0;for(let n of t){let t=e.querySelector(`a[href="#${n}"]`);if(!t)continue;let s=getComputedStyle(t);r=Math.min(r,t.offsetTop+parseFloat(s.paddingTop)),o=Math.max(o,t.offsetTop+t.clientHeight-parseFloat(s.paddingBottom))}return[r,o-r]}function l(e,t){e.style.setProperty("--fd-top",`${t[0]}px`),e.style.setProperty("--fd-height",`${t[1]}px`)}function d({containerRef:e,...t}){let r=s.Mf(),d=(0,n.useRef)(null),c=(0,i.J)(()=>{e.current&&d.current&&l(d.current,a(e.current,r))});return(0,n.useEffect)(()=>{if(!e.current)return;let t=e.current;c();let r=new ResizeObserver(c);return r.observe(t),()=>{r.disconnect()}},[e,c]),(0,u.T)(r,()=>{e.current&&d.current&&l(d.current,a(e.current,r))}),(0,o.jsx)("div",{ref:d,role:"none",...t})}},24703:(e,t,r)=>{"use strict";r.d(t,{RootToggle:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call RootToggle() from the server but RootToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\root-toggle.js","RootToggle")},25334:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27384:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w,generateMetadata:()=>P,generateStaticParams:()=>B});var o=r(37413);let n=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,s=Object.hasOwnProperty;class u{constructor(){this.occurrences,this.reset()}slug(e,t){var r,o;let u=(r=e,o=!0===t,"string"!=typeof r?"":(o||(r=r.toLowerCase()),r.replace(n,"").replace(/ /g,"-"))),i=u;for(;s.call(this.occurrences,u);)this.occurrences[i]++,u=i+"-"+this.occurrences[i];return this.occurrences[u]=0,u}reset(){this.occurrences=Object.create(null)}}new u,r(35367),r(7255);var i=r(61120),a=r(67829),l=r(85752),d=r(8974),c=r(20057),m=r(74586),p=r(39614),D=r(26373);let f=(0,D.A)("text",[["path",{d:"M17 6.1H3",key:"wptmhv"}],["path",{d:"M21 12.1H3",key:"1j38uz"}],["path",{d:"M15.1 18H3",key:"1nb16a"}]]),h=(0,D.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var F=r(36045),C=r(27986);function x({toc:e=[],full:t=!1,tableOfContentPopover:{enabled:r,component:n,...s}={},tableOfContent:{enabled:u,component:i,...p}={},...D}){let h=e.length>0||void 0!==p.footer||void 0!==p.header;return u??(u=!t&&h),r??(r=e.length>0||void 0!==s.header||void 0!==s.footer),(0,o.jsxs)(a.AnchorProvider,{toc:e,single:p.single,children:[(0,o.jsxs)(c.PageBody,{...D.container,className:(0,d.QP)(D.container?.className),style:{"--fd-tocnav-height":r?void 0:"0px",...D.container?.style},children:[(0,l.NI)({enabled:r,component:n},(0,o.jsxs)(c.TocPopover,{className:"h-10",children:[(0,o.jsx)(c.TocPopoverTrigger,{className:"w-full",items:e}),(0,o.jsxs)(c.TocPopoverContent,{children:[s.header,(0,o.jsx)(m.TOCScrollArea,{isMenu:!0,children:"clerk"===s.style?(0,o.jsx)(C.default,{items:e}):(0,o.jsx)(m.TOCItems,{items:e})}),s.footer]})]}),{items:e,...s}),(0,o.jsxs)(c.PageArticle,{...D.article,className:(0,d.QP)(t||!u?"max-w-[1120px]":"max-w-[860px]",D.article?.className),children:[(0,l.NI)(D.breadcrumb,(0,o.jsx)(c.Breadcrumb,{...D.breadcrumb})),D.children,(0,o.jsx)("div",{role:"none",className:"flex-1"}),(0,o.jsxs)("div",{className:"flex flex-row flex-wrap items-center justify-between gap-4 empty:hidden",children:[D.editOnGithub?(0,o.jsx)(b,{...D.editOnGithub}):null,D.lastUpdate?(0,o.jsx)(c.LastUpdate,{date:new Date(D.lastUpdate)}):null]}),(0,l.NI)(D.footer,(0,o.jsx)(c.Footer,{items:D.footer?.items}))]})]}),(0,l.NI)({enabled:u,component:i},(0,o.jsxs)(m.Toc,{children:[p.header,(0,o.jsxs)("h3",{className:"inline-flex items-center gap-1.5 text-sm text-fd-muted-foreground",children:[(0,o.jsx)(f,{className:"size-4"}),(0,o.jsx)(F.I18nLabel,{label:"toc"})]}),(0,o.jsx)(m.TOCScrollArea,{children:"clerk"===p.style?(0,o.jsx)(C.default,{items:e}):(0,o.jsx)(m.TOCItems,{items:e})}),p.footer]}),{items:e,...p})]})}function b({owner:e,repo:t,sha:r,path:n,...s}){let u=`https://github.com/${e}/${t}/blob/${r}/${n.startsWith("/")?n.slice(1):n}`;return(0,o.jsxs)("a",{href:u,target:"_blank",rel:"noreferrer noopener",...s,className:(0,d.QP)((0,p.r)({color:"secondary",className:"gap-1.5 text-fd-muted-foreground"}),s.className),children:[(0,o.jsx)(h,{className:"size-3.5"}),(0,o.jsx)(F.I18nLabel,{label:"editOnGithub"})]})}let g=(0,i.forwardRef)((e,t)=>(0,o.jsx)("div",{ref:t,...e,className:(0,d.QP)("prose",e.className),children:e.children}));g.displayName="DocsBody";let v=(0,i.forwardRef)((e,t)=>void 0===e.children?null:(0,o.jsx)("p",{ref:t,...e,className:(0,d.QP)("mb-8 text-lg text-fd-muted-foreground",e.className),children:e.children}));v.displayName="DocsDescription";let A=(0,i.forwardRef)((e,t)=>(0,o.jsx)("h1",{ref:t,...e,className:(0,d.QP)("text-3xl font-semibold",e.className),children:e.children}));A.displayName="DocsTitle";var E=r(39916),y=r(29013),j=r(92897);async function w(e){let t=await e.params,r=j.s.getPage(t.slug,t.lang);r||(0,E.notFound)();let n=r.data.body;return(0,o.jsxs)(x,{toc:r.data.toc,full:r.data.full,tableOfContent:{style:"clerk"},editOnGithub:{owner:"TEN-framework",repo:"portal",sha:"main",path:`content/docs/${r.file.path}`},children:[(0,o.jsx)(A,{children:r.data.title}),(0,o.jsx)(v,{children:r.data.description}),(0,o.jsx)(g,{children:(0,o.jsx)(n,{components:{...y.A,a:(0,y.f)(j.s,r)}})})]})}async function B(){return j.s.generateParams()}async function P(e){let t=await e.params,r=j.s.getPage(t.slug,t.lang);return r||(0,E.notFound)(),{title:r.data.title,description:r.data.description}}},27986:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\node_modules\\\\fumadocs-ui\\\\dist\\\\components\\\\layout\\\\toc-clerk.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\toc-clerk.js","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36045:(e,t,r)=>{"use strict";r.d(t,{I18nLabel:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call defaultTranslations() from the server but defaultTranslations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\i18n.js","defaultTranslations"),(0,o.registerClientReference)(function(){throw Error("Attempted to call I18nContext() from the server but I18nContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\i18n.js","I18nContext");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call I18nLabel() from the server but I18nLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\i18n.js","I18nLabel");(0,o.registerClientReference)(function(){throw Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\i18n.js","useI18n")},37609:(e,t,r)=>{"use strict";r.d(t,{TreeContextProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call TreeContextProvider() from the server but TreeContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\tree.js","TreeContextProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useTreePath() from the server but useTreePath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\tree.js","useTreePath"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useTreeContext() from the server but useTreeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\tree.js","useTreeContext")},39614:(e,t,r)=>{"use strict";r.d(t,{r:()=>o});let o=(0,r(93788).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 p-1 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5"}}})},39987:(e,t,r)=>{"use strict";r.d(t,{LayoutTab:()=>i,LayoutTabs:()=>u,Navbar:()=>n,NavbarSidebarTrigger:()=>s,SidebarLayoutTab:()=>a});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\notebook-client.js","Navbar"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call NavbarSidebarTrigger() from the server but NavbarSidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\notebook-client.js","NavbarSidebarTrigger"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call LayoutTabs() from the server but LayoutTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\notebook-client.js","LayoutTabs"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call LayoutTab() from the server but LayoutTab is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\notebook-client.js","LayoutTab"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarLayoutTab() from the server but SidebarLayoutTab is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\notebook-client.js","SidebarLayoutTab")},46936:(e,t,r)=>{Promise.resolve().then(r.bind(r,40851)),Promise.resolve().then(r.bind(r,78655)),Promise.resolve().then(r.bind(r,69767)),Promise.resolve().then(r.bind(r,78941)),Promise.resolve().then(r.bind(r,24703)),Promise.resolve().then(r.bind(r,36163)),Promise.resolve().then(r.bind(r,77300)),Promise.resolve().then(r.bind(r,24274)),Promise.resolve().then(r.bind(r,31061)),Promise.resolve().then(r.bind(r,78189)),Promise.resolve().then(r.bind(r,3231)),Promise.resolve().then(r.bind(r,37609)),Promise.resolve().then(r.bind(r,8801)),Promise.resolve().then(r.bind(r,39987))},53700:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var o=r(60687),n=r(92534),s=r(43210),u=r(82348),i=r(23149),a=r(3512);function l({items:e}){let t=(0,s.useRef)(null),[r,n]=(0,s.useState)();return 0===e.length?(0,o.jsx)(a.k,{}):(0,o.jsxs)(o.Fragment,{children:[r?(0,o.jsx)("div",{className:"absolute start-0 top-0 rtl:-scale-x-100",style:{width:r.width,height:r.height,maskImage:`url("data:image/svg+xml,${encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${r.width} ${r.height}"><path d="${r.path}" stroke="black" stroke-width="1" fill="none" /></svg>`)}")`},children:(0,o.jsx)(i.j,{containerRef:t,className:"mt-(--fd-top) h-(--fd-height) bg-fd-primary transition-all"})}):null,(0,o.jsx)("div",{className:"flex flex-col",ref:t,children:e.map((t,r)=>(0,o.jsx)(d,{item:t,upper:e[r-1]?.depth,lower:e[r+1]?.depth},t.url))})]})}function d({item:e,upper:t=e.depth,lower:r=e.depth}){var s;let i=10*(e.depth>=3),a=10*(t>=3);return(0,o.jsxs)(n.Cz,{href:e.url,style:{paddingInlineStart:(s=e.depth)<=2?14:3===s?26:36},className:"prose relative py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",children:[i!==a?(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",className:"absolute -top-1.5 start-0 size-4 rtl:-scale-x-100",children:(0,o.jsx)("line",{x1:a,y1:"0",x2:i,y2:"12",className:"stroke-fd-foreground/10",strokeWidth:"1"})}):null,(0,o.jsx)("div",{className:(0,u.QP)("absolute inset-y-0 w-px bg-fd-foreground/10",i!==a&&"top-1.5",i!==10*(r>=3)&&"bottom-1.5"),style:{insetInlineStart:i}}),e.title]})}},55854:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CollapsibleSidebar:()=>g,Sidebar:()=>v,SidebarCollapseTrigger:()=>T,SidebarFolder:()=>B,SidebarFolderContent:()=>I,SidebarFolderLink:()=>N,SidebarFolderTrigger:()=>P,SidebarFooter:()=>E,SidebarHeader:()=>A,SidebarItem:()=>w,SidebarPageTree:()=>R,SidebarSeparator:()=>j,SidebarViewport:()=>y});var o=r(60687),n=r(25334),s=r(78272),u=r(95341),i=r(66218),a=r(43210),l=r(46250),d=r(5261),c=r(82348),m=r(88968),p=r(349),D=r(77379),f=r(40463),h=r(24224),F=r(79667);let C=(0,h.F)("relative flex flex-row items-center gap-2 rounded-md p-2 text-start text-fd-muted-foreground [overflow-wrap:anywhere] md:py-1.5 [&_svg]:size-4 [&_svg]:shrink-0",{variants:{active:{true:"bg-fd-primary/10 text-fd-primary",false:"transition-colors hover:bg-fd-accent/50 hover:text-fd-accent-foreground/80 hover:transition-none"}}}),x=(0,a.createContext)(null),b=(0,a.createContext)(null);function g(e){let{collapsed:t}=(0,f.c)(),[r,n]=(0,a.useState)(!1),s=(0,a.useRef)(0),u=(0,a.useRef)(0);return(0,d.T)(t,()=>{n(!1),u.current=Date.now()+150}),(0,o.jsx)(v,{...e,onPointerEnter:e=>{!t||"touch"===e.pointerType||u.current>Date.now()||(window.clearTimeout(s.current),n(!0))},onPointerLeave:e=>{t&&"touch"!==e.pointerType&&(window.clearTimeout(s.current),s.current=window.setTimeout(()=>{n(!1),u.current=Date.now()+150},Math.min(e.clientX,document.body.clientWidth-e.clientX)>100?0:500))},"data-collapsed":t,className:(0,c.QP)("md:transition-all",t&&"md:-me-(--fd-sidebar-width) md:-translate-x-(--fd-sidebar-offset) rtl:md:translate-x-(--fd-sidebar-offset)",t&&r&&"z-50 md:translate-x-0",t&&!r&&"md:opacity-0",e.className),style:{"--fd-sidebar-offset":"calc(var(--fd-sidebar-width) - 6px)",...e.style}})}function v({defaultOpenLevel:e=0,prefetch:t=!0,inner:r,...n}){let s=(0,a.useMemo)(()=>({defaultOpenLevel:e,prefetch:t,level:1}),[e,t]);return(0,o.jsx)(x.Provider,{value:s,children:(0,o.jsx)(u.Pg,{id:"nd-sidebar",removeScrollOn:"(width < 768px)",...n,className:(0,c.QP)("fixed top-[calc(var(--fd-banner-height)+var(--fd-nav-height))] z-20 bg-fd-card text-sm md:sticky md:h-(--fd-sidebar-height)","max-md:inset-x-0 max-md:bottom-0 max-md:bg-fd-background/80 max-md:text-[15px] max-md:backdrop-blur-lg max-md:data-[open=false]:invisible",n.className),style:{...n.style,"--fd-sidebar-height":"calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"},children:(0,o.jsx)("div",{...r,className:(0,c.QP)("flex size-full max-w-full flex-col pt-2 md:ms-auto md:w-(--fd-sidebar-width) md:border-e md:pt-4",r?.className),children:n.children})})})}function A(e){return(0,o.jsx)("div",{...e,className:(0,c.QP)("flex flex-col gap-3 px-4 empty:hidden",e.className),children:e.children})}function E(e){return(0,o.jsx)("div",{...e,className:(0,c.QP)("flex flex-col border-t px-4 py-3 empty:hidden",e.className),children:e.children})}function y(e){return(0,o.jsx)(m.FK,{...e,className:(0,c.QP)("h-full",e.className),children:(0,o.jsx)(m.Gl,{className:"p-4",style:{maskImage:"linear-gradient(to bottom, transparent, white 12px)"},children:e.children})})}function j(e){let{level:t}=S();return(0,o.jsx)("p",{...e,className:(0,c.QP)("inline-flex items-center gap-2 mb-2 px-2 text-sm font-medium [&_svg]:size-4 [&_svg]:shrink-0",e.className),style:{paddingInlineStart:Q(t),...e.style},children:e.children})}function w({icon:e,...t}){let r=(0,i.a8)(),s=void 0!==t.href&&(0,p.$)(t.href,r,!1),{prefetch:u,level:a}=S();return(0,o.jsxs)(l.default,{...t,"data-active":s,className:(0,c.QP)(C({active:s}),t.className),prefetch:u,style:{paddingInlineStart:Q(a),...t.style},children:[(0,o.jsx)(U,{level:a,active:s}),e??(t.external?(0,o.jsx)(n.A,{}):null),t.children]})}function B({defaultOpen:e=!1,...t}){let[r,n]=(0,a.useState)(e);return(0,d.T)(e,e=>{e&&n(e)}),(0,o.jsx)(D.Nt,{open:r,onOpenChange:n,...t,children:(0,o.jsx)(b.Provider,{value:(0,a.useMemo)(()=>({open:r,setOpen:n}),[r]),children:t.children})})}function P(e){let{level:t}=S(),{open:r}=k();return(0,o.jsxs)(D.R6,{...e,className:(0,c.QP)(C({active:!1}),"w-full"),style:{paddingInlineStart:Q(t),...e.style},children:[(0,o.jsx)(U,{level:t}),e.children,(0,o.jsx)(s.A,{"data-icon":!0,className:(0,c.QP)("ms-auto transition-transform",!r&&"-rotate-90")})]})}function N(e){let{open:t,setOpen:r}=k(),{prefetch:n,level:u}=S(),a=(0,i.a8)(),d=void 0!==e.href&&(0,p.$)(e.href,a,!1);return(0,o.jsxs)(l.default,{...e,"data-active":d,className:(0,c.QP)(C({active:d}),"w-full",e.className),onClick:e=>{e.target.hasAttribute("data-icon")?(r(e=>!e),e.preventDefault()):r(e=>!d||!e)},prefetch:n,style:{paddingInlineStart:Q(u),...e.style},children:[(0,o.jsx)(U,{level:u,active:d}),e.children,(0,o.jsx)(s.A,{"data-icon":!0,className:(0,c.QP)("ms-auto transition-transform",!t&&"-rotate-90")})]})}function I(e){let t=S();return(0,o.jsx)(D.Ke,{...e,className:(0,c.QP)("relative",e.className),children:(0,o.jsxs)(x.Provider,{value:(0,a.useMemo)(()=>({...t,level:t.level+1}),[t]),children:[(0,o.jsx)("div",{className:"absolute w-px inset-y-0 bg-fd-border start-3"}),e.children]})})}function T(e){let{collapsed:t,setCollapsed:r}=(0,f.c)();return(0,o.jsx)("button",{type:"button","aria-label":"Collapse Sidebar","data-collapsed":t,...e,onClick:()=>{r(e=>!e)},children:e.children})}function k(){let e=(0,a.useContext)(b);if(!e)throw Error("Missing sidebar folder");return e}function S(){let e=(0,a.useContext)(x);if(!e)throw Error("<Sidebar /> component required.");return e}function R(e){let{root:t}=(0,F.t)();return(0,a.useMemo)(()=>{let{Separator:r,Item:n,Folder:s}=e.components??{};return(0,o.jsx)(a.Fragment,{children:function e(t,u){return t.map((t,i)=>{if("separator"===t.type)return r?(0,o.jsx)(r,{item:t},i):(0,o.jsxs)(j,{className:(0,c.QP)(0!==i&&"mt-8"),children:[t.icon,t.name]},i);if("folder"===t.type){let r=e(t.children,u+1);return s?(0,o.jsx)(s,{item:t,level:u,children:r},i):(0,o.jsx)(_,{item:t,children:r},i)}return n?(0,o.jsx)(n,{item:t},t.url):(0,o.jsx)(w,{href:t.url,external:t.external,icon:t.icon,children:t.name},t.url)})}(t.children,1)},t.$id)},[e.components,t])}function _({item:e,...t}){let{defaultOpenLevel:r,level:n}=S(),s=(0,F.L)();return(0,o.jsxs)(B,{defaultOpen:(e.defaultOpen??r>=n)||s.includes(e),children:[e.index?(0,o.jsxs)(N,{href:e.index.url,external:e.index.external,...t,children:[e.icon,e.name]}):(0,o.jsxs)(P,{...t,children:[e.icon,e.name]}),(0,o.jsx)(I,{children:t.children})]})}function Q(e){return`calc(var(--spacing) * ${(e>1?e:0)*2+2})`}function U({level:e,active:t}){return e<=1?null:(0,o.jsx)("div",{className:(0,c.QP)("absolute w-px inset-y-2 z-[2] start-3",t&&"bg-fd-primary")})}},56664:(e,t,r)=>{Promise.resolve().then(r.bind(r,13453)),Promise.resolve().then(r.bind(r,76773)),Promise.resolve().then(r.bind(r,46250)),Promise.resolve().then(r.bind(r,52923)),Promise.resolve().then(r.bind(r,90753)),Promise.resolve().then(r.bind(r,61481)),Promise.resolve().then(r.bind(r,55854)),Promise.resolve().then(r.bind(r,61883)),Promise.resolve().then(r.bind(r,60510)),Promise.resolve().then(r.bind(r,74047)),Promise.resolve().then(r.bind(r,353)),Promise.resolve().then(r.bind(r,79667)),Promise.resolve().then(r.bind(r,97363)),Promise.resolve().then(r.bind(r,59254))},59254:(e,t,r)=>{"use strict";r.d(t,{LayoutTab:()=>C,LayoutTabs:()=>h,Navbar:()=>D,NavbarSidebarTrigger:()=>f,SidebarLayoutTab:()=>x});var o=r(60687),n=r(82348),s=r(40463),u=r(353),i=r(95341),a=r(22880),l=r(11860);let d=(0,r(62688).A)("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var c=r(46250),m=r(66218),p=r(349);function D({mode:e,...t}){let{open:r,collapsed:i}=(0,s.c)(),{isTransparent:a}=(0,u.h)();return(0,o.jsx)("header",{id:"nd-subnav",...t,className:(0,n.QP)("fixed inset-x-0 top-(--fd-banner-height) z-10 px-(--fd-layout-offset) backdrop-blur-sm transition-colors",(!a||r)&&"bg-fd-background/80","auto"===e&&!i&&"ps-[calc(var(--fd-layout-offset)+var(--fd-sidebar-width))]",t.className),children:t.children})}function f(e){let{open:t}=(0,s.c)();return(0,o.jsx)(i.x2,{...e,className:(0,n.QP)((0,a.r)({color:"ghost",size:"icon"}),e.className),children:t?(0,o.jsx)(l.A,{}):(0,o.jsx)(d,{})})}function h(e){return(0,o.jsx)("div",{...e,className:(0,n.QP)("flex flex-row items-end gap-6 overflow-auto",e.className),children:e.children})}function F(e){let t=(0,m.a8)();return e.urls?e.urls.has(t.endsWith("/")?t.slice(0,-1):t):(0,p.$)(e.url,t,!0)}function C(e){let{closeOnRedirect:t}=(0,s.c)(),r=F(e);return(0,o.jsx)(c.default,{className:(0,n.QP)("inline-flex items-center py-2.5 border-b border-transparent gap-2 text-fd-muted-foreground text-sm text-nowrap",r&&"text-fd-foreground font-medium border-fd-primary"),href:e.url,onClick:()=>{t.current=!1},children:e.title})}function x({item:e,...t}){let r=F(e);return(0,o.jsxs)(c.default,{...t,className:(0,n.QP)("flex flex-row items-center px-2 -mx-2 py-1.5 gap-2.5 text-fd-muted-foreground [&_svg]:!size-4.5",r?"text-fd-primary font-medium":"hover:text-fd-accent-foreground",t.className),"data-active":r,href:e.url,children:[e.icon,e.title]})}},59427:(e,t,r)=>{Promise.resolve().then(r.bind(r,66218)),Promise.resolve().then(r.bind(r,46250)),Promise.resolve().then(r.bind(r,92534)),Promise.resolve().then(r.bind(r,31035)),Promise.resolve().then(r.bind(r,53700)),Promise.resolve().then(r.bind(r,3512)),Promise.resolve().then(r.bind(r,60510)),Promise.resolve().then(r.bind(r,72527)),Promise.resolve().then(r.bind(r,10555))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67829:(e,t,r)=>{"use strict";r.d(t,{AnchorProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call AnchorProvider() from the server but AnchorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\toc.js","AnchorProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call ScrollProvider() from the server but ScrollProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\toc.js","ScrollProvider"),(0,o.registerClientReference)(function(){throw Error("Attempted to call TOCItem() from the server but TOCItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\toc.js","TOCItem"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useActiveAnchor() from the server but useActiveAnchor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\toc.js","useActiveAnchor"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useActiveAnchors() from the server but useActiveAnchors is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\toc.js","useActiveAnchors")},74586:(e,t,r)=>{"use strict";r.d(t,{TOCItems:()=>u,TOCScrollArea:()=>s,Toc:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call Toc() from the server but Toc is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\toc.js","Toc");(0,o.registerClientReference)(function(){throw Error("Attempted to call TocItemsEmpty() from the server but TocItemsEmpty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\toc.js","TocItemsEmpty");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call TOCScrollArea() from the server but TOCScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\toc.js","TOCScrollArea"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call TOCItems() from the server but TOCItems is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\toc.js","TOCItems")},77300:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CollapsibleSidebar:()=>n,Sidebar:()=>s,SidebarCollapseTrigger:()=>f,SidebarFolder:()=>c,SidebarFolderContent:()=>D,SidebarFolderLink:()=>p,SidebarFolderTrigger:()=>m,SidebarFooter:()=>i,SidebarHeader:()=>u,SidebarItem:()=>d,SidebarPageTree:()=>h,SidebarSeparator:()=>l,SidebarViewport:()=>a});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call CollapsibleSidebar() from the server but CollapsibleSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","CollapsibleSidebar"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","Sidebar"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarHeader"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarFooter"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarViewport() from the server but SidebarViewport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarViewport"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarSeparator"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarItem() from the server but SidebarItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarItem"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarFolder() from the server but SidebarFolder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarFolder"),m=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarFolderTrigger() from the server but SidebarFolderTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarFolderTrigger"),p=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarFolderLink() from the server but SidebarFolderLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarFolderLink"),D=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarFolderContent() from the server but SidebarFolderContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarFolderContent"),f=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarCollapseTrigger() from the server but SidebarCollapseTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarCollapseTrigger"),h=(0,o.registerClientReference)(function(){throw Error("Attempted to call SidebarPageTree() from the server but SidebarPageTree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\sidebar.js","SidebarPageTree")},78189:(e,t,r)=>{"use strict";r.d(t,{Popover:()=>n,PopoverContent:()=>u,PopoverTrigger:()=>s});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\ui\\popover.js","Popover"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\ui\\popover.js","PopoverTrigger"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\ui\\popover.js","PopoverContent");(0,o.registerClientReference)(function(){throw Error("Attempted to call PopoverClose() from the server but PopoverClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\ui\\popover.js","PopoverClose")},79551:e=>{"use strict";e.exports=require("url")},90753:(e,t,r)=>{"use strict";r.d(t,{RootToggle:()=>m});var o=r(60687);let n=(0,r(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);var s=r(43210),u=r(46250),i=r(66218),a=r(82348),l=r(349),d=r(40463),c=r(74047);function m({options:e,placeholder:t,...r}){let[m,D]=(0,s.useState)(!1),{closeOnRedirect:f}=(0,d.c)(),h=(0,i.a8)(),F=(0,s.useMemo)(()=>e.findLast(e=>e.urls?e.urls.has(h.endsWith("/")?h.slice(0,-1):h):(0,l.$)(e.url,h,!0)),[e,h]),C=()=>{f.current=!1,D(!1)},x=F?(0,o.jsx)(p,{...F}):t;return(0,o.jsxs)(c.Popover,{open:m,onOpenChange:D,children:[x?(0,o.jsxs)(c.PopoverTrigger,{...r,className:(0,a.QP)("flex items-center gap-2.5 rounded-lg pe-2 hover:text-fd-accent-foreground",r.className),children:[x,(0,o.jsx)(n,{className:"size-4 text-fd-muted-foreground"})]}):null,(0,o.jsx)(c.PopoverContent,{className:"w-(--radix-popover-trigger-width) overflow-hidden p-0",children:e.map(e=>(0,o.jsx)(u.default,{href:e.url,onClick:C,...e.props,className:(0,a.QP)("flex w-full flex-row items-center gap-2 px-2 py-1.5",F===e?"bg-fd-accent text-fd-accent-foreground":"hover:bg-fd-accent/50",e.props?.className),children:(0,o.jsx)(p,{...e})},e.url))})]})}function p(e){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(o.Fragment,{children:e.icon}),(0,o.jsxs)("div",{className:"flex-1 text-start",children:[(0,o.jsx)("p",{className:"text-sm font-medium",children:e.title}),e.description?(0,o.jsx)("p",{className:"text-xs text-fd-muted-foreground",children:e.description}):null]})]})}},91836:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>u.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>l});var o=r(65239),n=r(48088),s=r(88170),u=r.n(s),i=r(30893),a={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>i[e]);r.d(t,a);let l={children:["",{children:["[lang]",{children:["docs",{children:["[[...slug]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27384)),"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\[[...slug]]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,21241)),"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,2635)),"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,87569))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,87569))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\[[...slug]]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[lang]/docs/[[...slug]]/page",pathname:"/[lang]/docs/[[...slug]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},92534:(e,t,r)=>{"use strict";r.d(t,{AnchorProvider:()=>C,N2:()=>F,Cz:()=>x,R3:()=>f,Mf:()=>h});var o=r(22548);r(22317);var n=r(43210);let s=e=>"object"==typeof e&&null!=e&&1===e.nodeType,u=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,i=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let r=getComputedStyle(e,null);return u(r.overflowY,t)||u(r.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},a=(e,t,r,o,n,s,u,i)=>s<e&&u>t||s>e&&u<t?0:s<=e&&i<=r||u>=t&&i>=r?s-e-o:u>t&&i<r||s<e&&i>r?u-t+n:0,l=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},d=(e,t)=>{var r,o,n,u;if("undefined"==typeof document)return[];let{scrollMode:d,block:c,inline:m,boundary:p,skipOverflowHiddenElements:D}=t,f="function"==typeof p?p:e=>e!==p;if(!s(e))throw TypeError("Invalid target");let h=document.scrollingElement||document.documentElement,F=[],C=e;for(;s(C)&&f(C);){if((C=l(C))===h){F.push(C);break}null!=C&&C===document.body&&i(C)&&!i(document.documentElement)||null!=C&&i(C,D)&&F.push(C)}let x=null!=(o=null==(r=window.visualViewport)?void 0:r.width)?o:innerWidth,b=null!=(u=null==(n=window.visualViewport)?void 0:n.height)?u:innerHeight,{scrollX:g,scrollY:v}=window,{height:A,width:E,top:y,right:j,bottom:w,left:B}=e.getBoundingClientRect(),{top:P,right:N,bottom:I,left:T}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),k="start"===c||"nearest"===c?y-P:"end"===c?w+I:y+A/2-P+I,S="center"===m?B+E/2-T+N:"end"===m?j+N:B-T,R=[];for(let e=0;e<F.length;e++){let t=F[e],{height:r,width:o,top:n,right:s,bottom:u,left:l}=t.getBoundingClientRect();if("if-needed"===d&&y>=0&&B>=0&&w<=b&&j<=x&&(t===h&&!i(t)||y>=n&&w<=u&&B>=l&&j<=s))break;let p=getComputedStyle(t),D=parseInt(p.borderLeftWidth,10),f=parseInt(p.borderTopWidth,10),C=parseInt(p.borderRightWidth,10),P=parseInt(p.borderBottomWidth,10),N=0,I=0,T="offsetWidth"in t?t.offsetWidth-t.clientWidth-D-C:0,_="offsetHeight"in t?t.offsetHeight-t.clientHeight-f-P:0,Q="offsetWidth"in t?0===t.offsetWidth?0:o/t.offsetWidth:0,U="offsetHeight"in t?0===t.offsetHeight?0:r/t.offsetHeight:0;if(h===t)N="start"===c?k:"end"===c?k-b:"nearest"===c?a(v,v+b,b,f,P,v+k,v+k+A,A):k-b/2,I="start"===m?S:"center"===m?S-x/2:"end"===m?S-x:a(g,g+x,x,D,C,g+S,g+S+E,E),N=Math.max(0,N+v),I=Math.max(0,I+g);else{N="start"===c?k-n-f:"end"===c?k-u+P+_:"nearest"===c?a(n,u,r,f,P+_,k,k+A,A):k-(n+r/2)+_/2,I="start"===m?S-l-D:"center"===m?S-(l+o/2)+T/2:"end"===m?S-s+C+T:a(l,s,o,D,C+T,S,S+E,E);let{scrollLeft:e,scrollTop:i}=t;N=0===U?0:Math.max(0,Math.min(i+N/U,t.scrollHeight-r/U+_)),I=0===Q?0:Math.max(0,Math.min(e+I/Q,t.scrollWidth-o/Q+T)),k+=i-N,S+=e-I}R.push({el:t,top:N,left:I})}return R},c=e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"};var m=r(60687),p=(0,n.createContext)([]),D=(0,n.createContext)({current:null});function f(){return(0,n.useContext)(p).at(-1)}function h(){return(0,n.useContext)(p)}function F({containerRef:e,children:t}){return(0,m.jsx)(D.Provider,{value:e,children:t})}function C({toc:e,single:t=!0,children:r}){return(0,n.useMemo)(()=>e.map(e=>e.url.split("#")[1]),[e]),(0,m.jsx)(p.Provider,{value:function(e,t){let[r,o]=(0,n.useState)([]);return t?r.slice(0,1):r}(0,t),children:r})}var x=(0,n.forwardRef)(({onActiveChange:e,...t},r)=>{let s=(0,n.useContext)(D),u=h(),i=(0,n.useRef)(null),a=function(...e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!==e&&(e.current=t)})}}(i,r),l=u.includes(t.href.slice(1));return(0,o.T)(l,t=>{let r=i.current;r&&(t&&s.current&&function(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;let r=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(d(e,t));let o="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:n,top:s,left:u}of d(e,c(t))){let e=s-r.top+r.bottom,t=u-r.left+r.right;n.scroll({top:e,left:t,behavior:o})}}(r,{behavior:"smooth",block:"center",inline:"center",scrollMode:"always",boundary:s.current}),e?.(t))}),(0,m.jsx)("a",{ref:a,"data-active":l,...t,children:t.children})});x.displayName="TOCItem"},99755:(e,t,r)=>{Promise.resolve().then(r.bind(r,19956)),Promise.resolve().then(r.bind(r,69767)),Promise.resolve().then(r.bind(r,67829)),Promise.resolve().then(r.bind(r,20383)),Promise.resolve().then(r.bind(r,27986)),Promise.resolve().then(r.bind(r,74586)),Promise.resolve().then(r.bind(r,31061)),Promise.resolve().then(r.bind(r,36045)),Promise.resolve().then(r.bind(r,20057))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,825,189,453,155,510,96,897,958],()=>r(91836));module.exports=o})();