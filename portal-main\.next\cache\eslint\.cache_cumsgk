[{"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\layout.config.tsx": "1", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\metadata.config.ts": "2", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\robots.ts": "3", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\sitemap.ts": "4", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\blog\\page.tsx": "5", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\blog\\[slug]\\page.tsx": "6", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\layout.tsx": "7", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\page.tsx": "8", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\sample-projects.ts": "9", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\_components.tsx": "10", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\layout.tsx": "11", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\[[...slug]]\\page.tsx": "12", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\layout.tsx": "13", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\client-search.tsx": "14", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\structured-data.tsx": "15", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\aurora-background.tsx": "16", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\button.tsx": "17", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\github-star-button.tsx": "18", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\logo.tsx": "19", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\i18n.ts": "20", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\next-intl-navigation.ts": "21", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\next-intl-requests.ts": "22", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\source.ts": "23", "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\utils.ts": "24"}, {"size": 1426, "mtime": 1755593907370, "results": "25", "hashOfConfig": "26"}, {"size": 1605, "mtime": 1755487355000, "results": "27", "hashOfConfig": "26"}, {"size": 321, "mtime": 1755594931680, "results": "28", "hashOfConfig": "26"}, {"size": 1286, "mtime": 1755594943062, "results": "29", "hashOfConfig": "26"}, {"size": 3844, "mtime": 1755487355000, "results": "30", "hashOfConfig": "26"}, {"size": 5017, "mtime": 1755487355000, "results": "31", "hashOfConfig": "26"}, {"size": 415, "mtime": 1755487355000, "results": "32", "hashOfConfig": "26"}, {"size": 1719, "mtime": 1755487355000, "results": "33", "hashOfConfig": "26"}, {"size": 1619, "mtime": 1755487355000, "results": "34", "hashOfConfig": "26"}, {"size": 9208, "mtime": 1755487355000, "results": "35", "hashOfConfig": "26"}, {"size": 568, "mtime": 1755487355000, "results": "36", "hashOfConfig": "26"}, {"size": 1651, "mtime": 1755487355000, "results": "37", "hashOfConfig": "26"}, {"size": 2578, "mtime": 1755593999217, "results": "38", "hashOfConfig": "26"}, {"size": 9927, "mtime": 1755594881413, "results": "39", "hashOfConfig": "26"}, {"size": 1424, "mtime": 1755487355000, "results": "40", "hashOfConfig": "26"}, {"size": 2262, "mtime": 1755487355000, "results": "41", "hashOfConfig": "26"}, {"size": 2123, "mtime": 1755487355000, "results": "42", "hashOfConfig": "26"}, {"size": 6117, "mtime": 1755593765215, "results": "43", "hashOfConfig": "26"}, {"size": 1204, "mtime": 1755487355000, "results": "44", "hashOfConfig": "26"}, {"size": 470, "mtime": 1755487355000, "results": "45", "hashOfConfig": "26"}, {"size": 308, "mtime": 1755487355000, "results": "46", "hashOfConfig": "26"}, {"size": 506, "mtime": 1755487355000, "results": "47", "hashOfConfig": "26"}, {"size": 489, "mtime": 1755487355000, "results": "48", "hashOfConfig": "26"}, {"size": 166, "mtime": 1755487355000, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lvs6d4", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\layout.config.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\metadata.config.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\sample-projects.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\(home)\\_components.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\docs\\[[...slug]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\app\\[lang]\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\client-search.tsx", [], ["122", "123", "124", "125", "126", "127"], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\structured-data.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\aurora-background.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\github-star-button.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\logo.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\i18n.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\next-intl-navigation.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\next-intl-requests.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\source.ts", [], [], "C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\lib\\utils.ts", [], [], {"ruleId": "128", "severity": 2, "message": "129", "line": 31, "column": 50, "nodeType": "130", "messageId": "131", "endLine": 31, "endColumn": 53, "suggestions": "132", "suppressions": "133"}, {"ruleId": "128", "severity": 2, "message": "129", "line": 56, "column": 41, "nodeType": "130", "messageId": "131", "endLine": 56, "endColumn": 44, "suggestions": "134", "suppressions": "135"}, {"ruleId": "128", "severity": 2, "message": "129", "line": 60, "column": 21, "nodeType": "130", "messageId": "131", "endLine": 60, "endColumn": 24, "suggestions": "136", "suppressions": "137"}, {"ruleId": "128", "severity": 2, "message": "129", "line": 62, "column": 21, "nodeType": "130", "messageId": "131", "endLine": 62, "endColumn": 24, "suggestions": "138", "suppressions": "139"}, {"ruleId": "128", "severity": 2, "message": "129", "line": 64, "column": 21, "nodeType": "130", "messageId": "131", "endLine": 64, "endColumn": 24, "suggestions": "140", "suppressions": "141"}, {"ruleId": "128", "severity": 2, "message": "129", "line": 66, "column": 21, "nodeType": "130", "messageId": "131", "endLine": 66, "endColumn": 24, "suggestions": "142", "suppressions": "143"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["144", "145"], ["146"], ["147", "148"], ["149"], ["150", "151"], ["152"], ["153", "154"], ["155"], ["156", "157"], ["158"], ["159", "160"], ["161"], {"messageId": "162", "fix": "163", "desc": "164"}, {"messageId": "165", "fix": "166", "desc": "167"}, {"kind": "168", "justification": "169"}, {"messageId": "162", "fix": "170", "desc": "164"}, {"messageId": "165", "fix": "171", "desc": "167"}, {"kind": "168", "justification": "169"}, {"messageId": "162", "fix": "172", "desc": "164"}, {"messageId": "165", "fix": "173", "desc": "167"}, {"kind": "168", "justification": "169"}, {"messageId": "162", "fix": "174", "desc": "164"}, {"messageId": "165", "fix": "175", "desc": "167"}, {"kind": "168", "justification": "169"}, {"messageId": "162", "fix": "176", "desc": "164"}, {"messageId": "165", "fix": "177", "desc": "167"}, {"kind": "168", "justification": "169"}, {"messageId": "162", "fix": "178", "desc": "164"}, {"messageId": "165", "fix": "179", "desc": "167"}, {"kind": "168", "justification": "169"}, "suggestUnknown", {"range": "180", "text": "181"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "182", "text": "183"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "184", "text": "181"}, {"range": "185", "text": "183"}, {"range": "186", "text": "181"}, {"range": "187", "text": "183"}, {"range": "188", "text": "181"}, {"range": "189", "text": "183"}, {"range": "190", "text": "181"}, {"range": "191", "text": "183"}, {"range": "192", "text": "181"}, {"range": "193", "text": "183"}, [946, 949], "unknown", [946, 949], "never", [1759, 1762], [1759, 1762], [1892, 1895], [1892, 1895], [2017, 2020], [2017, 2020], [2144, 2147], [2144, 2147], [2275, 2278], [2275, 2278]]