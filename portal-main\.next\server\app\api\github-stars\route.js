/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/github-stars/route";
exports.ids = ["app/api/github-stars/route"];
exports.modules = {

/***/ "(rsc)/./app/api/github-stars/route.ts":
/*!***************************************!*\
  !*** ./app/api/github-stars/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// In-memory cache for last successful responses\nconst cache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const repo = searchParams.get('repo');\n    if (!repo) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Repository parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    const cacheKey = repo;\n    const now = Date.now();\n    // Check if we have a recent cached response\n    const cached = cache.get(cacheKey);\n    if (cached && now - cached.timestamp < CACHE_DURATION) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            stargazers_count: cached.stargazers_count,\n            cached_at: new Date(cached.timestamp).toISOString(),\n            from_cache: true\n        });\n    }\n    try {\n        // Prepare headers for authenticated requests\n        const headers = {\n            'Accept': 'application/vnd.github+json',\n            'X-GitHub-Api-Version': '2022-11-28',\n            'User-Agent': 'TEN-Portal-Website'\n        };\n        // Add authentication if GitHub token is available (server-side only)\n        const githubToken = process.env.GITHUB_TOKEN;\n        if (githubToken) {\n            headers['Authorization'] = `Bearer ${githubToken}`;\n        }\n        const response = await fetch(`https://api.github.com/repos/${repo}`, {\n            headers\n        });\n        if (response.ok) {\n            const data = await response.json();\n            const starCount = data.stargazers_count;\n            // Cache the successful response\n            cache.set(cacheKey, {\n                stargazers_count: starCount,\n                timestamp: now\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                stargazers_count: starCount,\n                cached_at: new Date().toISOString(),\n                from_cache: false\n            });\n        } else if (response.status === 403) {\n            // Handle rate limit exceeded\n            const rateLimitReset = response.headers.get('X-RateLimit-Reset');\n            const resetTime = rateLimitReset ? new Date(parseInt(rateLimitReset) * 1000) : null;\n            console.warn('GitHub API rate limit exceeded.', resetTime ? `Resets at: ${resetTime.toLocaleString()}` : '');\n            // Return last successful cached data or fallback\n            const fallbackCount = cached?.stargazers_count || 7135;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                stargazers_count: fallbackCount,\n                error: 'Rate limit exceeded',\n                reset_time: resetTime?.toISOString(),\n                from_cache: !!cached\n            }, {\n                status: 429\n            });\n        } else {\n            console.error('GitHub API request failed:', response.status, response.statusText);\n            // Return last successful cached data or fallback\n            const fallbackCount = cached?.stargazers_count || 7135;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch repository data',\n                stargazers_count: fallbackCount,\n                from_cache: !!cached\n            }, {\n                status: response.status\n            });\n        }\n    } catch (error) {\n        console.error('Failed to fetch star count:', error);\n        // Return last successful cached data or fallback\n        const fallbackCount = cached?.stargazers_count || 7135;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            stargazers_count: fallbackCount,\n            from_cache: !!cached\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/github-stars/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DeepInsight_Downloads_portal_main_portal_main_app_api_github_stars_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/github-stars/route.ts */ \"(rsc)/./app/api/github-stars/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/github-stars/route\",\n        pathname: \"/api/github-stars\",\n        filename: \"route\",\n        bundlePath: \"app/api/github-stars/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\app\\\\api\\\\github-stars\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DeepInsight_Downloads_portal_main_portal_main_app_api_github_stars_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgithub-stars%2Froute&page=%2Fapi%2Fgithub-stars%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgithub-stars%2Froute.ts&appDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=C%3A%5CUsers%5CDeepInsight%5CDownloads%5Cportal-main%5Cportal-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();