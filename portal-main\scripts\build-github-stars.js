#!/usr/bin/env node

/**
 * 构建时获取GitHub星标数据的脚本
 * 在静态导出时运行，将星标数据写入JSON文件
 */

const fs = require('fs')
const path = require('path')

// GitHub仓库列表
const repositories = [
  'TEN-framework/ten-framework',
  'TEN-framework/portal',
  'TEN-framework/ten-vad',
  'TEN-framework/ten-turn-detection'
]

async function fetchGitHubStars(repo) {
  try {
    const headers = {
      'Accept': 'application/vnd.github+json',
      'X-GitHub-Api-Version': '2022-11-28',
      'User-Agent': 'TEN-Portal-Website'
    }

    // 如果有GitHub token，使用认证请求
    if (process.env.GITHUB_TOKEN) {
      headers['Authorization'] = `Bearer ${process.env.GITHUB_TOKEN}`
    }

    console.log(`正在获取 ${repo} 的星标数据...`)
    
    const response = await fetch(`https://api.github.com/repos/${repo}`, {
      headers
    })

    if (response.ok) {
      const data = await response.json()
      console.log(`✅ ${repo}: ${data.stargazers_count} stars`)
      return {
        repo,
        stargazers_count: data.stargazers_count,
        updated_at: new Date().toISOString(),
        success: true
      }
    } else if (response.status === 403) {
      console.warn(`⚠️  ${repo}: GitHub API 限流，使用默认值`)
      return {
        repo,
        stargazers_count: getDefaultStarCount(repo),
        updated_at: new Date().toISOString(),
        success: false,
        error: 'Rate limit exceeded'
      }
    } else {
      console.error(`❌ ${repo}: API 请求失败 (${response.status})`)
      return {
        repo,
        stargazers_count: getDefaultStarCount(repo),
        updated_at: new Date().toISOString(),
        success: false,
        error: `HTTP ${response.status}`
      }
    }
  } catch (error) {
    console.error(`❌ ${repo}: 请求失败`, error.message)
    return {
      repo,
      stargazers_count: getDefaultStarCount(repo),
      updated_at: new Date().toISOString(),
      success: false,
      error: error.message
    }
  }
}

function getDefaultStarCount(repo) {
  // 为不同仓库提供合理的默认值
  const defaults = {
    'TEN-framework/ten-framework': 7135,
    'TEN-framework/portal': 100,
    'TEN-framework/ten-vad': 50,
    'TEN-framework/ten-turn-detection': 30
  }
  return defaults[repo] || 1000
}

async function buildGitHubStarsData() {
  console.log('🚀 开始构建GitHub星标数据...')
  
  const results = {}
  
  // 并行获取所有仓库的星标数据
  const promises = repositories.map(repo => fetchGitHubStars(repo))
  const starData = await Promise.all(promises)
  
  // 整理数据
  starData.forEach(data => {
    results[data.repo] = data
  })
  
  // 确保public目录存在
  const publicDir = path.join(process.cwd(), 'public')
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true })
  }
  
  // 写入JSON文件
  const outputPath = path.join(publicDir, 'github-stars.json')
  fs.writeFileSync(outputPath, JSON.stringify(results, null, 2))
  
  console.log(`✅ GitHub星标数据已保存到: ${outputPath}`)
  console.log(`📊 总计处理 ${repositories.length} 个仓库`)
  
  return results
}

// 如果直接运行此脚本
if (require.main === module) {
  buildGitHubStarsData()
    .then(() => {
      console.log('🎉 GitHub星标数据构建完成!')
      process.exit(0)
    })
    .catch(error => {
      console.error('💥 构建失败:', error)
      process.exit(1)
    })
}

module.exports = { buildGitHubStarsData, fetchGitHubStars }
