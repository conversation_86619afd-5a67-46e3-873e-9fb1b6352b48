{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [], "headers": [], "dynamicRoutes": [{"page": "/[lang]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)(?:/)?$"}, {"page": "/[lang]/blog", "regex": "^/([^/]+?)/blog(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/blog(?:/)?$"}, {"page": "/[lang]/blog/[slug]", "regex": "^/([^/]+?)/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/blog/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[lang]/docs/[[...slug]]", "regex": "^/([^/]+?)/docs(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPlang": "nxtPlang", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlang>[^/]+?)/docs(?:/(?<nxtPslug>.+?))?(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}