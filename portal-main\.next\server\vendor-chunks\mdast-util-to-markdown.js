"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/configure.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/configure.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configure: () => (/* binding */ configure)\n/* harmony export */ });\n/**\n * @import {Options, State} from './types.js'\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {State} base\n * @param {Options} extension\n * @returns {State}\n */\nfunction configure(base, extension) {\n  let index = -1\n  /** @type {keyof Options} */\n  let key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'extensions': {\n          // Empty.\n          break\n        }\n\n        /* c8 ignore next 4 */\n        case 'unsafe': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'join': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'handlers': {\n          map(base[key], extension[key])\n          break\n        }\n\n        default: {\n          // @ts-expect-error: matches.\n          base.options[key] = extension[key]\n        }\n      }\n    }\n  }\n\n  return base\n}\n\n/**\n * @template T\n * @param {Array<T>} left\n * @param {Array<T> | null | undefined} right\n */\nfunction list(left, right) {\n  if (right) {\n    left.push(...right)\n  }\n}\n\n/**\n * @template T\n * @param {Record<string, T>} left\n * @param {Record<string, T> | null | undefined} right\n */\nfunction map(left, right) {\n  if (right) {\n    Object.assign(left, right)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/configure.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2Jsb2NrcXVvdGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxxQkFBcUI7QUFDakMsWUFBWSxrQkFBa0I7QUFDOUI7O0FBRUE7QUFDQSxXQUFXLFlBQVk7QUFDdkIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxLQUFLO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFxoYW5kbGVcXGJsb2NrcXVvdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtCbG9ja3F1b3RlLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqIEBpbXBvcnQge0luZm8sIE1hcCwgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge0Jsb2NrcXVvdGV9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJsb2NrcXVvdGUobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdibG9ja3F1b3RlJylcbiAgY29uc3QgdHJhY2tlciA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcbiAgdHJhY2tlci5tb3ZlKCc+ICcpXG4gIHRyYWNrZXIuc2hpZnQoMilcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5pbmRlbnRMaW5lcyhcbiAgICBzdGF0ZS5jb250YWluZXJGbG93KG5vZGUsIHRyYWNrZXIuY3VycmVudCgpKSxcbiAgICBtYXBcbiAgKVxuICBleGl0KClcbiAgcmV0dXJuIHZhbHVlXG59XG5cbi8qKiBAdHlwZSB7TWFwfSAqL1xuZnVuY3Rpb24gbWFwKGxpbmUsIF8sIGJsYW5rKSB7XG4gIHJldHVybiAnPicgKyAoYmxhbmsgPyAnJyA6ICcgJykgKyBsaW5lXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */\n\n\n\n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1QixZQUFZLGFBQWE7QUFDekI7O0FBRTBEOztBQUUxRDtBQUNBLFdBQVcsT0FBTztBQUNsQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0seUVBQWM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFxoYW5kbGVcXGJyZWFrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QnJlYWssIFBhcmVudHN9IGZyb20gJ21kYXN0J1xuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuaW1wb3J0IHtwYXR0ZXJuSW5TY29wZX0gZnJvbSAnLi4vdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7QnJlYWt9IF9cbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gXzFcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXJkQnJlYWsoXywgXzEsIHN0YXRlLCBpbmZvKSB7XG4gIGxldCBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBzdGF0ZS51bnNhZmUubGVuZ3RoKSB7XG4gICAgLy8gSWYgd2UgY2Fu4oCZdCBwdXQgZW9scyBpbiB0aGlzIGNvbnN0cnVjdCAoc2V0ZXh0IGhlYWRpbmdzLCB0YWJsZXMpLCB1c2UgYVxuICAgIC8vIHNwYWNlIGluc3RlYWQuXG4gICAgaWYgKFxuICAgICAgc3RhdGUudW5zYWZlW2luZGV4XS5jaGFyYWN0ZXIgPT09ICdcXG4nICYmXG4gICAgICBwYXR0ZXJuSW5TY29wZShzdGF0ZS5zdGFjaywgc3RhdGUudW5zYWZlW2luZGV4XSlcbiAgICApIHtcbiAgICAgIHJldHVybiAvWyBcXHRdLy50ZXN0KGluZm8uYmVmb3JlKSA/ICcnIDogJyAnXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuICdcXFxcXFxuJ1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(rsc)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */\n\n\n\n\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction code(node, _, state, info) {\n  const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */\n\n\n\n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction definition(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */\n\n\n\n\n\nemphasis.peek = emphasisPeek\n\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction emphasis(node, _, state, info) {\n  const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)\n  }\n\n  const after = tracker.move(marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @import {Html} from 'mdast'\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {Html} node\n * @returns {string}\n */\nfunction html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxNQUFNO0FBQ2xCOztBQUVBOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFxoYW5kbGVcXGh0bWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtIdG1sfSBmcm9tICdtZGFzdCdcbiAqL1xuXG5odG1sLnBlZWsgPSBodG1sUGVla1xuXG4vKipcbiAqIEBwYXJhbSB7SHRtbH0gbm9kZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGh0bWwobm9kZSkge1xuICByZXR1cm4gbm9kZS52YWx1ZSB8fCAnJ1xufVxuXG4vKipcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIGh0bWxQZWVrKCkge1xuICByZXR1cm4gJzwnXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */\n\n\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction image(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */\nconst handle = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpbmstcmVmZXJlbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6QixZQUFZLHdCQUF3QjtBQUNwQzs7QUFFQTs7QUFFQTtBQUNBLFdBQVcsZUFBZTtBQUMxQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGhhbmRsZVxcbGluay1yZWZlcmVuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge0xpbmtSZWZlcmVuY2UsIFBhcmVudHN9IGZyb20gJ21kYXN0J1xuICovXG5cbmxpbmtSZWZlcmVuY2UucGVlayA9IGxpbmtSZWZlcmVuY2VQZWVrXG5cbi8qKlxuICogQHBhcmFtIHtMaW5rUmVmZXJlbmNlfSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsaW5rUmVmZXJlbmNlKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IHR5cGUgPSBub2RlLnJlZmVyZW5jZVR5cGVcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdsaW5rUmVmZXJlbmNlJylcbiAgbGV0IHN1YmV4aXQgPSBzdGF0ZS5lbnRlcignbGFiZWwnKVxuICBjb25zdCB0cmFja2VyID0gc3RhdGUuY3JlYXRlVHJhY2tlcihpbmZvKVxuICBsZXQgdmFsdWUgPSB0cmFja2VyLm1vdmUoJ1snKVxuICBjb25zdCB0ZXh0ID0gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcobm9kZSwge1xuICAgIGJlZm9yZTogdmFsdWUsXG4gICAgYWZ0ZXI6ICddJyxcbiAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICB9KVxuICB2YWx1ZSArPSB0cmFja2VyLm1vdmUodGV4dCArICddWycpXG5cbiAgc3ViZXhpdCgpXG4gIC8vIEhpZGUgdGhlIGZhY3QgdGhhdCB3ZeKAmXJlIGluIHBocmFzaW5nLCBiZWNhdXNlIGVzY2FwZXMgZG9u4oCZdCB3b3JrLlxuICBjb25zdCBzdGFjayA9IHN0YXRlLnN0YWNrXG4gIHN0YXRlLnN0YWNrID0gW11cbiAgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdyZWZlcmVuY2UnKVxuICAvLyBOb3RlOiBmb3IgcHJvcGVyIHRyYWNraW5nLCB3ZSBzaG91bGQgcmVzZXQgdGhlIG91dHB1dCBwb3NpdGlvbnMgd2hlbiB3ZSBlbmRcbiAgLy8gdXAgbWFraW5nIGEgYHNob3J0Y3V0YCByZWZlcmVuY2UsIGJlY2F1c2UgdGhlbiB0aGVyZSBpcyBubyBicmFjZSBvdXRwdXQuXG4gIC8vIFByYWN0aWNhbGx5LCBpbiB0aGF0IGNhc2UsIHRoZXJlIGlzIG5vIGNvbnRlbnQsIHNvIGl0IGRvZXNu4oCZdCBtYXR0ZXIgdGhhdFxuICAvLyB3ZeKAmXZlIHRyYWNrZWQgb25lIHRvbyBtYW55IGNoYXJhY3RlcnMuXG4gIGNvbnN0IHJlZmVyZW5jZSA9IHN0YXRlLnNhZmUoc3RhdGUuYXNzb2NpYXRpb25JZChub2RlKSwge1xuICAgIGJlZm9yZTogdmFsdWUsXG4gICAgYWZ0ZXI6ICddJyxcbiAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICB9KVxuICBzdWJleGl0KClcbiAgc3RhdGUuc3RhY2sgPSBzdGFja1xuICBleGl0KClcblxuICBpZiAodHlwZSA9PT0gJ2Z1bGwnIHx8ICF0ZXh0IHx8IHRleHQgIT09IHJlZmVyZW5jZSkge1xuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShyZWZlcmVuY2UgKyAnXScpXG4gIH0gZWxzZSBpZiAodHlwZSA9PT0gJ3Nob3J0Y3V0Jykge1xuICAgIC8vIFJlbW92ZSB0aGUgdW53YW50ZWQgYFtgLlxuICAgIHZhbHVlID0gdmFsdWUuc2xpY2UoMCwgLTEpXG4gIH0gZWxzZSB7XG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCddJylcbiAgfVxuXG4gIHJldHVybiB2YWx1ZVxufVxuXG4vKipcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIGxpbmtSZWZlcmVuY2VQZWVrKCkge1xuICByZXR1cm4gJ1snXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */\n\n\n\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction link(node, _, state, info) {\n  const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? '<' : '['\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQSxZQUFZLGFBQWE7QUFDekIsWUFBWSxlQUFlO0FBQzNCLFlBQVksTUFBTTtBQUNsQjs7QUFFaUQ7QUFDc0I7O0FBRXZFOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcscUJBQXFCO0FBQ2hDLFdBQVcsT0FBTztBQUNsQixXQUFXLE1BQU07QUFDakIsYUFBYTtBQUNiO0FBQ087QUFDUCxnQkFBZ0IsZ0VBQVU7QUFDMUI7QUFDQTtBQUNBLGFBQWEsTUFBTTtBQUNuQjtBQUNBLGFBQWEsTUFBTTtBQUNuQjs7QUFFQSxNQUFNLHNGQUFvQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdEQUFnRDtBQUM1RTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxrQ0FBa0MsT0FBTztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcscUJBQXFCO0FBQ2hDLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDQTtBQUNBLFNBQVMsc0ZBQW9CO0FBQzdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGhhbmRsZVxcbGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7TGluaywgUGFyZW50c30gZnJvbSAnbWRhc3QnXG4gKiBAaW1wb3J0IHtFeGl0fSBmcm9tICcuLi90eXBlcy5qcydcbiAqL1xuXG5pbXBvcnQge2NoZWNrUXVvdGV9IGZyb20gJy4uL3V0aWwvY2hlY2stcXVvdGUuanMnXG5pbXBvcnQge2Zvcm1hdExpbmtBc0F1dG9saW5rfSBmcm9tICcuLi91dGlsL2Zvcm1hdC1saW5rLWFzLWF1dG9saW5rLmpzJ1xuXG5saW5rLnBlZWsgPSBsaW5rUGVla1xuXG4vKipcbiAqIEBwYXJhbSB7TGlua30gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGluayhub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBxdW90ZSA9IGNoZWNrUXVvdGUoc3RhdGUpXG4gIGNvbnN0IHN1ZmZpeCA9IHF1b3RlID09PSAnXCInID8gJ1F1b3RlJyA6ICdBcG9zdHJvcGhlJ1xuICBjb25zdCB0cmFja2VyID0gc3RhdGUuY3JlYXRlVHJhY2tlcihpbmZvKVxuICAvKiogQHR5cGUge0V4aXR9ICovXG4gIGxldCBleGl0XG4gIC8qKiBAdHlwZSB7RXhpdH0gKi9cbiAgbGV0IHN1YmV4aXRcblxuICBpZiAoZm9ybWF0TGlua0FzQXV0b2xpbmsobm9kZSwgc3RhdGUpKSB7XG4gICAgLy8gSGlkZSB0aGUgZmFjdCB0aGF0IHdl4oCZcmUgaW4gcGhyYXNpbmcsIGJlY2F1c2UgZXNjYXBlcyBkb27igJl0IHdvcmsuXG4gICAgY29uc3Qgc3RhY2sgPSBzdGF0ZS5zdGFja1xuICAgIHN0YXRlLnN0YWNrID0gW11cbiAgICBleGl0ID0gc3RhdGUuZW50ZXIoJ2F1dG9saW5rJylcbiAgICBsZXQgdmFsdWUgPSB0cmFja2VyLm1vdmUoJzwnKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShcbiAgICAgIHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIHtcbiAgICAgICAgYmVmb3JlOiB2YWx1ZSxcbiAgICAgICAgYWZ0ZXI6ICc+JyxcbiAgICAgICAgLi4udHJhY2tlci5jdXJyZW50KClcbiAgICAgIH0pXG4gICAgKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnPicpXG4gICAgZXhpdCgpXG4gICAgc3RhdGUuc3RhY2sgPSBzdGFja1xuICAgIHJldHVybiB2YWx1ZVxuICB9XG5cbiAgZXhpdCA9IHN0YXRlLmVudGVyKCdsaW5rJylcbiAgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdsYWJlbCcpXG4gIGxldCB2YWx1ZSA9IHRyYWNrZXIubW92ZSgnWycpXG4gIHZhbHVlICs9IHRyYWNrZXIubW92ZShcbiAgICBzdGF0ZS5jb250YWluZXJQaHJhc2luZyhub2RlLCB7XG4gICAgICBiZWZvcmU6IHZhbHVlLFxuICAgICAgYWZ0ZXI6ICddKCcsXG4gICAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICAgIH0pXG4gIClcbiAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCddKCcpXG4gIHN1YmV4aXQoKVxuXG4gIGlmIChcbiAgICAvLyBJZiB0aGVyZeKAmXMgbm8gdXJsIGJ1dCB0aGVyZSBpcyBhIHRpdGxl4oCmXG4gICAgKCFub2RlLnVybCAmJiBub2RlLnRpdGxlKSB8fFxuICAgIC8vIElmIHRoZXJlIGFyZSBjb250cm9sIGNoYXJhY3RlcnMgb3Igd2hpdGVzcGFjZS5cbiAgICAvW1xcMC0gXFx1MDA3Rl0vLnRlc3Qobm9kZS51cmwpXG4gICkge1xuICAgIHN1YmV4aXQgPSBzdGF0ZS5lbnRlcignZGVzdGluYXRpb25MaXRlcmFsJylcbiAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoJzwnKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShcbiAgICAgIHN0YXRlLnNhZmUobm9kZS51cmwsIHtiZWZvcmU6IHZhbHVlLCBhZnRlcjogJz4nLCAuLi50cmFja2VyLmN1cnJlbnQoKX0pXG4gICAgKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnPicpXG4gIH0gZWxzZSB7XG4gICAgLy8gTm8gd2hpdGVzcGFjZSwgcmF3IGlzIHByZXR0aWVyLlxuICAgIHN1YmV4aXQgPSBzdGF0ZS5lbnRlcignZGVzdGluYXRpb25SYXcnKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShcbiAgICAgIHN0YXRlLnNhZmUobm9kZS51cmwsIHtcbiAgICAgICAgYmVmb3JlOiB2YWx1ZSxcbiAgICAgICAgYWZ0ZXI6IG5vZGUudGl0bGUgPyAnICcgOiAnKScsXG4gICAgICAgIC4uLnRyYWNrZXIuY3VycmVudCgpXG4gICAgICB9KVxuICAgIClcbiAgfVxuXG4gIHN1YmV4aXQoKVxuXG4gIGlmIChub2RlLnRpdGxlKSB7XG4gICAgc3ViZXhpdCA9IHN0YXRlLmVudGVyKGB0aXRsZSR7c3VmZml4fWApXG4gICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCcgJyArIHF1b3RlKVxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShcbiAgICAgIHN0YXRlLnNhZmUobm9kZS50aXRsZSwge1xuICAgICAgICBiZWZvcmU6IHZhbHVlLFxuICAgICAgICBhZnRlcjogcXVvdGUsXG4gICAgICAgIC4uLnRyYWNrZXIuY3VycmVudCgpXG4gICAgICB9KVxuICAgIClcbiAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUocXVvdGUpXG4gICAgc3ViZXhpdCgpXG4gIH1cblxuICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoJyknKVxuXG4gIGV4aXQoKVxuICByZXR1cm4gdmFsdWVcbn1cblxuLyoqXG4gKiBAcGFyYW0ge0xpbmt9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIGxpbmtQZWVrKG5vZGUsIF8sIHN0YXRlKSB7XG4gIHJldHVybiBmb3JtYXRMaW5rQXNBdXRvbGluayhub2RlLCBzdGF0ZSkgPyAnPCcgOiAnWydcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */\n\n\n\n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction listItem(node, parent, state, info) {\n  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)\n  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2xpc3QtaXRlbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLFlBQVksa0JBQWtCO0FBQzlCLFlBQVksbUJBQW1CO0FBQy9COztBQUVtRDtBQUNrQjs7QUFFckU7QUFDQSxXQUFXLFVBQVU7QUFDckIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQLHlCQUF5QixvRkFBbUI7QUFDNUMsc0NBQXNDLGtFQUFXOztBQUVqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxhQUFhLEtBQUs7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGhhbmRsZVxcbGlzdC1pdGVtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgTWFwLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge0xpc3RJdGVtLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge2NoZWNrQnVsbGV0fSBmcm9tICcuLi91dGlsL2NoZWNrLWJ1bGxldC5qcydcbmltcG9ydCB7Y2hlY2tMaXN0SXRlbUluZGVudH0gZnJvbSAnLi4vdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7TGlzdEl0ZW19IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gcGFyZW50XG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gbGlzdEl0ZW0obm9kZSwgcGFyZW50LCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBsaXN0SXRlbUluZGVudCA9IGNoZWNrTGlzdEl0ZW1JbmRlbnQoc3RhdGUpXG4gIGxldCBidWxsZXQgPSBzdGF0ZS5idWxsZXRDdXJyZW50IHx8IGNoZWNrQnVsbGV0KHN0YXRlKVxuXG4gIC8vIEFkZCB0aGUgbWFya2VyIHZhbHVlIGZvciBvcmRlcmVkIGxpc3RzLlxuICBpZiAocGFyZW50ICYmIHBhcmVudC50eXBlID09PSAnbGlzdCcgJiYgcGFyZW50Lm9yZGVyZWQpIHtcbiAgICBidWxsZXQgPVxuICAgICAgKHR5cGVvZiBwYXJlbnQuc3RhcnQgPT09ICdudW1iZXInICYmIHBhcmVudC5zdGFydCA+IC0xXG4gICAgICAgID8gcGFyZW50LnN0YXJ0XG4gICAgICAgIDogMSkgK1xuICAgICAgKHN0YXRlLm9wdGlvbnMuaW5jcmVtZW50TGlzdE1hcmtlciA9PT0gZmFsc2VcbiAgICAgICAgPyAwXG4gICAgICAgIDogcGFyZW50LmNoaWxkcmVuLmluZGV4T2Yobm9kZSkpICtcbiAgICAgIGJ1bGxldFxuICB9XG5cbiAgbGV0IHNpemUgPSBidWxsZXQubGVuZ3RoICsgMVxuXG4gIGlmIChcbiAgICBsaXN0SXRlbUluZGVudCA9PT0gJ3RhYicgfHxcbiAgICAobGlzdEl0ZW1JbmRlbnQgPT09ICdtaXhlZCcgJiZcbiAgICAgICgocGFyZW50ICYmIHBhcmVudC50eXBlID09PSAnbGlzdCcgJiYgcGFyZW50LnNwcmVhZCkgfHwgbm9kZS5zcHJlYWQpKVxuICApIHtcbiAgICBzaXplID0gTWF0aC5jZWlsKHNpemUgLyA0KSAqIDRcbiAgfVxuXG4gIGNvbnN0IHRyYWNrZXIgPSBzdGF0ZS5jcmVhdGVUcmFja2VyKGluZm8pXG4gIHRyYWNrZXIubW92ZShidWxsZXQgKyAnICcucmVwZWF0KHNpemUgLSBidWxsZXQubGVuZ3RoKSlcbiAgdHJhY2tlci5zaGlmdChzaXplKVxuICBjb25zdCBleGl0ID0gc3RhdGUuZW50ZXIoJ2xpc3RJdGVtJylcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5pbmRlbnRMaW5lcyhcbiAgICBzdGF0ZS5jb250YWluZXJGbG93KG5vZGUsIHRyYWNrZXIuY3VycmVudCgpKSxcbiAgICBtYXBcbiAgKVxuICBleGl0KClcblxuICByZXR1cm4gdmFsdWVcblxuICAvKiogQHR5cGUge01hcH0gKi9cbiAgZnVuY3Rpb24gbWFwKGxpbmUsIGluZGV4LCBibGFuaykge1xuICAgIGlmIChpbmRleCkge1xuICAgICAgcmV0dXJuIChibGFuayA/ICcnIDogJyAnLnJlcGVhdChzaXplKSkgKyBsaW5lXG4gICAgfVxuXG4gICAgcmV0dXJuIChibGFuayA/IGJ1bGxldCA6IGJ1bGxldCArICcgJy5yZXBlYXQoc2l6ZSAtIGJ1bGxldC5sZW5ndGgpKSArIGxpbmVcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */\n\n\n\n\n\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? bullet === '.'\n      ? ')'\n      : '.'\n    : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state)\n  let useDifferentMarker =\n    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGFBQWE7QUFDekIsWUFBWSxvQkFBb0I7QUFDaEM7O0FBRUE7QUFDQSxXQUFXLFdBQVc7QUFDdEIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGhhbmRsZVxccGFyYWdyYXBoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJhZ3JhcGgsIFBhcmVudHN9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtQYXJhZ3JhcGh9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcmFncmFwaChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBleGl0ID0gc3RhdGUuZW50ZXIoJ3BhcmFncmFwaCcpXG4gIGNvbnN0IHN1YmV4aXQgPSBzdGF0ZS5lbnRlcigncGhyYXNpbmcnKVxuICBjb25zdCB2YWx1ZSA9IHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIGluZm8pXG4gIHN1YmV4aXQoKVxuICBleGl0KClcbiAgcmV0dXJuIHZhbHVlXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(rsc)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */\n\n\n\n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some(function (d) {\n    return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d)\n  })\n\n  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  return container.call(state, node, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6QixZQUFZLGVBQWU7QUFDM0I7O0FBRTRDOztBQUU1QztBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBLFdBQVcsNkRBQVE7QUFDbkIsR0FBRzs7QUFFSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcaGFuZGxlXFxyb290LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBSb290fSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge3BocmFzaW5nfSBmcm9tICdtZGFzdC11dGlsLXBocmFzaW5nJ1xuXG4vKipcbiAqIEBwYXJhbSB7Um9vdH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICAvLyBOb3RlOiBgaHRtbGAgbm9kZXMgYXJlIGFtYmlndW91cy5cbiAgY29uc3QgaGFzUGhyYXNpbmcgPSBub2RlLmNoaWxkcmVuLnNvbWUoZnVuY3Rpb24gKGQpIHtcbiAgICByZXR1cm4gcGhyYXNpbmcoZClcbiAgfSlcblxuICBjb25zdCBjb250YWluZXIgPSBoYXNQaHJhc2luZyA/IHN0YXRlLmNvbnRhaW5lclBocmFzaW5nIDogc3RhdGUuY29udGFpbmVyRmxvd1xuICByZXR1cm4gY29udGFpbmVyLmNhbGwoc3RhdGUsIG5vZGUsIGluZm8pXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */\n\n\n\n\n\nstrong.peek = strongPeek\n\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction strong(node, _, state, info) {\n  const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker + marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail)\n  }\n\n  const after = tracker.move(marker + marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */\n\n/**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxhQUFhO0FBQ3pCLFlBQVksZUFBZTtBQUMzQjs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLHFCQUFxQjtBQUNoQyxXQUFXLE9BQU87QUFDbEIsV0FBVyxNQUFNO0FBQ2pCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFxoYW5kbGVcXHRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFRleHR9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtUZXh0fSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0KG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIHJldHVybiBzdGF0ZS5zYWZlKG5vZGUudmFsdWUsIGluZm8pXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */\n\n\n\n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction thematicBreak(_, _1, state) {\n  const value = (\n    (0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksd0JBQXdCO0FBQ3BDOztBQUVvRTtBQUNyQjs7QUFFL0M7QUFDQSxXQUFXLGVBQWU7QUFDMUIsV0FBVyxxQkFBcUI7QUFDaEMsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSxJQUFJLDhEQUFTO0FBQ2IsV0FBVyxtRkFBbUI7O0FBRTlCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcaGFuZGxlXFx0aGVtYXRpYy1icmVhay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyZW50cywgVGhlbWF0aWNCcmVha30gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtjaGVja1J1bGVSZXBldGl0aW9ufSBmcm9tICcuLi91dGlsL2NoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcydcbmltcG9ydCB7Y2hlY2tSdWxlfSBmcm9tICcuLi91dGlsL2NoZWNrLXJ1bGUuanMnXG5cbi8qKlxuICogQHBhcmFtIHtUaGVtYXRpY0JyZWFrfSBfXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoXywgXzEsIHN0YXRlKSB7XG4gIGNvbnN0IHZhbHVlID0gKFxuICAgIGNoZWNrUnVsZShzdGF0ZSkgKyAoc3RhdGUub3B0aW9ucy5ydWxlU3BhY2VzID8gJyAnIDogJycpXG4gICkucmVwZWF0KGNoZWNrUnVsZVJlcGV0aXRpb24oc3RhdGUpKVxuXG4gIHJldHVybiBzdGF0ZS5vcHRpb25zLnJ1bGVTcGFjZXMgPyB2YWx1ZS5zbGljZSgwLCAtMSkgOiB2YWx1ZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toMarkdown: () => (/* binding */ toMarkdown)\n/* harmony export */ });\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! zwitch */ \"(rsc)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _configure_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./configure.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/configure.js\");\n/* harmony import */ var _handle_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./handle/index.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\");\n/* harmony import */ var _join_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./join.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/join.js\");\n/* harmony import */ var _unsafe_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./unsafe.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\");\n/* harmony import */ var _util_association_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/association.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/association.js\");\n/* harmony import */ var _util_compile_pattern_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/compile-pattern.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js\");\n/* harmony import */ var _util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./util/container-phrasing.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var _util_container_flow_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./util/container-flow.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\");\n/* harmony import */ var _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./util/indent-lines.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\");\n/* harmony import */ var _util_safe_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/safe.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\");\n/* harmony import */ var _util_track_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/track.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @import {Info, Join, Options, SafeConfig, State} from 'mdast-util-to-markdown'\n * @import {Nodes} from 'mdast'\n * @import {Enter, FlowParents, PhrasingParents, TrackFields} from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Turn an mdast syntax tree into markdown.\n *\n * @param {Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized markdown representing `tree`.\n */\nfunction toMarkdown(tree, options) {\n  const settings = options || {}\n  /** @type {State} */\n  const state = {\n    associationId: _util_association_js__WEBPACK_IMPORTED_MODULE_0__.association,\n    containerPhrasing: containerPhrasingBound,\n    containerFlow: containerFlowBound,\n    createTracker: _util_track_js__WEBPACK_IMPORTED_MODULE_1__.track,\n    compilePattern: _util_compile_pattern_js__WEBPACK_IMPORTED_MODULE_2__.compilePattern,\n    enter,\n    // @ts-expect-error: GFM / frontmatter are typed in `mdast` but not defined\n    // here.\n    handlers: {..._handle_index_js__WEBPACK_IMPORTED_MODULE_3__.handle},\n    // @ts-expect-error: add `handle` in a second.\n    handle: undefined,\n    indentLines: _util_indent_lines_js__WEBPACK_IMPORTED_MODULE_4__.indentLines,\n    indexStack: [],\n    join: [..._join_js__WEBPACK_IMPORTED_MODULE_5__.join],\n    options: {},\n    safe: safeBound,\n    stack: [],\n    unsafe: [..._unsafe_js__WEBPACK_IMPORTED_MODULE_6__.unsafe]\n  }\n\n  ;(0,_configure_js__WEBPACK_IMPORTED_MODULE_7__.configure)(state, settings)\n\n  if (state.options.tightDefinitions) {\n    state.join.push(joinDefinition)\n  }\n\n  state.handle = (0,zwitch__WEBPACK_IMPORTED_MODULE_8__.zwitch)('type', {\n    invalid,\n    unknown,\n    handlers: state.handlers\n  })\n\n  let result = state.handle(tree, undefined, state, {\n    before: '\\n',\n    after: '\\n',\n    now: {line: 1, column: 1},\n    lineShift: 0\n  })\n\n  if (\n    result &&\n    result.charCodeAt(result.length - 1) !== 10 &&\n    result.charCodeAt(result.length - 1) !== 13\n  ) {\n    result += '\\n'\n  }\n\n  return result\n\n  /** @type {Enter} */\n  function enter(name) {\n    state.stack.push(name)\n    return exit\n\n    /**\n     * @returns {undefined}\n     */\n    function exit() {\n      state.stack.pop()\n    }\n  }\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction invalid(value) {\n  throw new Error('Cannot handle value `' + value + '`, expected node')\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction unknown(value) {\n  // Always a node.\n  const node = /** @type {Nodes} */ (value)\n  throw new Error('Cannot handle unknown node `' + node.type + '`')\n}\n\n/** @type {Join} */\nfunction joinDefinition(left, right) {\n  // No blank line between adjacent definitions.\n  if (left.type === 'definition' && left.type === right.type) {\n    return 0\n  }\n}\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {PhrasingParents} parent\n *   Parent of flow nodes.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasingBound(parent, info) {\n  return (0,_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_9__.containerPhrasing)(parent, this, info)\n}\n\n/**\n * Serialize the children of a parent that contains flow children.\n *\n * These children will typically be joined by blank lines.\n * What they are joined by exactly is defined by `Join` functions.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {FlowParents} parent\n *   Parent of flow nodes.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlowBound(parent, info) {\n  return (0,_util_container_flow_js__WEBPACK_IMPORTED_MODULE_10__.containerFlow)(parent, this, info)\n}\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} value\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safeBound(value, config) {\n  return (0,_util_safe_js__WEBPACK_IMPORTED_MODULE_11__.safe)(this, value, config)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/join.js":
/*!*********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/join.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   join: () => (/* binding */ join)\n/* harmony export */ });\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/format-code-as-indented.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-heading-as-setext.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Join} from 'mdast-util-to-markdown'\n */\n\n\n\n\n/** @type {Array<Join>} */\nconst join = [joinDefaults]\n\n/** @type {Join} */\nfunction joinDefaults(left, right, parent, state) {\n  // Indented code after list or another indented code.\n  if (\n    right.type === 'code' &&\n    (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(right, state) &&\n    (left.type === 'list' ||\n      (left.type === right.type && (0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_0__.formatCodeAsIndented)(left, state)))\n  ) {\n    return false\n  }\n\n  // Join children of a list or an item.\n  // In which case, `parent` has a `spread` field.\n  if ('spread' in parent && typeof parent.spread === 'boolean') {\n    if (\n      left.type === 'paragraph' &&\n      // Two paragraphs.\n      (left.type === right.type ||\n        right.type === 'definition' ||\n        // Paragraph followed by a setext heading.\n        (right.type === 'heading' && (0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_1__.formatHeadingAsSetext)(right, state)))\n    ) {\n      return\n    }\n\n    return parent.spread ? 1 : 0\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvam9pbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBLFlBQVksTUFBTTtBQUNsQjs7QUFFc0U7QUFDRTs7QUFFeEUsV0FBVyxhQUFhO0FBQ2pCOztBQUVQLFdBQVcsTUFBTTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksc0ZBQW9CO0FBQ3hCO0FBQ0EsbUNBQW1DLHNGQUFvQjtBQUN2RDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHdGQUFxQjtBQUMxRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXGpvaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtKb2lufSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbmltcG9ydCB7Zm9ybWF0Q29kZUFzSW5kZW50ZWR9IGZyb20gJy4vdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcydcbmltcG9ydCB7Zm9ybWF0SGVhZGluZ0FzU2V0ZXh0fSBmcm9tICcuL3V0aWwvZm9ybWF0LWhlYWRpbmctYXMtc2V0ZXh0LmpzJ1xuXG4vKiogQHR5cGUge0FycmF5PEpvaW4+fSAqL1xuZXhwb3J0IGNvbnN0IGpvaW4gPSBbam9pbkRlZmF1bHRzXVxuXG4vKiogQHR5cGUge0pvaW59ICovXG5mdW5jdGlvbiBqb2luRGVmYXVsdHMobGVmdCwgcmlnaHQsIHBhcmVudCwgc3RhdGUpIHtcbiAgLy8gSW5kZW50ZWQgY29kZSBhZnRlciBsaXN0IG9yIGFub3RoZXIgaW5kZW50ZWQgY29kZS5cbiAgaWYgKFxuICAgIHJpZ2h0LnR5cGUgPT09ICdjb2RlJyAmJlxuICAgIGZvcm1hdENvZGVBc0luZGVudGVkKHJpZ2h0LCBzdGF0ZSkgJiZcbiAgICAobGVmdC50eXBlID09PSAnbGlzdCcgfHxcbiAgICAgIChsZWZ0LnR5cGUgPT09IHJpZ2h0LnR5cGUgJiYgZm9ybWF0Q29kZUFzSW5kZW50ZWQobGVmdCwgc3RhdGUpKSlcbiAgKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICAvLyBKb2luIGNoaWxkcmVuIG9mIGEgbGlzdCBvciBhbiBpdGVtLlxuICAvLyBJbiB3aGljaCBjYXNlLCBgcGFyZW50YCBoYXMgYSBgc3ByZWFkYCBmaWVsZC5cbiAgaWYgKCdzcHJlYWQnIGluIHBhcmVudCAmJiB0eXBlb2YgcGFyZW50LnNwcmVhZCA9PT0gJ2Jvb2xlYW4nKSB7XG4gICAgaWYgKFxuICAgICAgbGVmdC50eXBlID09PSAncGFyYWdyYXBoJyAmJlxuICAgICAgLy8gVHdvIHBhcmFncmFwaHMuXG4gICAgICAobGVmdC50eXBlID09PSByaWdodC50eXBlIHx8XG4gICAgICAgIHJpZ2h0LnR5cGUgPT09ICdkZWZpbml0aW9uJyB8fFxuICAgICAgICAvLyBQYXJhZ3JhcGggZm9sbG93ZWQgYnkgYSBzZXRleHQgaGVhZGluZy5cbiAgICAgICAgKHJpZ2h0LnR5cGUgPT09ICdoZWFkaW5nJyAmJiBmb3JtYXRIZWFkaW5nQXNTZXRleHQocmlnaHQsIHN0YXRlKSkpXG4gICAgKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICByZXR1cm4gcGFyZW50LnNwcmVhZCA/IDEgOiAwXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/join.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/unsafe.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/unsafe.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unsafe: () => (/* binding */ unsafe)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain things like attention (emphasis, strong), images, or links.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * @type {Array<ConstructName>}\n */\nconst fullPhrasingSpans = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\n/** @type {Array<Unsafe>} */\nconst unsafe = [\n  {character: '\\t', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: '\\t', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: '\\t',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  {\n    character: '\\r',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {\n    character: '\\n',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {character: ' ', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: ' ', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: ' ',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  // An exclamation mark can start an image, if it is followed by a link or\n  // a link reference.\n  {\n    character: '!',\n    after: '\\\\[',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A quote can break out of a title.\n  {character: '\"', inConstruct: 'titleQuote'},\n  // A number sign could start an ATX heading if it starts a line.\n  {atBreak: true, character: '#'},\n  {character: '#', inConstruct: 'headingAtx', after: '(?:[\\r\\n]|$)'},\n  // Dollar sign and percentage are not used in markdown.\n  // An ampersand could start a character reference.\n  {character: '&', after: '[#A-Za-z]', inConstruct: 'phrasing'},\n  // An apostrophe can break out of a title.\n  {character: \"'\", inConstruct: 'titleApostrophe'},\n  // A left paren could break out of a destination raw.\n  {character: '(', inConstruct: 'destinationRaw'},\n  // A left paren followed by `]` could make something into a link or image.\n  {\n    before: '\\\\]',\n    character: '(',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A right paren could start a list item or break out of a destination\n  // raw.\n  {atBreak: true, before: '\\\\d+', character: ')'},\n  {character: ')', inConstruct: 'destinationRaw'},\n  // An asterisk can start thematic breaks, list items, emphasis, strong.\n  {atBreak: true, character: '*', after: '(?:[ \\t\\r\\n*])'},\n  {character: '*', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A plus sign could start a list item.\n  {atBreak: true, character: '+', after: '(?:[ \\t\\r\\n])'},\n  // A dash can start thematic breaks, list items, and setext heading\n  // underlines.\n  {atBreak: true, character: '-', after: '(?:[ \\t\\r\\n-])'},\n  // A dot could start a list item.\n  {atBreak: true, before: '\\\\d+', character: '.', after: '(?:[ \\t\\r\\n]|$)'},\n  // Slash, colon, and semicolon are not used in markdown for constructs.\n  // A less than can start html (flow or text) or an autolink.\n  // HTML could start with an exclamation mark (declaration, cdata, comment),\n  // slash (closing tag), question mark (instruction), or a letter (tag).\n  // An autolink also starts with a letter.\n  // Finally, it could break out of a destination literal.\n  {atBreak: true, character: '<', after: '[!/?A-Za-z]'},\n  {\n    character: '<',\n    after: '[!/?A-Za-z]',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  {character: '<', inConstruct: 'destinationLiteral'},\n  // An equals to can start setext heading underlines.\n  {atBreak: true, character: '='},\n  // A greater than can start block quotes and it can break out of a\n  // destination literal.\n  {atBreak: true, character: '>'},\n  {character: '>', inConstruct: 'destinationLiteral'},\n  // Question mark and at sign are not used in markdown for constructs.\n  // A left bracket can start definitions, references, labels,\n  {atBreak: true, character: '['},\n  {character: '[', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  {character: '[', inConstruct: ['label', 'reference']},\n  // A backslash can start an escape (when followed by punctuation) or a\n  // hard break (when followed by an eol).\n  // Note: typical escapes are handled in `safe`!\n  {character: '\\\\', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  // A right bracket can exit labels.\n  {character: ']', inConstruct: ['label', 'reference']},\n  // Caret is not used in markdown for constructs.\n  // An underscore can start emphasis, strong, or a thematic break.\n  {atBreak: true, character: '_'},\n  {character: '_', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A grave accent can start code (fenced or text), or it can break out of\n  // a grave accent code fence.\n  {atBreak: true, character: '`'},\n  {\n    character: '`',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedMetaGraveAccent']\n  },\n  {character: '`', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // Left brace, vertical bar, right brace are not used in markdown for\n  // constructs.\n  // A tilde can start code (fenced).\n  {atBreak: true, character: '~'}\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/unsafe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/association.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/association.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   association: () => (/* binding */ association)\n/* harmony export */ });\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-decode-string */ \"(rsc)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/**\n * @import {AssociationId} from '../types.js'\n */\n\n\n\n/**\n * Get an identifier from an association to match it to others.\n *\n * Associations are nodes that match to something else through an ID:\n * <https://github.com/syntax-tree/mdast#association>.\n *\n * The `label` of an association is the string value: character escapes and\n * references work, and casing is intact.\n * The `identifier` is used to match one association to another:\n * controversially, character escapes and references don’t work in this\n * matching: `&copy;` does not match `©`, and `\\+` does not match `+`.\n *\n * But casing is ignored (and whitespace) is trimmed and collapsed: ` A\\nb`\n * matches `a b`.\n * So, we do prefer the label when figuring out how we’re going to serialize:\n * it has whitespace, casing, and we can ignore most useless character\n * escapes and all character references.\n *\n * @type {AssociationId}\n */\nfunction association(node) {\n  if (node.label || !node.identifier) {\n    return node.label || ''\n  }\n\n  return (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_0__.decodeString)(node.identifier)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/association.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nfunction checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcY2hlY2stYnVsbGV0LW9yZGVyZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXRPcmRlcmVkJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXRPcmRlcmVkKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0T3JkZXJlZCB8fCAnLidcblxuICBpZiAobWFya2VyICE9PSAnLicgJiYgbWFya2VyICE9PSAnKScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldE9yZGVyZWRgLCBleHBlY3RlZCBgLmAgb3IgYClgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBulletOther(state) {\n  const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3RoZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUU2Qzs7QUFFN0M7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUCxpQkFBaUIsNkRBQVc7QUFDNUI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWJ1bGxldC1vdGhlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbmltcG9ydCB7Y2hlY2tCdWxsZXR9IGZyb20gJy4vY2hlY2stYnVsbGV0LmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldE90aGVyKHN0YXRlKSB7XG4gIGNvbnN0IGJ1bGxldCA9IGNoZWNrQnVsbGV0KHN0YXRlKVxuICBjb25zdCBidWxsZXRPdGhlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0T3RoZXJcblxuICBpZiAoIWJ1bGxldE90aGVyKSB7XG4gICAgcmV0dXJuIGJ1bGxldCA9PT0gJyonID8gJy0nIDogJyonXG4gIH1cblxuICBpZiAoYnVsbGV0T3RoZXIgIT09ICcqJyAmJiBidWxsZXRPdGhlciAhPT0gJysnICYmIGJ1bGxldE90aGVyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgYnVsbGV0T3RoZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0T3RoZXJgLCBleHBlY3RlZCBgKmAsIGArYCwgb3IgYC1gJ1xuICAgIClcbiAgfVxuXG4gIGlmIChidWxsZXRPdGhlciA9PT0gYnVsbGV0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0V4cGVjdGVkIGBidWxsZXRgIChgJyArXG4gICAgICAgIGJ1bGxldCArXG4gICAgICAgICdgKSBhbmQgYGJ1bGxldE90aGVyYCAoYCcgK1xuICAgICAgICBidWxsZXRPdGhlciArXG4gICAgICAgICdgKSB0byBiZSBkaWZmZXJlbnQnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIGJ1bGxldE90aGVyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWJ1bGxldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2J1bGxldCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0KHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0IHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICcrJyAmJiBtYXJrZXIgIT09ICctJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0YCwgZXhwZWN0ZWQgYCpgLCBgK2AsIG9yIGAtYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nfunction checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcY2hlY2stZW1waGFzaXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydlbXBoYXNpcyddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrRW1waGFzaXMoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5lbXBoYXNpcyB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBlbXBoYXNpcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmVtcGhhc2lzYCwgZXhwZWN0ZWQgYCpgLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nfunction checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcY2hlY2stZmVuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydmZW5jZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrRmVuY2Uoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5mZW5jZSB8fCAnYCdcblxuICBpZiAobWFya2VyICE9PSAnYCcgJiYgbWFya2VyICE9PSAnficpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBjb2RlIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuZmVuY2VgLCBleHBlY3RlZCBgYCBgIGBgIG9yIGB+YCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nfunction checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'one'\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snbGlzdEl0ZW1JbmRlbnQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0xpc3RJdGVtSW5kZW50KHN0YXRlKSB7XG4gIGNvbnN0IHN0eWxlID0gc3RhdGUub3B0aW9ucy5saXN0SXRlbUluZGVudCB8fCAnb25lJ1xuXG4gIGlmIChzdHlsZSAhPT0gJ3RhYicgJiYgc3R5bGUgIT09ICdvbmUnICYmIHN0eWxlICE9PSAnbWl4ZWQnKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIHN0eWxlICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmxpc3RJdGVtSW5kZW50YCwgZXhwZWN0ZWQgYHRhYmAsIGBvbmVgLCBvciBgbWl4ZWRgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBzdHlsZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nfunction checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGdCQUFnQjtBQUM1Qjs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcY2hlY2stcXVvdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydxdW90ZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUXVvdGUoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5xdW90ZSB8fCAnXCInXG5cbiAgaWYgKG1hcmtlciAhPT0gJ1wiJyAmJiBtYXJrZXIgIT09IFwiJ1wiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgdGl0bGUgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5xdW90ZWAsIGV4cGVjdGVkIGBcImAsIG9yIGBcXCdgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nfunction checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLXJ1bGUtcmVwZXRpdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3J1bGVSZXBldGl0aW9uJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tSdWxlUmVwZXRpdGlvbihzdGF0ZSkge1xuICBjb25zdCByZXBldGl0aW9uID0gc3RhdGUub3B0aW9ucy5ydWxlUmVwZXRpdGlvbiB8fCAzXG5cbiAgaWYgKHJlcGV0aXRpb24gPCAzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgcnVsZXMgd2l0aCByZXBldGl0aW9uIGAnICtcbiAgICAgICAgcmVwZXRpdGlvbiArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5ydWxlUmVwZXRpdGlvbmAsIGV4cGVjdGVkIGAzYCBvciBtb3JlJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiByZXBldGl0aW9uXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nfunction checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZ0JBQWdCO0FBQzVCOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxjaGVjay1ydWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncnVsZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUnVsZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnJ1bGUgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJy0nICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgcnVsZXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5ydWxlYCwgZXhwZWN0ZWQgYCpgLCBgLWAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nfunction checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ087QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLXN0cm9uZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3N0cm9uZyddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrU3Ryb25nKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuc3Ryb25nIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHN0cm9uZyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnN0cm9uZ2AsIGV4cGVjdGVkIGAqYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compilePattern: () => (/* binding */ compilePattern)\n/* harmony export */ });\n/**\n * @import {CompilePattern} from '../types.js'\n */\n\n/**\n * @type {CompilePattern}\n */\nfunction compilePattern(pattern) {\n  if (!pattern._compiled) {\n    const before =\n      (pattern.atBreak ? '[\\\\r\\\\n][\\\\t ]*' : '') +\n      (pattern.before ? '(?:' + pattern.before + ')' : '')\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (pattern.after ? '(?:' + pattern.after + ')' : ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jb21waWxlLXBhdHRlcm4uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxnQkFBZ0I7QUFDNUI7O0FBRUE7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNvbXBpbGUtcGF0dGVybi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0NvbXBpbGVQYXR0ZXJufSBmcm9tICcuLi90eXBlcy5qcydcbiAqL1xuXG4vKipcbiAqIEB0eXBlIHtDb21waWxlUGF0dGVybn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbXBpbGVQYXR0ZXJuKHBhdHRlcm4pIHtcbiAgaWYgKCFwYXR0ZXJuLl9jb21waWxlZCkge1xuICAgIGNvbnN0IGJlZm9yZSA9XG4gICAgICAocGF0dGVybi5hdEJyZWFrID8gJ1tcXFxcclxcXFxuXVtcXFxcdCBdKicgOiAnJykgK1xuICAgICAgKHBhdHRlcm4uYmVmb3JlID8gJyg/OicgKyBwYXR0ZXJuLmJlZm9yZSArICcpJyA6ICcnKVxuXG4gICAgcGF0dGVybi5fY29tcGlsZWQgPSBuZXcgUmVnRXhwKFxuICAgICAgKGJlZm9yZSA/ICcoJyArIGJlZm9yZSArICcpJyA6ICcnKSArXG4gICAgICAgICgvW3xcXFxce30oKVtcXF1eJCsqPy4tXS8udGVzdChwYXR0ZXJuLmNoYXJhY3RlcikgPyAnXFxcXCcgOiAnJykgK1xuICAgICAgICBwYXR0ZXJuLmNoYXJhY3RlciArXG4gICAgICAgIChwYXR0ZXJuLmFmdGVyID8gJyg/OicgKyBwYXR0ZXJuLmFmdGVyICsgJyknIDogJycpLFxuICAgICAgJ2cnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHBhdHRlcm4uX2NvbXBpbGVkXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/compile-pattern.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-flow.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerFlow: () => (/* binding */ containerFlow)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {FlowChildren, FlowParents, TrackFields} from '../types.js'\n */\n\n/**\n * @param {FlowParents} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlow(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  const tracker = state.createTracker(info)\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n\n  indexStack.push(-1)\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    indexStack[indexStack.length - 1] = index\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          before: '\\n',\n          after: '\\n',\n          ...tracker.current()\n        })\n      )\n    )\n\n    if (child.type !== 'list') {\n      state.bulletLastUsed = undefined\n    }\n\n    if (index < children.length - 1) {\n      results.push(\n        tracker.move(between(child, children[index + 1], parent, state))\n      )\n    }\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n\n/**\n * @param {FlowChildren} left\n * @param {FlowChildren} right\n * @param {FlowParents} parent\n * @param {State} state\n * @returns {string}\n */\nfunction between(left, right, parent, state) {\n  let index = state.join.length\n\n  while (index--) {\n    const result = state.join[index](left, right, parent, state)\n\n    if (result === true || result === 1) {\n      break\n    }\n\n    if (typeof result === 'number') {\n      return '\\n'.repeat(1 + result)\n    }\n\n    if (result === false) {\n      return '\\n\\n<!---->\\n\\n'\n    }\n  }\n\n  return '\\n\\n'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerPhrasing: () => (/* binding */ containerPhrasing)\n/* harmony export */ });\n/* harmony import */ var _encode_character_reference_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/**\n * @import {Handle, Info, State} from 'mdast-util-to-markdown'\n * @import {PhrasingParents} from '../types.js'\n */\n\n\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @param {PhrasingParents} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasing(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n  let before = info.before\n  /** @type {string | undefined} */\n  let encodeAfter\n\n  indexStack.push(-1)\n  let tracker = state.createTracker(info)\n\n  while (++index < children.length) {\n    const child = children[index]\n    /** @type {string} */\n    let after\n\n    indexStack[indexStack.length - 1] = index\n\n    if (index + 1 < children.length) {\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      let handle = state.handle.handlers[children[index + 1].type]\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, state, {\n            before: '',\n            after: '',\n            ...tracker.current()\n          }).charAt(0)\n        : ''\n    } else {\n      after = info.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n\n      // To do: does this work to reset tracker?\n      tracker = state.createTracker(info)\n      tracker.move(results.join(''))\n    }\n\n    let value = state.handle(child, parent, state, {\n      ...tracker.current(),\n      after,\n      before\n    })\n\n    // If we had to encode the first character after the previous node and it’s\n    // still the same character,\n    // encode it.\n    if (encodeAfter && encodeAfter === value.slice(0, 1)) {\n      value =\n        (0,_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_0__.encodeCharacterReference)(encodeAfter.charCodeAt(0)) + value.slice(1)\n    }\n\n    const encodingInfo = state.attentionEncodeSurroundingInfo\n    state.attentionEncodeSurroundingInfo = undefined\n    encodeAfter = undefined\n\n    // If we have to encode the first character before the current node and\n    // it’s still the same character,\n    // encode it.\n    if (encodingInfo) {\n      if (\n        results.length > 0 &&\n        encodingInfo.before &&\n        before === results[results.length - 1].slice(-1)\n      ) {\n        results[results.length - 1] =\n          results[results.length - 1].slice(0, -1) +\n          (0,_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_0__.encodeCharacterReference)(before.charCodeAt(0))\n      }\n\n      if (encodingInfo.after) encodeAfter = after\n    }\n\n    tracker.move(value)\n    results.push(value)\n    before = value.slice(-1)\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)\n/* harmony export */ });\n/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */\nfunction encodeCharacterReference(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLHFEQUFxRDtBQUNyRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxlbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEVuY29kZSBhIGNvZGUgcG9pbnQgYXMgYSBjaGFyYWN0ZXIgcmVmZXJlbmNlLlxuICpcbiAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlXG4gKiAgIENvZGUgcG9pbnQgdG8gZW5jb2RlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRW5jb2RlZCBjaGFyYWN0ZXIgcmVmZXJlbmNlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZW5jb2RlQ2hhcmFjdGVyUmVmZXJlbmNlKGNvZGUpIHtcbiAgcmV0dXJuICcmI3gnICsgY29kZS50b1N0cmluZygxNikudG9VcHBlckNhc2UoKSArICc7J1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)\n/* harmony export */ });\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ \"(rsc)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/**\n * @import {EncodeSides} from '../types.js'\n */\n\n\n\n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */\n// Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nfunction encodeInfo(outside, inside, marker) {\n  const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside)\n  const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside)\n\n  // Letter outside:\n  if (outsideKind === undefined) {\n    return insideKind === undefined\n      ? // Letter inside:\n        // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === '_'\n        ? {inside: true, outside: true}\n        : {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (letter, whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: encode outer (letter)\n          {inside: false, outside: true}\n  }\n\n  // Whitespace outside:\n  if (outsideKind === 1) {\n    return insideKind === undefined\n      ? // Letter inside: already forms.\n        {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: already forms.\n          {inside: false, outside: false}\n  }\n\n  // Punctuation outside:\n  return insideKind === undefined\n    ? // Letter inside: already forms.\n      {inside: false, outside: false}\n    : insideKind === 1\n      ? // Whitespace inside: encode inner (whitespace).\n        {inside: true, outside: false}\n      : // Punctuation inside: already forms.\n        {inside: false, outside: false}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatCodeAsIndented(node, state) {\n  return Boolean(\n    state.options.fences === false &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLE9BQU87QUFDbkIsWUFBWSxNQUFNO0FBQ2xCOztBQUVBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcZm9ybWF0LWNvZGUtYXMtaW5kZW50ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge0NvZGV9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtDb2RlfSBub2RlXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRDb2RlQXNJbmRlbnRlZChub2RlLCBzdGF0ZSkge1xuICByZXR1cm4gQm9vbGVhbihcbiAgICBzdGF0ZS5vcHRpb25zLmZlbmNlcyA9PT0gZmFsc2UgJiZcbiAgICAgIG5vZGUudmFsdWUgJiZcbiAgICAgIC8vIElmIHRoZXJl4oCZcyBubyBpbmZv4oCmXG4gICAgICAhbm9kZS5sYW5nICYmXG4gICAgICAvLyBBbmQgdGhlcmXigJlzIGEgbm9uLXdoaXRlc3BhY2UgY2hhcmFjdGVy4oCmXG4gICAgICAvW14gXFxyXFxuXS8udGVzdChub2RlLnZhbHVlKSAmJlxuICAgICAgLy8gQW5kIHRoZSB2YWx1ZSBkb2VzbuKAmXQgc3RhcnQgb3IgZW5kIGluIGEgYmxhbmvigKZcbiAgICAgICEvXltcXHQgXSooPzpbXFxyXFxuXXwkKXwoPzpefFtcXHJcXG5dKVtcXHQgXSokLy50ZXN0KG5vZGUudmFsdWUpXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(rsc)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */\n\n\n\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function (node) {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtaGVhZGluZy1hcy1zZXRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQjs7QUFFNEM7QUFDQzs7QUFFN0M7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLEVBQUUsd0RBQUs7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxrREFBSTtBQUNqQjtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLE1BQU0sOERBQVE7QUFDZDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcZm9ybWF0LWhlYWRpbmctYXMtc2V0ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtIZWFkaW5nfSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge0VYSVQsIHZpc2l0fSBmcm9tICd1bmlzdC11dGlsLXZpc2l0J1xuaW1wb3J0IHt0b1N0cmluZ30gZnJvbSAnbWRhc3QtdXRpbC10by1zdHJpbmcnXG5cbi8qKlxuICogQHBhcmFtIHtIZWFkaW5nfSBub2RlXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRIZWFkaW5nQXNTZXRleHQobm9kZSwgc3RhdGUpIHtcbiAgbGV0IGxpdGVyYWxXaXRoQnJlYWsgPSBmYWxzZVxuXG4gIC8vIExvb2sgZm9yIGxpdGVyYWxzIHdpdGggYSBsaW5lIGJyZWFrLlxuICAvLyBOb3RlIHRoYXQgdGhpcyBhbHNvXG4gIHZpc2l0KG5vZGUsIGZ1bmN0aW9uIChub2RlKSB7XG4gICAgaWYgKFxuICAgICAgKCd2YWx1ZScgaW4gbm9kZSAmJiAvXFxyP1xcbnxcXHIvLnRlc3Qobm9kZS52YWx1ZSkpIHx8XG4gICAgICBub2RlLnR5cGUgPT09ICdicmVhaydcbiAgICApIHtcbiAgICAgIGxpdGVyYWxXaXRoQnJlYWsgPSB0cnVlXG4gICAgICByZXR1cm4gRVhJVFxuICAgIH1cbiAgfSlcblxuICByZXR1cm4gQm9vbGVhbihcbiAgICAoIW5vZGUuZGVwdGggfHwgbm9kZS5kZXB0aCA8IDMpICYmXG4gICAgICB0b1N0cmluZyhub2RlKSAmJlxuICAgICAgKHN0YXRlLm9wdGlvbnMuc2V0ZXh0IHx8IGxpdGVyYWxXaXRoQnJlYWspXG4gIClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(rsc)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */\n\n\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nfunction formatLinkAsAutolink(node, state) {\n  const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indentLines: () => (/* binding */ indentLines)\n/* harmony export */ });\n/**\n * @import {IndentLines} from '../types.js'\n */\n\nconst eol = /\\r?\\n|\\r/g\n\n/**\n * @type {IndentLines}\n */\nfunction indentLines(value, map) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  let line = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = eol.exec(value))) {\n    one(value.slice(start, match.index))\n    result.push(match[0])\n    start = match.index + match[0].length\n    line++\n  }\n\n  one(value.slice(start))\n\n  return result.join('')\n\n  /**\n   * @param {string} value\n   */\n  function one(value) {\n    result.push(map(value, line, !value))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9pbmRlbnQtbGluZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxhQUFhO0FBQ3pCOztBQUVBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUCxhQUFhLGVBQWU7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtdG8tbWFya2Rvd25cXGxpYlxcdXRpbFxcaW5kZW50LWxpbmVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5kZW50TGluZXN9IGZyb20gJy4uL3R5cGVzLmpzJ1xuICovXG5cbmNvbnN0IGVvbCA9IC9cXHI/XFxufFxcci9nXG5cbi8qKlxuICogQHR5cGUge0luZGVudExpbmVzfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5kZW50TGluZXModmFsdWUsIG1hcCkge1xuICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gIGNvbnN0IHJlc3VsdCA9IFtdXG4gIGxldCBzdGFydCA9IDBcbiAgbGV0IGxpbmUgPSAwXG4gIC8qKiBAdHlwZSB7UmVnRXhwRXhlY0FycmF5IHwgbnVsbH0gKi9cbiAgbGV0IG1hdGNoXG5cbiAgd2hpbGUgKChtYXRjaCA9IGVvbC5leGVjKHZhbHVlKSkpIHtcbiAgICBvbmUodmFsdWUuc2xpY2Uoc3RhcnQsIG1hdGNoLmluZGV4KSlcbiAgICByZXN1bHQucHVzaChtYXRjaFswXSlcbiAgICBzdGFydCA9IG1hdGNoLmluZGV4ICsgbWF0Y2hbMF0ubGVuZ3RoXG4gICAgbGluZSsrXG4gIH1cblxuICBvbmUodmFsdWUuc2xpY2Uoc3RhcnQpKVxuXG4gIHJldHVybiByZXN1bHQuam9pbignJylcblxuICAvKipcbiAgICogQHBhcmFtIHtzdHJpbmd9IHZhbHVlXG4gICAqL1xuICBmdW5jdGlvbiBvbmUodmFsdWUpIHtcbiAgICByZXN1bHQucHVzaChtYXAodmFsdWUsIGxpbmUsICF2YWx1ZSkpXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nfunction patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksdUJBQXVCO0FBQ25DOztBQUVBO0FBQ0EsV0FBVyxzQkFBc0I7QUFDakMsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsc0JBQXNCO0FBQ2pDLFdBQVcsdUJBQXVCO0FBQ2xDLFdBQVcsU0FBUztBQUNwQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXHBhdHRlcm4taW4tc2NvcGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtDb25zdHJ1Y3ROYW1lLCBVbnNhZmV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge0FycmF5PENvbnN0cnVjdE5hbWU+fSBzdGFja1xuICogQHBhcmFtIHtVbnNhZmV9IHBhdHRlcm5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5leHBvcnQgZnVuY3Rpb24gcGF0dGVybkluU2NvcGUoc3RhY2ssIHBhdHRlcm4pIHtcbiAgcmV0dXJuIChcbiAgICBsaXN0SW5TY29wZShzdGFjaywgcGF0dGVybi5pbkNvbnN0cnVjdCwgdHJ1ZSkgJiZcbiAgICAhbGlzdEluU2NvcGUoc3RhY2ssIHBhdHRlcm4ubm90SW5Db25zdHJ1Y3QsIGZhbHNlKVxuICApXG59XG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxDb25zdHJ1Y3ROYW1lPn0gc3RhY2tcbiAqIEBwYXJhbSB7VW5zYWZlWydpbkNvbnN0cnVjdCddfSBsaXN0XG4gKiBAcGFyYW0ge2Jvb2xlYW59IG5vbmVcbiAqIEByZXR1cm5zIHtib29sZWFufVxuICovXG5mdW5jdGlvbiBsaXN0SW5TY29wZShzdGFjaywgbGlzdCwgbm9uZSkge1xuICBpZiAodHlwZW9mIGxpc3QgPT09ICdzdHJpbmcnKSB7XG4gICAgbGlzdCA9IFtsaXN0XVxuICB9XG5cbiAgaWYgKCFsaXN0IHx8IGxpc3QubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIG5vbmVcbiAgfVxuXG4gIGxldCBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBsaXN0Lmxlbmd0aCkge1xuICAgIGlmIChzdGFjay5pbmNsdWRlcyhsaXN0W2luZGV4XSkpIHtcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZhbHNlXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/safe.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/safe.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safe: () => (/* binding */ safe)\n/* harmony export */ });\n/* harmony import */ var _encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./encode-character-reference.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pattern-in-scope.js */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {SafeConfig, State} from 'mdast-util-to-markdown'\n */\n\n\n\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {string | null | undefined} input\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safe(state, input, config) {\n  const value = (config.before || '') + (input || '') + (config.after || '')\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {Record<number, {before: boolean, after: boolean}>} */\n  const infos = {}\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n\n    if (!(0,_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, pattern)) {\n      continue\n    }\n\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    while ((match = expression.exec(value))) {\n      const before = 'before' in pattern || Boolean(pattern.atBreak)\n      const after = 'after' in pattern\n      const position = match.index + (before ? match[1].length : 0)\n\n      if (positions.includes(position)) {\n        if (infos[position].before && !before) {\n          infos[position].before = false\n        }\n\n        if (infos[position].after && !after) {\n          infos[position].after = false\n        }\n      } else {\n        positions.push(position)\n        infos[position] = {before, after}\n      }\n    }\n  }\n\n  positions.sort(numerical)\n\n  let start = config.before ? config.before.length : 0\n  const end = value.length - (config.after ? config.after.length : 0)\n  index = -1\n\n  while (++index < positions.length) {\n    const position = positions[index]\n\n    // Character before or after matched:\n    if (position < start || position >= end) {\n      continue\n    }\n\n    // If this character is supposed to be escaped because it has a condition on\n    // the next character, and the next character is definitly being escaped,\n    // then skip this escape.\n    if (\n      (position + 1 < end &&\n        positions[index + 1] === position + 1 &&\n        infos[position].after &&\n        !infos[position + 1].before &&\n        !infos[position + 1].after) ||\n      (positions[index - 1] === position - 1 &&\n        infos[position].before &&\n        !infos[position - 1].before &&\n        !infos[position - 1].after)\n    ) {\n      continue\n    }\n\n    if (start !== position) {\n      // If we have to use a character reference, an ampersand would be more\n      // correct, but as backslashes only care about punctuation, either will\n      // do the trick\n      result.push(escapeBackslashes(value.slice(start, position), '\\\\'))\n    }\n\n    start = position\n\n    if (\n      /[!-/:-@[-`{-~]/.test(value.charAt(position)) &&\n      (!config.encode || !config.encode.includes(value.charAt(position)))\n    ) {\n      // Character escape.\n      result.push('\\\\')\n    } else {\n      // Character reference.\n      result.push((0,_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(position)))\n      start++\n    }\n  }\n\n  result.push(escapeBackslashes(value.slice(start, end), config.after))\n\n  return result.join('')\n}\n\n/**\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction numerical(a, b) {\n  return a - b\n}\n\n/**\n * @param {string} value\n * @param {string} after\n * @returns {string}\n */\nfunction escapeBackslashes(value, after) {\n  const expression = /\\\\(?=[!-/:-@[-`{-~])/g\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const results = []\n  const whole = value + after\n  let index = -1\n  let start = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = expression.exec(whole))) {\n    positions.push(match.index)\n  }\n\n  while (++index < positions.length) {\n    if (start !== positions[index]) {\n      results.push(value.slice(start, positions[index]))\n    }\n\n    results.push('\\\\')\n    start = positions[index]\n  }\n\n  results.push(value.slice(start))\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/mdast-util-to-markdown/lib/util/track.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/track.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/**\n * @import {CreateTracker, TrackCurrent, TrackMove, TrackShift} from '../types.js'\n */\n\n/**\n * Track positional info in the output.\n *\n * @type {CreateTracker}\n */\nfunction track(config) {\n  // Defaults are used to prevent crashes when older utilities somehow activate\n  // this code.\n  /* c8 ignore next 5 */\n  const options = config || {}\n  const now = options.now || {}\n  let lineShift = options.lineShift || 0\n  let line = now.line || 1\n  let column = now.column || 1\n\n  return {move, current, shift}\n\n  /**\n   * Get the current tracked info.\n   *\n   * @type {TrackCurrent}\n   */\n  function current() {\n    return {now: {line, column}, lineShift}\n  }\n\n  /**\n   * Define an increased line shift (the typical indent for lines).\n   *\n   * @type {TrackShift}\n   */\n  function shift(value) {\n    lineShift += value\n  }\n\n  /**\n   * Move past some generated markdown.\n   *\n   * @type {TrackMove}\n   */\n  function move(input) {\n    // eslint-disable-next-line unicorn/prefer-default-parameters\n    const value = input || ''\n    const chunks = value.split(/\\r?\\n|\\r/g)\n    const tail = chunks[chunks.length - 1]\n    line += chunks.length - 1\n    column =\n      chunks.length === 1 ? column + tail.length : 1 + tail.length + lineShift\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mdast-util-to-markdown/lib/util/track.js\n");

/***/ })

};
;