{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "static-export", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vN71RwwSdiFQOQ0Dh9wbKiJ0iciCJdD1++BM2W2ecDc=", "__NEXT_PREVIEW_MODE_ID": "3473f19bded2a5244b7524e50005492c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3d0d170f8377e6dc35094ee2847735212925d37829fb71528c1c0242db511212", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5725b9099224e5690511052d14f6ab4a36cf4085afefd7a81cad74b1db54eeaa"}}}, "functions": {}, "sortedMiddleware": ["/"]}