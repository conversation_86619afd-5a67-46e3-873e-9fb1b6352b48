"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[886],{3522:(e,t,r)=>{r.d(t,{B:()=>function e(t,r=!1){for(let i of Object.keys(t))void 0===t[i]&&delete t[i],r&&"object"==typeof t[i]&&null!==t[i]?e(t[i],r):r&&Array.isArray(t[i])&&t[i].forEach(t=>e(t,r));return t}})},8886:(e,t,r)=>{r.d(t,{searchDocs:()=>o});var i=r(3522);async function o(e,t,r){let o=[],{index:n="default",client:l,params:u={}}=r;if("crawler"===n){let r=await l.search({...u,term:e,where:{category:t?{eq:t.slice(0,1).toUpperCase()+t.slice(1)}:void 0,...u.where},limit:10});if(!r)return o;if("crawler"===n){for(let e of r.hits){let t=e.document;o.push({id:e.id,type:"page",content:t.title,url:t.path},{id:"page"+e.id,type:"text",content:t.content,url:t.path})}return o}}let a={...u,term:e,where:(0,i.B)({tag:t,...u.where}),groupBy:{properties:["page_id"],maxResult:7,...u.groupBy}},c=await l.search(a);if(!c||!c.groups)return o;for(let e of c.groups){let t=!1;for(let r of e.result){let e=r.document;t||(o.push({id:e.page_id,type:"page",content:e.title,url:e.url}),t=!0),o.push({id:e.id,content:e.content,type:e.content===e.section?"heading":"text",url:e.section_id?`${e.url}#${e.section_id}`:e.url})}}return o}r(9189)}}]);