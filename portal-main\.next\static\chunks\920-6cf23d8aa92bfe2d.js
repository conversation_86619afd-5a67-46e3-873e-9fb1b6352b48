"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[920],{2293:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(2115),u=0;function o(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),u++,()=>{1===u&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),u--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},4378:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(2115),u=n(7650),o=n(3655),a=n(2712),i=n(5155),s=r.forwardRef((e,t)=>{var n,s;let{container:l,...c}=e,[d,f]=r.useState(!1);(0,a.N)(()=>f(!0),[]);let v=l||d&&(null==(s=globalThis)||null==(n=s.document)?void 0:n.body);return v?u.createPortal((0,i.jsx)(o.sG.div,{...c,ref:t}),v):null});s.displayName="Portal"},7543:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(3536).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),u=n(6101),o=n(3655),a=n(9033),i=n(5155),s="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:E,onUnmountAutoFocus:y,...h}=e,[b,w]=r.useState(null),g=(0,a.c)(E),L=(0,a.c)(y),A=r.useRef(null),C=(0,u.s)(t,e=>w(e)),k=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(k.paused||!b)return;let t=e.target;b.contains(t)?A.current=t:m(A.current,{select:!0})},t=function(e){if(k.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||m(A.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,k.paused]),r.useEffect(()=>{if(b){p.add(k);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(s,c);b.addEventListener(s,g),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(b))}return()=>{b.removeEventListener(s,g),setTimeout(()=>{let t=new CustomEvent(l,c);b.addEventListener(l,L),b.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),b.removeEventListener(l,L),p.remove(k)},0)}}},[b,g,L,k]);let D=r.useCallback(e=>{if(!n&&!d||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[u,o]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);u&&o?e.shiftKey||r!==o?e.shiftKey&&r===u&&(e.preventDefault(),n&&m(o,{select:!0})):(e.preventDefault(),n&&m(u,{select:!0})):r===t&&e.preventDefault()}},[n,d,k.paused]);return(0,i.jsx)(o.sG.div,{tabIndex:-1,...h,ref:C,onKeyDown:D})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var p=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=E(e,t)).unshift(t)},remove(t){var n;null==(n=(e=E(e,t))[0])||n.resume()}}}();function E(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},u=new WeakMap,o=new WeakMap,a={},i=0,s=function(e){return e&&(e.host||s(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=s(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],f=new Set,v=new Set(l),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};l.forEach(m);var p=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))p(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,i=(u.get(e)||0)+1,s=(c.get(e)||0)+1;u.set(e,i),c.set(e,s),d.push(e),1===i&&a&&o.set(e,!0),1===s&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return p(t),f.clear(),i++,function(){d.forEach(function(e){var t=u.get(e)-1,a=c.get(e)-1;u.set(e,t),c.set(e,a),t||(o.has(e)||e.removeAttribute(r),o.delete(e)),a||e.removeAttribute(n)}),--i||(u=new WeakMap,u=new WeakMap,o=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var u=Array.from(Array.isArray(e)?e:[e]),o=t||r(e);return o?(u.push.apply(u,Array.from(o.querySelectorAll("[aria-live]"))),l(u,o,n,"aria-hidden")):function(){return null}}},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,u=n(2115),o=n(5185),a=n(3655),i=n(6101),s=n(9033),l=n(5155),c="dismissableLayer.update",d=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=u.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:E,onPointerDownOutside:y,onFocusOutside:h,onInteractOutside:b,onDismiss:w,...g}=e,L=u.useContext(d),[A,C]=u.useState(null),k=null!=(f=null==A?void 0:A.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,D]=u.useState({}),P=(0,i.s)(t,e=>C(e)),N=Array.from(L.layers),[S]=[...L.layersWithOutsidePointerEventsDisabled].slice(-1),T=N.indexOf(S),x=A?N.indexOf(A):-1,O=L.layersWithOutsidePointerEventsDisabled.size>0,F=x>=T,W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),o=u.useRef(!1),a=u.useRef(()=>{});return u.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,u,{discrete:!0})},u={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...L.branches].some(e=>e.contains(t));F&&!n&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},k),R=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),o=u.useRef(!1);return u.useEffect(()=>{let e=e=>{e.target&&!o.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...L.branches].some(e=>e.contains(t))&&(null==h||h(e),null==b||b(e),e.defaultPrevented||null==w||w())},k);return!function(e,t=globalThis?.document){let n=(0,s.c)(e);u.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{x===L.layers.size-1&&(null==E||E(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},k),u.useEffect(()=>{if(A)return p&&(0===L.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),L.layersWithOutsidePointerEventsDisabled.add(A)),L.layers.add(A),v(),()=>{p&&1===L.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[A,k,p,L]),u.useEffect(()=>()=>{A&&(L.layers.delete(A),L.layersWithOutsidePointerEventsDisabled.delete(A),v())},[A,L]),u.useEffect(()=>{let e=()=>D({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,l.jsx)(a.sG.div,{...g,ref:P,style:{pointerEvents:O?F?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,R.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,W.onPointerDownCapture)})});function v(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:u}=r,o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),u?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",u.forwardRef((e,t)=>{let n=u.useContext(d),r=u.useRef(null),o=(0,i.s)(t,r);return u.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(a.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"}}]);