exports.id=453,exports.ids=[453],exports.modules={43:(e,t,r)=>{"use strict";r.d(t,{FX:()=>a,jH:()=>s});var n=r(43210),o=r(60687),i=n.createContext(void 0),a=e=>{let{dir:t,children:r}=e;return(0,o.jsx)(i.Provider,{value:t,children:r})};function s(e){let t=n.useContext(i);return e||t||"ltr"}},163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},349:(e,t,r)=>{"use strict";function n(e,t,r=!0){return e.endsWith("/")&&(e=e.slice(0,-1)),t.endsWith("/")&&(t=t.slice(0,-1)),e===t||r&&t.startsWith(`${e}/`)}r.d(t,{$:()=>n})},353:(e,t,r)=>{"use strict";r.d(t,{NavProvider:()=>c,StylesProvider:()=>l,h:()=>f,v:()=>s});var n=r(60687),o=r(43210),i=r(66218);let a=(0,i.q6)("StylesContext",{tocNav:"xl:hidden",toc:"max-xl:hidden"});function s(){return a.use()}function l({children:e,...t}){return(0,n.jsx)(a.Provider,{value:t,children:e})}let u=(0,i.q6)("NavContext",{isTransparent:!1});function c({transparentMode:e="none",children:t}){let[r,i]=(0,o.useState)("none"!==e);return(0,n.jsx)(u.Provider,{value:(0,o.useMemo)(()=>({isTransparent:r}),[r]),children:t})}function f(){return u.use()}},1359:(e,t,r)=>{"use strict";r.d(t,{Oh:()=>i});var n=r(43210),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:o,quality:i}=e,a=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},2015:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return f},parseParameter:function(){return l}});let n=r(46143),o=r(71437),i=r(53293),a=r(72887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let f of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>f.startsWith(e)),a=f.match(s);if(e&&a&&a[2]){let{key:t,optional:r,repeat:o}=u(a[2]);n[t]={pos:l++,repeat:o,optional:r},c.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:o}=u(a[2]);n[e]={pos:l++,repeat:t,optional:o},r&&a[1]&&c.push("/"+(0,i.escapeStringRegexp)(a[1]));let s=t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(s=s.substring(1)),c.push(s)}else c.push("/"+(0,i.escapeStringRegexp)(f));t&&a&&a[3]&&c.push((0,i.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:n}}function f(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:o=!1}=void 0===t?{}:t,{parameterizedRoute:i,groups:a}=c(e,r,n),s=i;return o||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:a}}function d(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:o,routeKeys:a,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:c,optional:f,repeat:d}=u(o),h=c.replace(/\W/g,"");s&&(h=""+s+h);let p=!1;(0===h.length||h.length>30)&&(p=!0),isNaN(parseInt(h.slice(0,1)))||(p=!0),p&&(h=n());let m=h in a;s?a[h]=""+s+c:a[h]=c;let g=r?(0,i.escapeStringRegexp)(r):"";return t=m&&l?"\\k<"+h+">":d?"(?<"+h+">.+?)":"(?<"+h+">[^/]+?)",f?"(?:/"+g+t+")?":"/"+g+t}function h(e,t,r,l,u){let c,f=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),h={},p=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(s);if(e&&a&&a[2])p.push(d({getSafeRouteKey:f,interceptionMarker:a[1],segment:a[2],routeKeys:h,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&p.push("/"+(0,i.escapeStringRegexp)(a[1]));let e=d({getSafeRouteKey:f,segment:a[2],routeKeys:h,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,i.escapeStringRegexp)(c));r&&a&&a[3]&&p.push((0,i.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:h}}function p(e,t){var r,n,o;let i=h(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(o=t.backreferenceDuplicateKeys)&&o),a=i.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...f(e,t),namedRegex:"^"+a+"$",routeKeys:i.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=h(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],o=r[0];if(Array.isArray(n)&&Array.isArray(o)){if(n[0]!==o[0]||n[2]!==o[2])return!0}else if(n!==o)return!0;if(t[4])return!r[4];if(r[4])return!0;let i=Object.values(t[1])[0],a=Object.values(r[1])[0];return!i||!a||e(i,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(19169);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},3231:(e,t,r)=>{"use strict";r.d(t,{NavProvider:()=>i,StylesProvider:()=>o});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call usePageStyles() from the server but usePageStyles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\layout.js","usePageStyles");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call StylesProvider() from the server but StylesProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\layout.js","StylesProvider"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call NavProvider() from the server but NavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\layout.js","NavProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useNav() from the server but useNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\contexts\\layout.js","useNav")},4355:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(2255);function o(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4363:(e,t,r)=>{"use strict";r.d(t,{C6:()=>o,Cl:()=>i,Tt:()=>a,fX:()=>s});var n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;function s(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(51550),o=r(59656);var i=o._("_maxConcurrency"),a=o._("_runningCount"),s=o._("_queue"),l=o._("_processNext");class u{enqueue(e){let t,r,o=new Promise((e,n)=>{t=e,r=n}),i=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:o,task:i}),n._(this,l)[l](),o}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,i)[i]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,i)[i]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5261:(e,t,r)=>{"use strict";r.d(t,{T:()=>n.T});var n=r(22548);r(22317)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return h},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return f}});let n=r(59008),o=r(59154),i=r(75076);function a(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function s(e,t,r){return a(e,t===o.PrefetchKind.FULL,r)}function l(e){let{url:t,nextUrl:r,tree:n,prefetchCache:i,kind:s,allowAliasing:l=!0}=e,u=function(e,t,r,n,i){for(let s of(void 0===t&&(t=o.PrefetchKind.TEMPORARY),[r,null])){let r=a(e,!0,s),l=a(e,!1,s),u=e.search?r:l,c=n.get(u);if(c&&i){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let f=n.get(l);if(i&&e.search&&t!==o.PrefetchKind.FULL&&f&&!f.key.includes("%"))return{...f,aliased:!0}}if(t!==o.PrefetchKind.FULL&&i){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,r,i,l);return u?(u.status=p(u),u.kind!==o.PrefetchKind.FULL&&s===o.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:i,kind:null!=s?s:o.PrefetchKind.TEMPORARY})}),s&&u.kind===o.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:n,url:t,nextUrl:r,prefetchCache:i,kind:s||o.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:i,data:a,kind:l}=e,u=a.couldBeIntercepted?s(i,l,t):s(i,l),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(a),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:o.PrefetchCacheEntryStatus.fresh,url:i};return n.set(u,c),c}function c(e){let{url:t,kind:r,tree:a,nextUrl:l,prefetchCache:u}=e,c=s(t,r),f=i.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:a,nextUrl:l,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:o}=e,i=n.get(o);if(!i)return;let a=s(t,i.kind,r);return n.set(a,{...i,key:a}),n.delete(o),a}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=r?r:c);t&&(t.kind=o.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:a,data:f,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:o.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,d),d}function f(e){for(let[t,r]of e)p(r)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),h=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:i}=e;return -1!==i?Date.now()<r+i?o.PrefetchCacheEntryStatus.fresh:o.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+d?n?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:t===o.PrefetchKind.AUTO&&Date.now()<r+h?o.PrefetchCacheEntryStatus.stale:t===o.PrefetchKind.FULL&&Date.now()<r+h?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return h}});let n=r(79551),o=r(11959),i=r(12437),a=r(2015),s=r(78034),l=r(15526),u=r(72887),c=r(74722),f=r(46143),d=r(47912);function h(e,t,r){let o=(0,n.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let n=e!==f.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(f.NEXT_QUERY_PARAM_PREFIX),i=e!==f.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(f.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete o.query[e]}e.url=(0,n.format)(o)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let o,{optional:i,repeat:a}=r.groups[n],s=`[${a?"...":""}${n}]`;i&&(s=`[${s}]`);let l=t[n];o=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,o)}return e}function m(e,t,r,n){let o={};for(let i of Object.keys(t.groups)){let a=e[i];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let s=r[i],l=t.groups[i].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(s))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${i}]]`))&&(a=void 0,delete e[i]),a&&"string"==typeof a&&t.groups[i].repeat&&(a=a.split("/")),a&&(o[i]=a)}return{params:o,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:f,caseSensitive:g}){let y,b,v;return c&&(y=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(b=(0,s.getRouteMatcher)(y))(e)),{handleRewrites:function(a,s){let d={},h=s.pathname,p=n=>{let u=(0,i.getPathMatch)(n.source+(f?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let p=u(s.pathname);if((n.has||n.missing)&&p){let e=(0,l.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:i,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:s.query});if(i.protocol)return!0;if(Object.assign(d,a,p),Object.assign(s.query,i.query),delete i.query,Object.assign(s,i),!(h=s.pathname))return!1;if(r&&(h=h.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,o.normalizeLocalePath)(h,t.locales);h=e.pathname,s.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(h===e)return!0;if(c&&b){let e=b(h);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(h!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(h||"");return t===(0,u.removeTrailingSlash)(e)||(null==b?void 0:b(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return d},defaultRouteRegex:y,dynamicRouteMatcher:b,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:r}=y,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,d.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let o={};for(let e of Object.keys(r)){let i=r[e];if(!i)continue;let a=t[i],s=n[e];if(!a.optional&&!s)return null;o[a.pos]=s}return o}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>y&&v?m(e,y,v,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>h(e,t,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}function y(e,t){return"string"==typeof e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[f.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let n=r(96127);function o(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7448:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return i}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function o(e){return["async","defer","noModule"].includes(e)}function i(e,t){for(let[i,a]of Object.entries(t)){if(!t.hasOwnProperty(i)||n.includes(i)||void 0===a)continue;let s=r[i]||i.toLowerCase();"SCRIPT"===e.tagName&&o(s)?e[s]=!!a:e.setAttribute(s,String(a)),(!1===a||"SCRIPT"===e.tagName&&o(s)&&(!a||"false"===a))&&(e.setAttribute(s,""),e.removeAttribute(s))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return f},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let n=r(12958),o=r(74722),i=r(70554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,r){let o=(r?"":"?")+"$",i=`\\d?${r?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${o}`),RegExp(`[\\\\/]${a.icon.filename}${i}${l(a.icon.extensions,t)}${o}`),RegExp(`[\\\\/]${a.apple.filename}${i}${l(a.apple.extensions,t)}${o}`),RegExp(`[\\\\/]${a.openGraph.filename}${i}${l(a.openGraph.extensions,t)}${o}`),RegExp(`[\\\\/]${a.twitter.filename}${i}${l(a.twitter.extensions,t)}${o}`)],u=(0,n.normalizePathSep)(e);return s.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function f(e){return!(0,i.isAppRouteRoute)(e)&&u(e,[],!1)}function d(e){let t=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,i.isAppRouteRoute)(e)&&u(t,[],!1)}},8495:(e,t,r)=>{"use strict";r.d(t,{RootProvider:()=>_});var n=r(60687),o=r(10218),i=r(43210),a=r(43),s=r(40463),l=r(79943),u=r(24249),c=r(72527),f=r(66218);let d=(0,i.lazy)(()=>r.e(236).then(r.bind(r,62236)));function h({children:e,dir:t="ltr",theme:r={},search:i,i18n:u}){let c=e;return i?.enabled!==!1&&(c=(0,n.jsx)(l.YL,{SearchDialog:d,...i,children:c})),r?.enabled!==!1&&(c=(0,n.jsx)(o.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,...r,children:c})),u&&(c=(0,n.jsx)(p,{...u,children:c})),(0,n.jsx)(a.FX,{dir:t,children:(0,n.jsx)(s.G,{children:c})})}function p({locales:e=[],locale:t,onLocaleChange:r,...o}){let a=(0,f.rd)(),s=(0,f.a8)(),l=(0,u.J)(e=>{if(r)return r(e);let n=s.split("/").filter(e=>e.length>0);n[0]!==t?n.unshift(e):n[0]=e,a.push(`/${n.join("/")}`),a.refresh()});return(0,n.jsx)(c.gJ.Provider,{value:(0,i.useMemo)(()=>({locale:t,locales:e,text:{...c.Cr,...o.translations},onChange:l}),[t,e,l,o.translations]),children:o.children})}var m=r(29956);r(22317);var g=r(16189),y=r(85814),b=r(31261),v=r.n(b);function E({children:e}){return(0,n.jsx)(m.Uy,{usePathname:g.usePathname,useRouter:g.useRouter,useParams:g.useParams,Link:y,Image:v(),children:e})}function _(e){return(0,n.jsx)(E,{children:(0,n.jsx)(h,{...e,children:e.children})})}r(79667),r(353)},8610:(e,t,r)=>{"use strict";r.d(t,{Dk:()=>eA,kc:()=>eM,Ym:()=>eI,c3:()=>eN});var n,o,i,a,s,l,u,c=r(43210),f=r(4363);function d(e,t){var r=t&&t.cache?t.cache:y,n=t&&t.serializer?t.serializer:m;return(t&&t.strategy?t.strategy:function(e,t){var r,n,o=1===e.length?h:p;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function h(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function p(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}var m=function(){return JSON.stringify(arguments)},g=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),y={create:function(){return new g}},b={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,p.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,h.bind(this,e,r,n)}};function v(e){return e.type===o.literal}function E(e){return e.type===o.number}function _(e){return e.type===o.date}function w(e){return e.type===o.time}function T(e){return e.type===o.select}function R(e){return e.type===o.plural}function S(e){return e.type===o.tag}function P(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function A(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var O=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,x=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,C=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,N=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,I=/^(@+)?(\+|#+)?[rs]?$/g,M=/(\*)(0+)|(#+)(0+)|(0+)/g,k=/^(0+)$/;function L(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(I,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function D(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function j(e){var t=D(e);return t||{}}var H={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},B=new RegExp("^".concat(O.source,"*")),U=new RegExp("".concat(O.source,"*$"));function F(e,t){return{start:e,end:t}}var G=!!String.prototype.startsWith&&"_a".startsWith("a",1),z=!!String.fromCodePoint,V=!!Object.fromEntries,$=!!String.prototype.codePointAt,W=!!String.prototype.trimStart,K=!!String.prototype.trimEnd,X=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},q=!0;try{q=(null==(a=er("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){q=!1}var Y=G?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},Z=z?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},J=V?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},Q=$?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},ee=W?function(e){return e.trimStart()}:function(e){return e.replace(B,"")},et=K?function(e){return e.trimEnd()}:function(e){return e.replace(U,"")};function er(e,t){return new RegExp(e,t)}if(q){var en=er("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return en.lastIndex=t,null!=(r=en.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,o=Q(e,t);if(void 0===o||ea(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return Z.apply(void 0,r)};var eo=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),i.push({type:o.pound,location:F(l,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&ei(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,F(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:F(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,F(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,F(r,this.clonePosition()));if(this.isEOF()||!ei(this.char()))return this.error(n.INVALID_TAG,F(l,this.clonePosition()));var u=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,F(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:F(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,F(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var l=F(r,this.clonePosition());return{val:{type:o.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(ei(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return Z.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),Z(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,F(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,F(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,F(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,F(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:F(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,F(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,F(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:F(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,F(l,c));case"number":case"date":case"time":this.bumpSpace();var d=null;if(this.bumpIf(",")){this.bumpSpace();var h=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=et(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,F(this.clonePosition(),this.clonePosition()));d={style:m,styleLocation:F(h,this.clonePosition())}}var g=this.tryParseArgumentClose(a);if(g.err)return g;var y=F(a,this.clonePosition());if(d&&Y(null==d?void 0:d.style,"::",0)){var b=ee(d.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(b,d.styleLocation);if(p.err)return p;return{val:{type:o.number,value:r,location:y,style:p.val},err:null}}if(0===b.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,y);var v,E=b;this.locale&&(E=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(H[t||""]||H[n||""]||H["".concat(n,"-001")]||H["001"])[0]}(t);for(("H"==l||"k"==l)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=l+r}else"J"===o?r+="H":r+=o}return r}(b,this.locale));var m={type:i.dateTime,pattern:E,location:d.styleLocation,parsedOptions:this.shouldParseSkeletons?(v={},E.replace(x,function(e){var t=e.length;switch(e[0]){case"G":v.era=4===t?"long":5===t?"narrow":"short";break;case"y":v.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":v.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":v.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":v.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");v.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");v.weekday=["short","long","narrow","short"][t-4];break;case"a":v.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":v.hourCycle="h12",v.hour=["numeric","2-digit"][t-1];break;case"H":v.hourCycle="h23",v.hour=["numeric","2-digit"][t-1];break;case"K":v.hourCycle="h11",v.hour=["numeric","2-digit"][t-1];break;case"k":v.hourCycle="h24",v.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":v.minute=["numeric","2-digit"][t-1];break;case"s":v.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":v.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),v):{}};return{val:{type:"date"===u?o.date:o.time,value:r,location:y,style:m},err:null}}return{val:{type:"number"===u?o.number:"date"===u?o.date:o.time,value:r,location:y,style:null!=(s=null==d?void 0:d.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,F(_,(0,f.Cl)({},_)));this.bumpSpace();var w=this.parseIdentifierIfPossible(),T=0;if("select"!==u&&"offset"===w.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,F(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),w=this.parseIdentifierIfPossible(),T=p.val}var R=this.tryParsePluralOrSelectOptions(e,u,t,w);if(R.err)return R;var g=this.tryParseArgumentClose(a);if(g.err)return g;var S=F(a,this.clonePosition());if("select"===u)return{val:{type:o.select,value:r,options:J(R.val),location:S},err:null};return{val:{type:o.plural,value:r,options:J(R.val),offset:T,pluralType:"plural"===u?"cardinal":"ordinal",location:S},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,F(l,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,F(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,F(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(C).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=(0,f.Cl)((0,f.Cl)((0,f.Cl)({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return(0,f.Cl)((0,f.Cl)({},e),j(t))},{}));continue;case"engineering":t=(0,f.Cl)((0,f.Cl)((0,f.Cl)({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return(0,f.Cl)((0,f.Cl)({},e),j(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(M,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(k.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(N.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(N,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=(0,f.Cl)((0,f.Cl)({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=(0,f.Cl)((0,f.Cl)({},t),L(o)));continue}if(I.test(n.stem)){t=(0,f.Cl)((0,f.Cl)({},t),L(n.stem));continue}var i=D(n.stem);i&&(t=(0,f.Cl)((0,f.Cl)({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!k.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=(0,f.Cl)((0,f.Cl)({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],l=new Set,u=o.value,c=o.location;;){if(0===u.length){var f=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=F(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===u&&(a=!0),this.bumpSpace();var h=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,F(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(h);if(m.err)return m;s.push([u,{value:p.val,location:F(h,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,F(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,F(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=F(n,this.clonePosition());return o?X(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=Q(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Y(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&ea(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function ei(e){return e>=97&&e<=122||e>=65&&e<=90}function ea(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function es(e,t){void 0===t&&(t={});var r=new eo(e,t=(0,f.Cl)({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,T(t)||R(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else E(t)&&P(t.style)||(_(t)||w(t))&&A(t.style)?delete t.style.location:S(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var el=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return(0,f.C6)(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),eu=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,o)||this}return(0,f.C6)(t,e),t}(el),ec=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return(0,f.C6)(t,e),t}(el),ef=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return(0,f.C6)(t,e),t}(el);function ed(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var eh=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var a,s,c=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=c.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,i,a,s,c){if(1===t.length&&v(t[0]))return[{type:u.literal,value:t[0].value}];for(var f=[],d=0;d<t.length;d++){var h=t[d];if(v(h)){f.push({type:u.literal,value:h.value});continue}if(h.type===o.pound){"number"==typeof s&&f.push({type:u.literal,value:n.getNumberFormat(r).format(s)});continue}var p=h.value;if(!(a&&p in a))throw new ef(p,c);var m=a[p];if(h.type===o.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),f.push({type:"string"==typeof m?u.literal:u.object,value:m});continue}if(_(h)){var g="string"==typeof h.style?i.date[h.style]:A(h.style)?h.style.parsedOptions:void 0;f.push({type:u.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(w(h)){var g="string"==typeof h.style?i.time[h.style]:A(h.style)?h.style.parsedOptions:i.time.medium;f.push({type:u.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(E(h)){var g="string"==typeof h.style?i.number[h.style]:P(h.style)?h.style.parsedOptions:void 0;g&&g.scale&&(m*=g.scale||1),f.push({type:u.literal,value:n.getNumberFormat(r,g).format(m)});continue}if(S(h)){var y=h.children,b=h.value,O=a[b];if("function"!=typeof O)throw new ec(b,"function",c);var x=O(e(y,r,n,i,a,s).map(function(e){return e.value}));Array.isArray(x)||(x=[x]),f.push.apply(f,x.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(T(h)){var C=h.options[m]||h.options.other;if(!C)throw new eu(h.value,m,Object.keys(h.options),c);f.push.apply(f,e(C.value,r,n,i,a));continue}if(R(h)){var C=h.options["=".concat(m)];if(!C){if(!Intl.PluralRules)throw new el('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,c);var N=n.getPluralRules(r,{type:h.pluralType}).select(m-(h.offset||0));C=h.options[N]||h.options.other}if(!C)throw new eu(h.value,m,Object.keys(h.options),c);f.push.apply(f,e(C.value,r,n,i,a,m-(h.offset||0)));continue}}return f.length<2?f:f.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}(c.ast,c.locales,c.formatters,c.formats,e,void 0,c.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=c.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(c.locales)[0]}},this.getAst=function(){return c.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=i||{},p=(h.formatters,(0,f.Tt)(h,["formatters"]));this.ast=e.__parse(t,(0,f.Cl)((0,f.Cl)({},p),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(a=e.formats,n?Object.keys(a).reduce(function(e,t){var r,o;return e[t]=(r=a[t],(o=n[t])?(0,f.Cl)((0,f.Cl)((0,f.Cl)({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=(0,f.Cl)((0,f.Cl)({},r[t]),o[t]||{}),e},{})):r),e},(0,f.Cl)({},a)):a),this.formatters=i&&i.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:d(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,(0,f.fX)([void 0],t,!1)))},{cache:ed(s.number),strategy:b.variadic}),getDateTimeFormat:d(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,(0,f.fX)([void 0],t,!1)))},{cache:ed(s.dateTime),strategy:b.variadic}),getPluralRules:d(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,(0,f.fX)([void 0],t,!1)))},{cache:ed(s.pluralRules),strategy:b.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=es,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();class ep extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var em=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(em||{});function eg(...e){return e.filter(Boolean).join(".")}function ey(e){return eg(e.namespace,e.key)}function eb(e){console.error(e)}function ev(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eE(e,t){return d(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:b.variadic})}function e_(e,t){return eE((...t)=>new e(...t),t)}function ew(e){return{getDateTimeFormat:e_(Intl.DateTimeFormat,e.dateTime),getNumberFormat:e_(Intl.NumberFormat,e.number),getPluralRules:e_(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:e_(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:e_(Intl.ListFormat,e.list),getDisplayNames:e_(Intl.DisplayNames,e.displayNames)}}function eT(e,t,r,n){let o=eg(n,r);if(!t)throw Error(o);let i=t;return r.split(".").forEach(t=>{let r=i[t];if(null==t||null==r)throw Error(o+` (${e})`);i=r}),i}let eR={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:2628e3,months:2628e3,quarter:7884e3,quarters:7884e3,year:31536e3,years:31536e3};var eS=r(60687);let eP=(0,c.createContext)(void 0);function eA({children:e,formats:t,getMessageFallback:r,locale:n,messages:o,now:i,onError:a,timeZone:s}){let l=(0,c.useContext)(eP),u=(0,c.useMemo)(()=>l?.cache||ev(),[n,l?.cache]),f=(0,c.useMemo)(()=>l?.formatters||ew(u),[u,l?.formatters]),d=(0,c.useMemo)(()=>({...function({formats:e,getMessageFallback:t,messages:r,onError:n,...o}){return{...o,formats:e||void 0,messages:r||void 0,onError:n||eb,getMessageFallback:t||ey}}({locale:n,formats:void 0===t?l?.formats:t,getMessageFallback:r||l?.getMessageFallback,messages:void 0===o?l?.messages:o,now:i||l?.now,onError:a||l?.onError,timeZone:s||l?.timeZone}),formatters:f,cache:u}),[u,t,f,r,n,o,i,a,l,s]);return(0,eS.jsx)(eP.Provider,{value:d,children:e})}function eO(){let e=(0,c.useContext)(eP);if(!e)throw Error(void 0);return e}let ex=!1,eC="undefined"==typeof window;function eN(e){return function(e,t,r){let{cache:n,formats:o,formatters:i,getMessageFallback:a,locale:s,onError:l,timeZone:u}=eO(),f=e["!"],d="!"===t?void 0:t.slice((r+".").length);return u||ex||!eC||(ex=!0,l(new ep(em.ENVIRONMENT_FALLBACK,void 0))),(0,c.useMemo)(()=>(function(e){let t=function(e,t,r,n=eb){try{if(!t)throw Error(void 0);let n=r?eT(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new ep(em.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=ey,locale:o,messagesOrError:i,namespace:a,onError:s,timeZone:l}){let u=i instanceof ep;function f(e,t,r){let o=new ep(t,r);return s(o),n({error:o,key:e,namespace:a})}function d(s,d,h){var p;let m,g;if(u)return n({error:i,key:s,namespace:a});try{m=eT(o,i,s,a)}catch(e){return f(s,em.MISSING_MESSAGE,e.message)}if("object"==typeof m){let e;return f(s,Array.isArray(m)?em.INVALID_MESSAGE:em.INSUFFICIENT_PATH,e)}let y=(p=m,d?void 0:p);if(y)return y;r.getMessageFormat||(r.getMessageFormat=eE((...e)=>new eh(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{g=r.getMessageFormat(m,o,function(e,t,r){let n=eh.formats.date,o=eh.formats.time,i={...e?.dateTime,...t?.dateTime},a={date:{...n,...i},time:{...o,...i},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=a[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),a}(t,h,l),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:l,...t})}})}catch(e){return f(s,em.INVALID_MESSAGE,e.message)}try{let e=g.format(d?function(e){let t={};return Object.keys(e).forEach(r=>{let n,o=0,i=e[r];n="function"==typeof i?e=>{let t=i(e);return(0,c.isValidElement)(t)?(0,c.cloneElement)(t,{key:r+o++}):t}:i,t[r]=n}),t}(d):d);if(null==e)throw Error(void 0);return(0,c.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return f(s,em.FORMATTING_ERROR,e.message)}}function h(e,t,r){let n=d(e,t,r);return"string"!=typeof n?f(e,em.INVALID_MESSAGE,void 0):n}return h.rich=d,h.markup=(e,t,r)=>d(e,t,r),h.raw=e=>{if(u)return n({error:i,key:e,namespace:a});try{return eT(o,i,e,a)}catch(t){return f(e,em.MISSING_MESSAGE,t.message)}},h.has=e=>{if(u)return!1;try{return eT(o,i,e,a),!0}catch{return!1}},h}({...e,messagesOrError:t})})({cache:n,formatters:i,getMessageFallback:a,messages:f,namespace:d,onError:l,formats:o,locale:s,timeZone:u}),[n,i,a,f,d,l,o,s,u])}({"!":eO().messages},e?`!.${e}`:"!","!")}function eI(){return eO().locale}function eM(){let{formats:e,formatters:t,locale:r,now:n,onError:o,timeZone:i}=eO();return(0,c.useMemo)(()=>(function(e){let{_cache:t=ev(),_formatters:r=ew(t),formats:n,locale:o,onError:i=eb,timeZone:a}=e;function s(e){return e?.timeZone||(a?e={...e,timeZone:a}:i(new ep(em.ENVIRONMENT_FALLBACK,void 0))),e}function l(e,t,r,n,o){let a;try{a=function(e,t,r){let n;if("string"==typeof t){if(!(n=e?.[t])){let e=new ep(em.MISSING_FORMAT,void 0);throw i(e),e}}else n=t;return r&&(n={...n,...r}),n}(r,e,t)}catch{return o()}try{return n(a)}catch(e){return i(new ep(em.FORMATTING_ERROR,e.message)),o()}}function u(e,t,i){return l(t,i,n?.dateTime,t=>(t=s(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function c(){return e.now?e.now:(i(new ep(em.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:u,number:function(e,t,i){return l(t,i,n?.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):c(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=c());let l=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<2628e3?"week":t<31536e3?"month":"year"}(l)),s.numeric="second"===a?"auto":"always";let u=(n=a,Math.round(l/eR[n]));return r.getRelativeTimeFormat(o,s).format(u,a)}catch(t){return i(new ep(em.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t,i){let a=[],s=new Map,u=0;for(let t of e){let e;"object"==typeof t?(e=String(u),s.set(e,t)):e=String(t),a.push(e),u++}return l(t,i,n?.list,e=>{let t=r.getListFormat(o,e).formatToParts(a).map(e=>"literal"===e.type?e.value:s.get(e.value)||e.value);return s.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i,a){return l(i,a,n?.dateTime,n=>(n=s(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[u(e),u(t)].join(" – "))}}})({formats:e,locale:r,now:n,onError:o,timeZone:i,_formatters:t}),[e,t,n,r,o,i])}},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s,TL:()=>a});var n=r(43210),o=r(98599),i=r(60687);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var a;let e,s,l=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,l):l),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...a}=e,s=n.Children.toArray(o),l=s.find(u);if(l){let e=l.props.children,o=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(59154),r(25232),r(29651),r(28627),r(78866),r(75076),r(97936),r(35429);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9510:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(43210),o=r(11273),i=r(98599),a=r(8730),s=r(60687);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,s.jsx)(u,{scope:t,itemMap:i,collectionRef:o,children:r})};f.displayName=t;let d=e+"CollectionSlot",h=(0,a.TL)(d),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(d,r),a=(0,i.s)(t,o.collectionRef);return(0,s.jsx)(h,{ref:a,children:n})});p.displayName=d;let m=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,a.TL)(m),b=n.forwardRef((e,t)=>{let{scope:r,children:o,...a}=e,l=n.useRef(null),u=(0,i.s)(t,l),f=c(m,r);return n.useEffect(()=>(f.itemMap.set(l,{ref:l,...a}),()=>void f.itemMap.delete(l))),(0,s.jsx)(y,{...{[g]:""},ref:u,children:o})});return b.displayName=m,[{Provider:f,Slot:p,ItemSlot:b},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}var u=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=f(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function f(e){return e!=e||0===e?0:Math.trunc(e)}},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return f},handleAliasedPrefetchEntry:function(){return c}});let n=r(83913),o=r(89752),i=r(86770),a=r(57391),s=r(33123),l=r(33898),u=r(59435);function c(e,t,r,c,d){let h,p=t.tree,m=t.cache,g=(0,a.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=f(r,Object.fromEntries(c.searchParams));let{seedData:a,isRootRender:u,pathToSegment:d}=t,y=["",...d];r=f(r,Object.fromEntries(c.searchParams));let b=(0,i.applyRouterStatePatchToTree)(y,p,r,g),v=(0,o.createEmptyCacheNode)();if(u&&a){let t=a[1];v.loading=a[3],v.rsc=t,function e(t,r,o,i,a){if(0!==Object.keys(i[1]).length)for(let l in i[1]){let u,c=i[1][l],f=c[0],d=(0,s.createRouterCacheKey)(f),h=null!==a&&void 0!==a[2][l]?a[2][l]:null;if(null!==h){let e=h[1],r=h[3];u={lazyData:null,rsc:f.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=r.parallelRoutes.get(l);p?p.set(d,u):r.parallelRoutes.set(l,new Map([[d,u]])),e(t,u,o,c,h)}}(e,v,m,r,a)}else v.rsc=m.rsc,v.prefetchRsc=m.prefetchRsc,v.loading=m.loading,v.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,v,m,t);b&&(p=b,m=v,h=!0)}return!!h&&(d.patchedTree=p,d.cache=m,d.canonicalUrl=g,d.hashFragment=c.hash,(0,u.handleMutable)(t,d))}function f(e,t){let[r,o,...i]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),o,...i];let a={};for(let[e,r]of Object.entries(o))a[e]=f(r,t);return[r,a,...i]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10218:(e,t,r)=>{"use strict";r.d(t,{D:()=>u,N:()=>c});var n=r(43210),o=(e,t,r,n,o,i,a,s)=>{let l=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&i?o.map(e=>i[e]||e):o;r?(l.classList.remove(...n),l.classList.add(i&&i[t]?i[t]:t)):l.setAttribute(e,t)}),r=t,s&&u.includes(r)&&(l.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},i=["light","dark"],a="(prefers-color-scheme: dark)",s=n.createContext(void 0),l={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(s))?e:l},c=e=>n.useContext(s)?n.createElement(n.Fragment,null,e.children):n.createElement(d,{...e}),f=["light","dark"],d=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:o=!0,storageKey:l="theme",themes:u=f,defaultTheme:c=r?"system":"light",attribute:d="data-theme",value:y,children:b,nonce:v,scriptProps:E})=>{let[_,w]=n.useState(()=>p(l,c)),[T,R]=n.useState(()=>"system"===_?g():_),S=y?Object.values(y):u,P=n.useCallback(e=>{let n=e;if(!n)return;"system"===e&&r&&(n=g());let a=y?y[n]:n,s=t?m(v):null,l=document.documentElement,u=e=>{"class"===e?(l.classList.remove(...S),a&&l.classList.add(a)):e.startsWith("data-")&&(a?l.setAttribute(e,a):l.removeAttribute(e))};if(Array.isArray(d)?d.forEach(u):u(d),o){let e=i.includes(c)?c:null,t=i.includes(n)?n:e;l.style.colorScheme=t}null==s||s()},[v]),A=n.useCallback(e=>{let t="function"==typeof e?e(_):e;w(t);try{localStorage.setItem(l,t)}catch(e){}},[_]),O=n.useCallback(t=>{R(g(t)),"system"===_&&r&&!e&&P("system")},[_,e]);n.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(O),O(e),()=>e.removeListener(O)},[O]),n.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?w(e.newValue):A(c))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),n.useEffect(()=>{P(null!=e?e:_)},[e,_]);let x=n.useMemo(()=>({theme:_,setTheme:A,forcedTheme:e,resolvedTheme:"system"===_?T:_,themes:r?[...u,"system"]:u,systemTheme:r?T:void 0}),[_,A,e,T,r,u]);return n.createElement(s.Provider,{value:x},n.createElement(h,{forcedTheme:e,storageKey:l,attribute:d,enableSystem:r,enableColorScheme:o,defaultTheme:c,value:y,themes:u,nonce:v,scriptProps:E}),b)},h=n.memo(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:i,enableColorScheme:a,defaultTheme:s,value:l,themes:u,nonce:c,scriptProps:f})=>{let d=JSON.stringify([r,t,s,e,u,l,i,a]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:c,dangerouslySetInnerHTML:{__html:`(${o.toString()})(${d})`}})}),p=(e,t)=>{},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},11160:(e,t,r)=>{"use strict";r.d(t,{e:()=>b,J:()=>g});var n=r(85665);let o=[65,65,65,65,65,65,65,67,69,69,69,69,73,73,73,73,69,78,79,79,79,79,79,null,79,85,85,85,85,89,80,115,97,97,97,97,97,97,97,99,101,101,101,101,105,105,105,105,101,110,111,111,111,111,111,null,111,117,117,117,117,121,112,121,65,97,65,97,65,97,67,99,67,99,67,99,67,99,68,100,68,100,69,101,69,101,69,101,69,101,69,101,71,103,71,103,71,103,71,103,72,104,72,104,73,105,73,105,73,105,73,105,73,105,73,105,74,106,75,107,107,76,108,76,108,76,108,76,108,76,108,78,110,78,110,78,110,110,78,110,79,111,79,111,79,111,79,111,82,114,82,114,82,114,83,115,83,115,83,115,83,115,84,116,84,116,84,116,85,117,85,117,85,117,85,117,85,117,85,117,87,119,89,121,89,90,122,90,122,90,122,115];var i=r(77728);let a={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},s={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},l="[aeiouy]",u="[^aeiou][^aeiouy]*",c=l+"[aeiou]*",f="^("+u+")?"+c+u,d="^("+u+")?"+c+u+"("+c+")?$",h="^("+u+")?"+c+u+c+u,p="^("+u+")?"+l;function m(e){let t,r,n,o,i,c;if(e.length<3)return e;let m=e.substring(0,1);if("y"==m&&(e=m.toUpperCase()+e.substring(1)),o=/^(.+?)([^s])s$/,(n=/^(.+?)(ss|i)es$/).test(e)?e=e.replace(n,"$1$2"):o.test(e)&&(e=e.replace(o,"$1$2")),o=/^(.+?)(ed|ing)$/,(n=/^(.+?)eed$/).test(e)){let t=n.exec(e);(n=new RegExp(f)).test(t[1])&&(n=/.$/,e=e.replace(n,""))}else o.test(e)&&(t=o.exec(e)[1],(o=new RegExp(p)).test(t)&&(e=t,o=/(at|bl|iz)$/,i=RegExp("([^aeiouylsz])\\1$"),c=RegExp("^"+u+l+"[^aeiouwxy]$"),o.test(e)?e+="e":i.test(e)?(n=/.$/,e=e.replace(n,"")):c.test(e)&&(e+="e")));if((n=/^(.+?)y$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(p),t&&n.test(t)&&(e=t+"i")}if((n=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(f),t&&n.test(t)&&(e=t+a[r])}if((n=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(f),t&&n.test(t)&&(e=t+s[r])}if(o=/^(.+?)(s|t)(ion)$/,(n=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(h),t&&n.test(t)&&(e=t)}else if(o.test(e)){let r=o.exec(e);t=r?.[1]??""+r?.[2]??"",(o=new RegExp(h)).test(t)&&(e=t)}if((n=/^(.+?)e$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(h),o=new RegExp(d),i=RegExp("^"+u+l+"[^aeiouwxy]$"),t&&(n.test(t)||o.test(t)&&!i.test(t))&&(e=t)}return n=/ll$/,o=new RegExp(h),n.test(e)&&o.test(e)&&(n=/.$/,e=e.replace(n,"")),"y"==m&&(e=m.toLowerCase()+e.substring(1)),e}function g(e,t,r=!0){let n=`${this.language}:${e}:${t}`;return r&&this.normalizationCache.has(n)?this.normalizationCache.get(n):this.stopWords?.includes(t)?(r&&this.normalizationCache.set(n,""),""):(this.stemmer&&!this.stemmerSkipProperties.has(e)&&(t=this.stemmer(t)),t=function(e){let t=[];for(let n=0;n<e.length;n++){var r;t[n]=(r=e.charCodeAt(n))<192||r>383?r:o[r-192]||r}return String.fromCharCode(...t)}(t),r&&this.normalizationCache.set(n,t),t)}function y(e,t,r,o=!0){let a;if(t&&t!==this.language)throw(0,n.$)("LANGUAGE_NOT_SUPPORTED",t);if("string"!=typeof e)return[e];let s=this.normalizeToken.bind(this,r??"");if(r&&this.tokenizeSkipProperties.has(r))a=[s(e,o)];else{let t=i.DO[this.language];a=e.toLowerCase().split(t).map(e=>s(e,o)).filter(Boolean)}let l=function(e){for(;""===e[e.length-1];)e.pop();for(;""===e[0];)e.shift();return e}(a);return this.allowDuplicates?l:Array.from(new Set(l))}function b(e={}){let t,r;if(e.language){if(!i.Fc.includes(e.language))throw(0,n.$)("LANGUAGE_NOT_SUPPORTED",e.language)}else e.language="english";if(e.stemming||e.stemmer&&!("stemming"in e))if(e.stemmer){if("function"!=typeof e.stemmer)throw(0,n.$)("INVALID_STEMMER_FUNCTION_TYPE");t=e.stemmer}else if("english"===e.language)t=m;else throw(0,n.$)("MISSING_STEMMER",e.language);if(!1!==e.stopWords){if(r=[],Array.isArray(e.stopWords))r=e.stopWords;else if("function"==typeof e.stopWords)r=e.stopWords(r);else if(e.stopWords)throw(0,n.$)("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");if(!Array.isArray(r))throw(0,n.$)("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");for(let e of r)if("string"!=typeof e)throw(0,n.$)("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY")}let o={tokenize:y,language:e.language,stemmer:t,stemmerSkipProperties:new Set(e.stemmerSkipProperties?[e.stemmerSkipProperties].flat():[]),tokenizeSkipProperties:new Set(e.tokenizeSkipProperties?[e.tokenizeSkipProperties].flat():[]),stopWords:r,allowDuplicates:!!e.allowDuplicates,normalizeToken:g,normalizationCache:new Map};return o.tokenize=y.bind(o),o.normalizeToken=g,o}},11273:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,q:()=>i});var n=r(43210),o=r(60687);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,a=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let a=n.createContext(i),s=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,u=r?.[e]?.[s]||a,c=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(u.Provider,{value:c,children:i})};return l.displayName=t+"Provider",[l,function(r,o){let l=o?.[e]?.[s]||a,u=n.useContext(l);if(u)return u;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},11490:(e,t,r)=>{"use strict";r.d(t,{A:()=>$});var n,o,i=r(4363),a=r(43210),s="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function d(e){return e}var h=function(e){void 0===e&&(e={});var t,r,n,o,a=(t=null,void 0===r&&(r=d),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return a.options=(0,i.Cl)({async:!0,ssr:!1},e),a}(),p=function(){},m=a.forwardRef(function(e,t){var r,n,o,s,l=a.useRef(null),d=a.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),m=d[0],g=d[1],y=e.forwardProps,b=e.children,v=e.className,E=e.removeScrollBar,_=e.enabled,w=e.shards,T=e.sideCar,R=e.noIsolation,S=e.inert,P=e.allowPinchZoom,A=e.as,O=e.gapMode,x=(0,i.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(r=[l,t],n=function(e){return r.forEach(function(t){return u(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,s=o.facade,c(function(){var e=f.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||u(e,null)}),n.forEach(function(e){t.has(e)||u(e,o)})}f.set(s,r)},[r]),s),N=(0,i.Cl)((0,i.Cl)({},x),m);return a.createElement(a.Fragment,null,_&&a.createElement(T,{sideCar:h,removeScrollBar:E,shards:w,noIsolation:R,inert:S,setCallbacks:g,allowPinchZoom:!!P,lockRef:l,gapMode:O}),y?a.cloneElement(a.Children.only(b),(0,i.Cl)((0,i.Cl)({},N),{ref:C})):a.createElement(void 0===A?"div":A,(0,i.Cl)({},N,{className:v,ref:C}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:s};var g=function(e){var t=e.sideCar,r=(0,i.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return a.createElement(n,(0,i.Cl)({},r))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,r){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},v=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},E={left:0,top:0,right:0,gap:0},_=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[_(r),_(n),_(o)]},T=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return E;var t=w(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},R=v(),S="data-scroll-locked",P=function(e,t,r,n){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(u,"px ").concat(n,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(s," {\n    right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},O=function(){a.useEffect(function(){return document.body.setAttribute(S,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},x=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;O();var i=a.useMemo(function(){return T(o)},[o]);return a.createElement(R,{styles:P(i,!t,o,r?"":"!important")})},C=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return C=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){C=!1}var I=!!C&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},k=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),L(e,n)){var o=D(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},L=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},D=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},j=function(e,t,r,n,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),s=a*n,l=r.target,u=t.contains(l),c=!1,f=s>0,d=0,h=0;do{var p=D(e,l),m=p[0],g=p[1]-p[2]-a*m;(m||g)&&L(e,l)&&(d+=g,h+=m),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return f&&(o&&1>Math.abs(d)||!o&&s>d)?c=!0:!f&&(o&&1>Math.abs(h)||!o&&-s>h)&&(c=!0),c},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},F=0,G=[];let z=(n=function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(F++)[0],s=a.useState(v)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,i.fX)([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=H(e),a=r.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,f=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===c.type)return!1;var d=k(f,c);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=k(f,c)),!d)return!1;if(!n.current&&"changedTouches"in e&&(s||u)&&(n.current=o),!o)return!0;var h=n.current||o;return j(h,t,e,"h"===h?s:u,!0)},[]),c=a.useCallback(function(e){if(G.length&&G[G.length-1]===s){var r="deltaY"in e?B(e):H(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=a.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){r.current=H(e),n.current=void 0},[]),h=a.useCallback(function(t){f(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){f(t.type,H(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return G.push(s),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",c,I),document.addEventListener("touchmove",c,I),document.addEventListener("touchstart",d,I),function(){G=G.filter(function(e){return e!==s}),document.removeEventListener("wheel",c,I),document.removeEventListener("touchmove",c,I),document.removeEventListener("touchstart",d,I)}},[]);var m=e.removeScrollBar,g=e.inert;return a.createElement(a.Fragment,null,g?a.createElement(s,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(x,{gapMode:e.gapMode}):null)},h.useMedium(n),g);var V=a.forwardRef(function(e,t){return a.createElement(m,(0,i.Cl)({},e,{ref:t,sideCar:z}))});V.classNames=m.classNames;let $=V},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return o}});let n=r(35362);function o(e,t){let r=[],o=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),i=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(o.source),o.flags):o,r);return(e,n)=>{if("string"!=typeof e)return!1;let o=i(e);if(!o)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete o.params[e.name];return{...n,...o.params}}}},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},12958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},13495:(e,t,r)=>{"use strict";r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>s});var n=r(43210),o=r(51215),i=r(8730),a=r(60687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},14967:(e,t,r)=>{"use strict";r.d(t,{EL:()=>i,HM:()=>o});var n=r(84604);function o({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:o,namespace:i,onError:a=n.g,...s}){return function({messages:e,namespace:t,...r},o){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...s,onError:a,cache:e,formatters:t,getMessageFallback:r,messages:{"!":o},namespace:i?`!.${i}`:"!"},"!")}function i(e,t){return e.includes(t)}},15526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return f},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return h}});let n=r(35362),o=r(53293),i=r(76759),a=r(71437),s=r(9977),l=r(88212);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let o={},i=r=>{let n,i=r.key;switch(r.type){case"header":i=i.toLowerCase(),n=e.headers[i];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[i];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return o[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(i)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{o[e]=t.groups[e]}):"host"===r.type&&t[0]&&(o.host=t[0])),!0}return!1};return!(!r.every(e=>i(e))||n.some(e=>i(e)))&&o}function f(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,o.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,i.parseUrl)(t),n=r.pathname;n&&(n=u(n));let a=r.href;a&&(a=u(a));let s=r.hostname;s&&(s=u(s));let l=r.hash;return l&&(l=u(l)),{...r,pathname:n,hostname:s,href:a,hash:l}}function h(e){let t,r,o=Object.assign({},e.query);delete o[s.NEXT_RSC_UNION_QUERY];let i=d(e),{hostname:l,query:c}=i,h=i.pathname;i.hash&&(h=""+h+i.hash);let p=[],m=[];for(let e of((0,n.pathToRegexp)(h,m),m))p.push(e.name);if(l){let e=[];for(let t of((0,n.pathToRegexp)(l,e),e))p.push(t.name)}let g=(0,n.compile)(h,{validate:!1});for(let[r,o]of(l&&(t=(0,n.compile)(l,{validate:!1})),Object.entries(c)))Array.isArray(o)?c[r]=o.map(t=>f(u(t),e.params)):"string"==typeof o&&(c[r]=f(u(o),e.params));let y=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!y.some(e=>p.includes(e)))for(let t of y)t in c||(c[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,o]=(r=g(e.params)).split("#",2);t&&(i.hostname=t(e.params)),i.pathname=n,i.hash=(o?"#":"")+(o||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...o,...i.query},{newUrl:r,destQuery:c,parsedDestination:i}}},16189:(e,t,r)=>{"use strict";var n=r(65773);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},18468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let a=i.length<=2,[s,l]=i,u=(0,n.createRouterCacheKey)(l),c=r.parallelRoutes.get(s);if(!c)return;let f=t.parallelRoutes.get(s);if(f&&f!==c||(f=new Map(c),t.parallelRoutes.set(s,f)),a)return void f.delete(u);let d=c.get(u),h=f.get(u);h&&d&&(h===d&&(h={lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes)},f.set(u,h)),e(h,d,(0,o.getNextFlightSegmentPath)(i)))}}});let n=r(33123),o=r(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},22308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,o,,a]=t;for(let s in n.includes(i.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),o)e(o[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(56928),o=r(59008),i=r(83913);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:r,updatedTree:i,updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c=i,canonicalUrl:f}=e,[,d,h,p]=i,m=[];if(h&&h!==f&&"refresh"===p&&!u.has(h)){u.add(h);let e=(0,o.fetchServerResponse)(new URL(h,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,a,a,e)});m.push(e)}for(let e in d){let n=s({navigatedAt:t,state:r,updatedTree:d[e],updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:f});m.push(n)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22317:(e,t,r)=>{"use strict"},22548:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var n=r(43210);function o(e,t,r=function e(t,r){if(Array.isArray(t)&&Array.isArray(r))return r.length!==t.length||t.some((t,n)=>e(t,r[n]));if("object"==typeof t&&t&&"object"==typeof r&&r){let n=Object.keys(t),o=Object.keys(r);return n.length!==o.length||n.some(n=>e(t[n],r[n]))}return t!==r}){let[i,a]=(0,n.useState)(e);r(i,e)&&(t(e,i),a(e))}},22880:(e,t,r)=>{"use strict";r.d(t,{r:()=>n});let n=(0,r(24224).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 p-1 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5"}}})},23736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),r(44827);let n=r(42785);function o(e,t,r){void 0===r&&(r=!0);let o=new URL("http://n"),i=t?new URL(t,o):e.startsWith(".")?new URL("http://n"):o,{pathname:a,searchParams:s,search:l,hash:u,href:c,origin:f}=new URL(e,i);if(f!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:u,href:c.slice(f.length)}}},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(49384);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let i=o(t)||o(n);return a[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},24249:(e,t,r)=>{"use strict";r.d(t,{J:()=>o}),r(22317);var n=r(43210);function o(e){let t=(0,n.useRef)(e);return t.current=e,(0,n.useCallback)((...e)=>t.current(...e),[])}},24274:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\theme-toggle.js","ThemeToggle")},24642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},25028:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(43210),o=r(51215),i=r(14163),a=r(66156),s=r(60687),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,a.N)(()=>c(!0),[]);let f=r||u&&globalThis?.document?.body;return f?o.createPortal((0,s.jsx)(i.sG.div,{...l,ref:t}),f):null});l.displayName="Portal"},25232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return v},navigateReducer:function(){return function e(t,r){let{url:_,isExternalUrl:w,navigateType:T,shouldScroll:R,allowAliasing:S}=r,P={},{hash:A}=_,O=(0,o.createHrefFromUrl)(_),x="push"===T;if((0,g.prunePrefetchCache)(t.prefetchCache),P.preserveCustomHistoryState=!1,P.pendingPush=x,w)return v(t,P,_.toString(),x);if(document.getElementById("__next-page-redirect"))return v(t,P,O,x);let C=(0,g.getOrCreatePrefetchCacheEntry)({url:_,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:S}),{treeAtTimeOfPrefetch:N,data:I}=C;return d.prefetchQueue.bump(I),I.then(d=>{let{flightData:g,canonicalUrl:w,postponed:T}=d,S=Date.now(),I=!1;if(C.lastUsedTime||(C.lastUsedTime=S,I=!0),C.aliased){let n=(0,b.handleAliasedPrefetchEntry)(S,t,g,_,P);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof g)return v(t,P,g,x);let M=w?(0,o.createHrefFromUrl)(w):O;if(A&&t.canonicalUrl.split("#",1)[0]===M.split("#",1)[0])return P.onlyHashChange=!0,P.canonicalUrl=M,P.shouldScroll=R,P.hashFragment=A,P.scrollableSegments=[],(0,c.handleMutable)(t,P);let k=t.tree,L=t.cache,D=[];for(let e of g){let{pathToSegment:r,seedData:o,head:c,isHeadPartial:d,isRootRender:g}=e,b=e.tree,w=["",...r],R=(0,a.applyRouterStatePatchToTree)(w,k,b,O);if(null===R&&(R=(0,a.applyRouterStatePatchToTree)(w,N,b,O)),null!==R){if(o&&g&&T){let e=(0,m.startPPRNavigation)(S,L,k,b,o,c,d,!1,D);if(null!==e){if(null===e.route)return v(t,P,O,x);R=e.route;let r=e.node;null!==r&&(P.cache=r);let o=e.dynamicRequestTree;if(null!==o){let r=(0,n.fetchServerResponse)(_,{flightRouterState:o,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else R=b}else{if((0,l.isNavigatingToNewRootLayout)(k,R))return v(t,P,O,x);let n=(0,h.createEmptyCacheNode)(),o=!1;for(let t of(C.status!==u.PrefetchCacheEntryStatus.stale||I?o=(0,f.applyFlightData)(S,L,n,e,C):(o=function(e,t,r,n){let o=!1;for(let i of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),E(n).map(e=>[...r,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,i),o=!0;return o}(n,L,r,b),C.lastUsedTime=S),(0,s.shouldHardNavigate)(w,k)?(n.rsc=L.rsc,n.prefetchRsc=L.prefetchRsc,(0,i.invalidateCacheBelowFlightSegmentPath)(n,L,r),P.cache=n):o&&(P.cache=n,L=n),E(b))){let e=[...r,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&D.push(e)}}k=R}}return P.patchedTree=k,P.canonicalUrl=M,P.scrollableSegments=D,P.hashFragment=A,P.shouldScroll=R,(0,c.handleMutable)(t,P)},()=>t)}}});let n=r(59008),o=r(57391),i=r(18468),a=r(86770),s=r(65951),l=r(2030),u=r(59154),c=r(59435),f=r(56928),d=r(75076),h=r(89752),p=r(83913),m=r(65956),g=r(5334),y=r(97464),b=r(9707);function v(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function E(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,o]of Object.entries(n))for(let n of E(o))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(4355),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},i=t.split(n),a=(r||{}).decode||e,s=0;s<i.length;s++){var l=i[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),f=l.substr(++u,l.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),void 0==o[c]&&(o[c]=function(e,t){try{return t(e)}catch(t){return e}}(f,a))}}return o},t.serialize=function(e,t,n){var i=n||{},a=i.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!o.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=i.maxAge){var u=i.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");l+="; Domain="+i.domain}if(i.path){if(!o.test(i.path))throw TypeError("option path is invalid");l+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},26736:(e,t,r)=>{"use strict";r.d(t,{RootProvider:()=>o});var n=r(12907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call RootProvider() from the server but RootProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","RootProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","useI18n"),(0,n.registerClientReference)(function(){throw Error("Attempted to call I18nLabel() from the server but I18nLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","I18nLabel"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SearchProvider() from the server but SearchProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","SearchProvider"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SearchOnly() from the server but SearchOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","SearchOnly"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useSearchContext() from the server but useSearchContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","useSearchContext"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","SidebarProvider"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","useSidebar"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useTreePath() from the server but useTreePath is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","useTreePath"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useTreeContext() from the server but useTreeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","useTreeContext"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TreeContextProvider() from the server but TreeContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","TreeContextProvider"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useNav() from the server but useNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","useNav"),(0,n.registerClientReference)(function(){throw Error("Attempted to call NavProvider() from the server but NavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","NavProvider"),(0,n.registerClientReference)(function(){throw Error("Attempted to call StylesProvider() from the server but StylesProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","StylesProvider"),(0,n.registerClientReference)(function(){throw Error("Attempted to call usePageStyles() from the server but usePageStyles is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\provider\\index.js","usePageStyles")},28627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return i}});let n=r(57391),o=r(70642);function i(e,t){var r;let{url:i,tree:a}=t,s=(0,n.createHrefFromUrl)(i),l=a||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,o.extractPathFromFlightRouterState)(l))?r:i.pathname}}r(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(57391),o=r(86770),i=r(2030),a=r(25232),s=r(56928),l=r(59435),u=r(89752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:f}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof r)return(0,a.handleExternalUrl)(e,d,r,e.pushRef.pendingPush);let h=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:l}=t,m=(0,o.applyRouterStatePatchToTree)(["",...r],h,l,e.canonicalUrl);if(null===m)return e;if((0,i.isNavigatingToNewRootLayout)(h,m))return(0,a.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,n.createHrefFromUrl)(c):void 0;g&&(d.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(f,p,y,t),d.patchedTree=m,d.cache=y,p=y,h=m}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29956:(e,t,r)=>{"use strict";r.d(t,{N_:()=>f,Uy:()=>s,_V:()=>c,a8:()=>l,q6:()=>d,rd:()=>u});var n=r(43210),o=r(60687),i=()=>{throw Error("You need to wrap your application inside `FrameworkProvider`.")},a=d("FrameworkContext",{useParams:i,useRouter:i,usePathname:i});function s({children:e,...t}){let r=n.useMemo(()=>({usePathname:t.usePathname,useRouter:t.useRouter,Link:t.Link,Image:t.Image,useParams:t.useParams}),[t.Link,t.usePathname,t.useRouter,t.useParams,t.Image]);return(0,o.jsx)(a.Provider,{value:r,children:e})}function l(){return a.use().usePathname()}function u(){return a.use().useRouter()}function c(e){let{Image:t}=a.use();if(!t){let{src:t,alt:r,priority:n,...i}=e;return(0,o.jsx)("img",{alt:r,src:t,fetchPriority:n?"high":"auto",...i})}return(0,o.jsx)(t,{...e})}function f(e){let{Link:t}=a.use();if(!t){let{href:t,prefetch:r,...n}=e;return(0,o.jsx)("a",{href:t,...n})}return(0,o.jsx)(t,{...e})}function d(e,t){let r=n.createContext(t);return{Provider:e=>(0,o.jsx)(r.Provider,{value:e.value,children:e.children}),use:t=>{let o=n.useContext(r);if(!o)throw Error(t??`Provider of ${e} is required but missing.`);return o}}}},30195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(40740)._(r(76715)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+i+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return i(e)}},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return f}});let n=r(14985),o=r(40740),i=r(60687),a=o._(r(43210)),s=n._(r(47755)),l=r(14959),u=r(89513),c=r(34604);function f(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let h=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,a=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){a=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let m=function(e){let{children:t}=e,r=(0,a.useContext)(l.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,i.jsx)(s.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return s}});let n=r(14985),o=r(44953),i=r(46533),a=n._(r(1933));function s(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},31355:(e,t,r)=>{"use strict";r.d(t,{qW:()=>d});var n,o=r(43210),i=r(70569),a=r(14163),s=r(98599),l=r(13495),u=r(60687),c="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:y,onDismiss:b,...v}=e,E=o.useContext(f),[_,w]=o.useState(null),T=_?.ownerDocument??globalThis?.document,[,R]=o.useState({}),S=(0,s.s)(t,e=>w(e)),P=Array.from(E.layers),[A]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),O=P.indexOf(A),x=_?P.indexOf(_):-1,C=E.layersWithOutsidePointerEventsDisabled.size>0,N=x>=O,I=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){p("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=n,t.addEventListener("click",i.current,{once:!0})):n()}else t.removeEventListener("click",i.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));N&&!r&&(m?.(e),y?.(e),e.defaultPrevented||b?.())},T),M=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(g?.(e),y?.(e),e.defaultPrevented||b?.())},T);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{x===E.layers.size-1&&(d?.(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},T),o.useEffect(()=>{if(_)return r&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(_)),E.layers.add(_),h(),()=>{r&&1===E.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=n)}},[_,T,r,E]),o.useEffect(()=>()=>{_&&(E.layers.delete(_),E.layersWithOutsidePointerEventsDisabled.delete(_),h())},[_,E]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...v,ref:S,style:{pointerEvents:C?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,a.hO)(o,i):o.dispatchEvent(i)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(f),n=o.useRef(null),i=(0,s.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},31658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return h}});let n=r(8304),o=function(e){return e&&e.__esModule?e:{default:e}}(r(78671)),i=r(6341),a=r(2015),s=r(30660),l=r(74722),u=r(12958),c=r(35499);function f(e){let t=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,s.djb2Hash)(t).toString(36).slice(0,6)),r}function d(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,i.interpolateDynamicPath)(n,t,s),{name:d,ext:h}=o.default.parse(r),p=f(o.default.posix.join(e,d)),m=p?`-${p}`:"";return(0,u.normalizePathSep)(o.default.join(c,`${d}${m}${h}`))}function h(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=f(e),!t.endsWith("/route")){let{dir:e,name:n,ext:i}=o.default.parse(t);t=o.default.posix.join(e,`${n}${r?`-${r}`:""}${i}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,o=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${o}`)+(r?"/route":"")}},32547:(e,t,r)=>{"use strict";r.d(t,{n:()=>f});var n=r(43210),o=r(98599),i=r(14163),a=r(13495),s=r(60687),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},f=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:f=!1,onMountAutoFocus:g,onUnmountAutoFocus:y,...b}=e,[v,E]=n.useState(null),_=(0,a.c)(g),w=(0,a.c)(y),T=n.useRef(null),R=(0,o.s)(t,e=>E(e)),S=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(f){let e=function(e){if(S.paused||!v)return;let t=e.target;v.contains(t)?T.current=t:p(T.current,{select:!0})},t=function(e){if(S.paused||!v)return;let t=e.relatedTarget;null!==t&&(v.contains(t)||p(T.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(v)});return v&&r.observe(v,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[f,v,S.paused]),n.useEffect(()=>{if(v){m.add(S);let e=document.activeElement;if(!v.contains(e)){let t=new CustomEvent(l,c);v.addEventListener(l,_),v.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(p(n,{select:t}),document.activeElement!==r)return}(d(v).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(v))}return()=>{v.removeEventListener(l,_),setTimeout(()=>{let t=new CustomEvent(u,c);v.addEventListener(u,w),v.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),v.removeEventListener(u,w),m.remove(S)},0)}}},[v,_,w,S]);let P=n.useCallback(e=>{if(!r&&!f||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[h(t,e),h(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&p(i,{select:!0})):(e.preventDefault(),r&&p(o,{select:!0})):n===t&&e.preventDefault()}},[r,f,S.paused]);return(0,s.jsx)(i.sG.div,{tabIndex:-1,...b,ref:R,onKeyDown:P})});function d(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function h(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function p(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}f.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},32708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},33898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let n=r(34400),o=r(41500),i=r(33123),a=r(83913);function s(e,t,r,s,l,u){let{segmentPath:c,seedData:f,tree:d,head:h}=s,p=t,m=r;for(let t=0;t<c.length;t+=2){let r=c[t],s=c[t+1],g=t===c.length-2,y=(0,i.createRouterCacheKey)(s),b=m.parallelRoutes.get(r);if(!b)continue;let v=p.parallelRoutes.get(r);v&&v!==b||(v=new Map(b),p.parallelRoutes.set(r,v));let E=b.get(y),_=v.get(y);if(g){if(f&&(!_||!_.lazyData||_===E)){let t=f[0],r=f[1],i=f[3];_={lazyData:null,rsc:u||t!==a.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:i,parallelRoutes:u&&E?new Map(E.parallelRoutes):new Map,navigatedAt:e},E&&u&&(0,n.invalidateCacheByRouterState)(_,E,d),u&&(0,o.fillLazyItemsTillLeafWithHead)(e,_,E,d,f,h,l),v.set(y,_)}continue}_&&E&&(_===E&&(_={lazyData:_.lazyData,rsc:_.rsc,prefetchRsc:_.prefetchRsc,head:_.head,prefetchHead:_.prefetchHead,parallelRoutes:new Map(_.parallelRoutes),loading:_.loading},v.set(y,_)),p=_,m=E)}}function l(e,t,r,n,o){s(e,t,r,n,o,!0)}function u(e,t,r,n,o){s(e,t,r,n,o,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let n=r(33123);function o(e,t,r){for(let o in r[1]){let i=r[1][o][0],a=(0,n.createRouterCacheKey)(i),s=t.parallelRoutes.get(o);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},35362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var o="",i=r+1;i<e.length;){var a=e.charCodeAt(i);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){o+=e[i++];continue}break}if(!o)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:o}),r=i;continue}if("("===n){var s=1,l="",i=r+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){l+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--s){i++;break}}else if("("===e[i]&&(s++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);l+=e[i++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=i;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,a="[^"+o(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",f=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},d=function(e){var t=f(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},h=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var p=f("CHAR"),m=f("NAME"),g=f("PATTERN");if(m||g){var y=p||"";-1===i.indexOf(y)&&(c+=y,y=""),c&&(s.push(c),c=""),s.push({name:m||l++,prefix:y,suffix:"",pattern:g||a,modifier:f("MODIFIER")||""});continue}var b=p||f("ESCAPED_CHAR");if(b){c+=b;continue}if(c&&(s.push(c),c=""),f("OPEN")){var y=h(),v=f("NAME")||"",E=f("PATTERN")||"",_=h();d("CLOSE"),s.push({name:v||(E?l++:""),pattern:v&&!E?a:E,prefix:y,suffix:_,modifier:f("MODIFIER")||""});continue}d("END")}return s}function r(e,t){void 0===t&&(t={});var r=i(t),n=t.encode,o=void 0===n?function(e){return e}:n,a=t.validate,s=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var i=e[n];if("string"==typeof i){r+=i;continue}var a=t?t[i.name]:void 0,u="?"===i.modifier||"*"===i.modifier,c="*"===i.modifier||"+"===i.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var f=0;f<a.length;f++){var d=o(a[f],i);if(s&&!l[n].test(d))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+d+'"');r+=i.prefix+d+i.suffix}continue}if("string"==typeof a||"number"==typeof a){var d=o(String(a),i);if(s&&!l[n].test(d))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+d+'"');r+=i.prefix+d+i.suffix;continue}if(!u){var h=c?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+h)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,o=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var i=n[0],a=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return o(e,r)}):s[r.name]=o(n[e],r)}}(l);return{path:i,index:a,params:s}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,f="["+o(r.endsWith||"")+"]|$",d="["+o(r.delimiter||"/#?")+"]",h=void 0===s||s?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)h+=o(c(m));else{var g=o(c(m.prefix)),y=o(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var b="*"===m.modifier?"?":"";h+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+b}else h+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else h+="("+m.pattern+")"+m.modifier;else h+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)a||(h+=d+"?"),h+=r.endsWith?"(?="+f+")":"$";else{var v=e[e.length-1],E="string"==typeof v?d.indexOf(v[v.length-1])>-1:void 0===v;a||(h+="(?:"+d+"(?="+f+"))?"),E||(h+="(?="+d+"|"+f+")")}return new RegExp(h,i(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var o=t.source.match(/\((?!\?)/g);if(o)for(var l=0;l<o.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",i(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=s})(),e.exports=t})()},35416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return i},getBotType:function(){return l},isBot:function(){return s}});let n=r(95796),o=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,i=n.HTML_LIMITED_BOT_UA_RE.source;function a(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return o.test(e)||a(e)}function l(e){return o.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return x}});let n=r(11264),o=r(11448),i=r(91563),a=r(59154),s=r(6361),l=r(57391),u=r(25232),c=r(86770),f=r(2030),d=r(59435),h=r(41500),p=r(89752),m=r(68214),g=r(96493),y=r(22308),b=r(74007),v=r(36875),E=r(97860),_=r(5334),w=r(25942),T=r(4355),R=r(24642);r(50593);let{createFromFetch:S,createTemporaryReferenceSet:P,encodeReply:A}=r(19357);async function O(e,t,r){let a,l,{actionId:u,actionArgs:c}=r,f=P(),d=(0,R.extractInfoFromServerReferenceId)(u),h="use-cache"===d.type?(0,R.omitUnusedArgs)(c,d):c,p=await A(h,{temporaryReferences:f}),m=await fetch("",{method:"POST",headers:{Accept:i.RSC_CONTENT_TYPE_HEADER,[i.ACTION_HEADER]:u,[i.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[i.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,v]=(null==g?void 0:g.split(";"))||[];switch(v){case"push":a=E.RedirectType.push;break;case"replace":a=E.RedirectType.replace;break;default:a=void 0}let _=!!m.headers.get(i.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let w=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,T=m.headers.get("content-type");if(null==T?void 0:T.startsWith(i.RSC_CONTENT_TYPE_HEADER)){let e=await S(Promise.resolve(m),{callServer:n.callServer,findSourceMapURL:o.findSourceMapURL,temporaryReferences:f});return y?{actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:w,redirectType:a,revalidatedParts:l,isPrerender:_}:{actionResult:e.a,actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:w,redirectType:a,revalidatedParts:l,isPrerender:_}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===T?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:w,redirectType:a,revalidatedParts:l,isPrerender:_}}function x(e,t){let{resolve:r,reject:n}=t,o={},i=e.tree;o.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,b=Date.now();return O(e,s,t).then(async m=>{let R,{actionResult:S,actionFlightData:P,redirectLocation:A,redirectType:O,isPrerender:x,revalidatedParts:C}=m;if(A&&(O===E.RedirectType.replace?(e.pushRef.pendingPush=!1,o.pendingPush=!1):(e.pushRef.pendingPush=!0,o.pendingPush=!0),o.canonicalUrl=R=(0,l.createHrefFromUrl)(A,!1)),!P)return(r(S),A)?(0,u.handleExternalUrl)(e,o,A.href,e.pushRef.pendingPush):e;if("string"==typeof P)return r(S),(0,u.handleExternalUrl)(e,o,P,e.pushRef.pendingPush);let N=C.paths.length>0||C.tag||C.cookie;for(let n of P){let{tree:a,seedData:l,head:d,isRootRender:m}=n;if(!m)return console.log("SERVER ACTION APPLY FAILED"),r(S),e;let v=(0,c.applyRouterStatePatchToTree)([""],i,a,R||e.canonicalUrl);if(null===v)return r(S),(0,g.handleSegmentMismatch)(e,t,a);if((0,f.isNavigatingToNewRootLayout)(i,v))return r(S),(0,u.handleExternalUrl)(e,o,R||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],r=(0,p.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=l[3],(0,h.fillLazyItemsTillLeafWithHead)(b,r,void 0,a,l,d,void 0),o.cache=r,o.prefetchCache=new Map,N&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:v,updatedCache:r,includeNextUrl:!!s,canonicalUrl:o.canonicalUrl||e.canonicalUrl})}o.patchedTree=v,i=v}return A&&R?(N||((0,_.createSeededPrefetchCacheEntry)({url:A,data:{flightData:P,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:x?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),o.prefetchCache=e.prefetchCache),n((0,v.getRedirectError)((0,T.hasBasePath)(R)?(0,w.removeBasePath)(R):R,O||E.RedirectType.push))):r(S),(0,d.handleMutable)(e,o)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35471:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{A:()=>n})},36162:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(47429),o=r.n(n)},36163:(e,t,r)=>{"use strict";r.d(t,{LargeSearchToggle:()=>i,SearchToggle:()=>o});var n=r(12907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call SearchToggle() from the server but SearchToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\search-toggle.js","SearchToggle"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call LargeSearchToggle() from the server but LargeSearchToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\search-toggle.js","LargeSearchToggle")},39916:(e,t,r)=>{"use strict";var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},40463:(e,t,r)=>{"use strict";r.d(t,{G:()=>c,c:()=>u});var n=r(60687),o=r(43210),i=r(66218),a=r(95341),s=r(5261);let l=(0,i.q6)("SidebarContext");function u(){return l.use()}function c({children:e}){let t=(0,o.useRef)(!0),[r,u]=(0,o.useState)(!1),[c,f]=(0,o.useState)(!1),d=(0,i.a8)();return(0,s.T)(d,()=>{t.current&&u(!1),t.current=!0}),(0,n.jsx)(l.Provider,{value:(0,o.useMemo)(()=>({open:r,setOpen:u,collapsed:c,setCollapsed:f,closeOnRedirect:t}),[r,c]),children:(0,n.jsx)(a.GB,{open:r,onOpenChange:u,children:e})})}},40932:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:a}=e,s=n?40*n:t,l=o?40*o:r,u=s&&l?"viewBox='0 0 "+s+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},41500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,i,a,s,l,u){if(0===Object.keys(a[1]).length){r.head=l;return}for(let c in a[1]){let f,d=a[1][c],h=d[0],p=(0,n.createRouterCacheKey)(h),m=null!==s&&void 0!==s[2][c]?s[2][c]:null;if(i){let n=i.parallelRoutes.get(c);if(n){let i,a=(null==u?void 0:u.kind)==="auto"&&u.status===o.PrefetchCacheEntryStatus.reusable,s=new Map(n),f=s.get(p);i=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),navigatedAt:t}:a&&f?{lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),loading:null,navigatedAt:t},s.set(p,i),e(t,i,f,d,m||null,l,u),r.parallelRoutes.set(c,s);continue}}if(null!==m){let e=m[1],r=m[3];f={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else f={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=r.parallelRoutes.get(c);g?g.set(p,f):r.parallelRoutes.set(c,new Map([[p,f]])),e(t,f,void 0,d,m,l,u)}}}});let n=r(33123),o=r(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},44397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let n=r(33123);function o(e,t){return function e(t,r,o){if(0===Object.keys(r).length)return[t,o];if(r.children){let[i,a]=r.children,s=t.parallelRoutes.get("children");if(s){let t=(0,n.createRouterCacheKey)(i),r=s.get(t);if(r){let n=e(r,a,o+"/"+t);if(n)return n}}}for(let i in r){if("children"===i)continue;let[a,s]=r[i],l=t.parallelRoutes.get(i);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let f=e(c,s,o+"/"+u);if(f)return f}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return u},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,h=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(50148);let n=r(41480),o=r(12756),i=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function s(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,c,f,{src:d,sizes:h,unoptimized:p=!1,priority:m=!1,loading:g,className:y,quality:b,width:v,height:E,fill:_=!1,style:w,overrideSrc:T,onLoad:R,onLoadingComplete:S,placeholder:P="empty",blurDataURL:A,fetchPriority:O,decoding:x="async",layout:C,objectFit:N,objectPosition:I,lazyBoundary:M,lazyRoot:k,...L}=e,{imgConf:D,showAltText:j,blurComplete:H,defaultLoader:B}=t,U=D||o.imageConfigDefault;if("allSizes"in U)u=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),n=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);u={...U,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===B)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=L.loader||B;delete L.loader,delete L.srcSet;let G="__next_img_default"in F;if(G){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...n}=t;return e(n)}}if(C){"fill"===C&&(_=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[C];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[C];t&&!h&&(h=t)}let z="",V=s(v),$=s(E);if((l=d)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(d)?d.default:d;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,f=e.blurHeight,A=A||e.blurDataURL,z=e.src,!_)if(V||$){if(V&&!$){let t=V/e.width;$=Math.round(e.height*t)}else if(!V&&$){let t=$/e.height;V=Math.round(e.width*t)}}else V=e.width,$=e.height}let W=!m&&("lazy"===g||void 0===g);(!(d="string"==typeof d?d:z)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,W=!1),u.unoptimized&&(p=!0),G&&!u.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(p=!0);let K=s(b),X=Object.assign(_?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:N,objectPosition:I}:{},j?{}:{color:"transparent"},w),q=H||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:V,heightInt:$,blurWidth:c,blurHeight:f,blurDataURL:A||"",objectFit:X.objectFit})+'")':'url("'+P+'")',Y=i.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=q?{backgroundSize:Y,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},J=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:a,loader:s}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,a),c=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>s({config:t,src:r,quality:i,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:s({config:t,src:r,quality:i,width:l[c]})}}({config:u,src:d,unoptimized:p,width:V,quality:K,sizes:h,loader:F});return{props:{...L,loading:W?"lazy":g,fetchPriority:O,width:V,height:$,decoding:x,className:y,style:{...X,...Z},sizes:J.sizes,srcSet:J.srcSet,src:T||J.src},meta:{unoptimized:p,priority:m,placeholder:P,fill:_}}}},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(8610),o=r(60687);function i({locale:e,...t}){if(!e)throw Error(void 0);return(0,o.jsx)(n.Dk,{locale:e,...t})}},46040:(e,t,r)=>{"use strict";r.d(t,{e:()=>s});var n=r(85766);let o={language:"mandarin"},i=new Intl.Segmenter("zh-CN",{granularity:"word"});function a(e,t,r){let n;if("string"!=typeof e)return[e];let o=function(e){for(;""===e[e.length-1];)e.pop();for(;""===e[0];)e.shift();return e}(r&&this?.tokenizeSkipProperties?.has(r)?[this?.normalizeToken?.bind(this,r??"")(e)]:function(e){let t=i.segment(e),r=[];for(let e of t)e.isWordLike&&r.push(e.segment);return r}(e));return this.allowDuplicates?o:Array.from(new Set(o))}function s(e=o){let t={tokenize:a,language:e.language,stemmerSkipProperties:new Set(e.stemmerSkipProperties?[e.stemmerSkipProperties].flat():[]),tokenizeSkipProperties:new Set(e.tokenizeSkipProperties?[e.tokenizeSkipProperties].flat():[]),stopWords:e.stopWords,allowDuplicates:!!e.allowDuplicates,normalizeToken:n.JK,normalizationCache:new Map};return t.tokenize=a.bind(a),t}},46059:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});var n=r(43210),o=r(98599),i=r(66156),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[o,a]=n.useState(),l=n.useRef({}),u=n.useRef(e),c=n.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(l.current);c.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,o=s(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),u.current=e}},[e,d]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,r=r=>{let n=s(l.current).includes(r.animationName);if(r.target===o&&n&&(d("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(c.current=s(l.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(e=>{e&&(l.current=getComputedStyle(e)),a(e)},[])}}(t),l="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),u=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||a.isPresent?n.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},46250:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(29956),o=r(43210),i=r(60687),a=(0,o.forwardRef)(({href:e="#",external:t=!(e.startsWith("/")||e.startsWith("#")||e.startsWith(".")),prefetch:r,...o},a)=>t?(0,i.jsx)("a",{ref:a,href:e,rel:"noreferrer noopener",target:"_blank",...o,children:o.children}):(0,i.jsx)(n.N_,{ref:a,href:e,prefetch:r,...o}));a.displayName="Link",r(22317)},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return E}});let n=r(14985),o=r(40740),i=r(60687),a=o._(r(43210)),s=n._(r(51215)),l=n._(r(30512)),u=r(44953),c=r(12756),f=r(17903);r(50148);let d=r(69148),h=n._(r(1933)),p=r(53038),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,r,n,o,i,a){let s=null==e?void 0:e.src;e&&e["data-loaded-src"]!==s&&(e["data-loaded-src"]=s,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:s,width:l,decoding:u,className:c,style:f,fetchPriority:d,placeholder:h,loading:m,unoptimized:b,fill:v,onLoadRef:E,onLoadingCompleteRef:_,setBlurComplete:w,setShowAltText:T,sizesInput:R,onLoad:S,onError:P,...A}=e,O=(0,a.useCallback)(e=>{e&&(P&&(e.src=e.src),e.complete&&g(e,h,E,_,w,b,R))},[r,h,E,_,w,P,b,R]),x=(0,p.useMergedRef)(t,O);return(0,i.jsx)("img",{...A,...y(d),loading:m,width:l,height:s,decoding:u,"data-nimg":v?"fill":"1",className:c,style:f,sizes:o,srcSet:n,src:r,ref:x,onLoad:e=>{g(e.currentTarget,h,E,_,w,b,R)},onError:e=>{T(!0),"empty"!==h&&w(!0),P&&P(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&s.default.preload?(s.default.preload(r.src,n),null):(0,i.jsx)(l.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let E=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(d.RouterContext),n=(0,a.useContext)(f.ImageConfigContext),o=(0,a.useMemo)(()=>{var e;let t=m||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:s,onLoadingComplete:l}=e,p=(0,a.useRef)(s);(0,a.useEffect)(()=>{p.current=s},[s]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[y,E]=(0,a.useState)(!1),[_,w]=(0,a.useState)(!1),{props:T,meta:R}=(0,u.getImgProps)(e,{defaultLoader:h.default,imgConf:o,blurComplete:y,showAltText:_});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{...T,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:E,setShowAltText:w,sizesInput:e.sizes,ref:t}),R.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:T}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47429:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\next\\dist\\client\\script.js")},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(43210),o=()=>{},i=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function s(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),s(),o(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),o(()=>(r&&(r._pendingUpdate=s),()=>{r&&(r._pendingUpdate=s)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},47909:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(26373).A)("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]])},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49384:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return f},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return o},prefetch:function(){return n},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return i},schedulePrefetchTask:function(){return s}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,o=r,i=r,a=r,s=r,l=r,u=r,c=r;var f=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},52923:(e,t,r)=>{"use strict";r.d(t,{LanguageToggle:()=>l,LanguageToggleText:()=>u});var n=r(60687),o=r(72527),i=r(74047),a=r(82348),s=r(22880);function l(e){let t=(0,o.s9)();if(!t.locales)throw Error("Missing `<I18nProvider />`");return(0,n.jsxs)(i.Popover,{children:[(0,n.jsx)(i.PopoverTrigger,{"aria-label":t.text.chooseLanguage,...e,className:(0,a.QP)((0,s.r)({color:"ghost",className:"gap-1.5 p-1.5"}),e.className),children:e.children}),(0,n.jsxs)(i.PopoverContent,{className:"flex flex-col overflow-hidden p-0",children:[(0,n.jsx)("p",{className:"mb-1 p-2 text-xs font-medium text-fd-muted-foreground",children:t.text.chooseLanguage}),t.locales.map(e=>(0,n.jsx)("button",{type:"button",className:(0,a.QP)("p-2 text-start text-sm",e.locale===t.locale?"bg-fd-primary/10 font-medium text-fd-primary":"hover:bg-fd-accent hover:text-fd-accent-foreground"),onClick:()=>{t.onChange?.(e.locale)},children:e.name},e.locale))]})]})}function u(e){let t=(0,o.s9)(),r=t.locales?.find(e=>e.locale===t.locale)?.name;return(0,n.jsx)("span",{...e,children:r})}},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(43210);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},54674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return n}}),r(84949),r(19169);let n=e=>(e.startsWith("/"),e);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56025:(e,t,r)=>{"use strict";function n(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function o(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}function i(e,t,r){return"string"==typeof e?e:e[t]||r}function a(e){let t=function(){try{return!0}catch{return!1}}();if("/"!==e){let r=e.endsWith("/");t&&!r?e+="/":!t&&r&&(e=e.slice(0,-1))}return e}function s(e,t){return"never"!==t.mode&&t.prefixes?.[e]||"/"+e}function l(e){return e.includes("[[...")}function u(e){return e.includes("[...")}function c(e){return e.includes("[")}r.d(t,{PJ:()=>o,Wl:()=>i,XP:()=>s,_x:()=>n,po:()=>a,yL:()=>f});function f(e){return"function"==typeof e.then}},56085:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},56928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return i}});let n=r(41500),o=r(33898);function i(e,t,r,i,a){let{tree:s,seedData:l,head:u,isRootRender:c}=i;if(null===l)return!1;if(c){let o=l[1];r.loading=l[3],r.rsc=o,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,s,l,u,a)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,o.fillCacheWithNewSubTreeData)(e,r,t,i,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return i}});let n=r(70642);function o(e){return void 0!==e}function i(e,t){var r,i;let a=null==(r=t.shouldScroll)||r,s=e.nextUrl;if(o(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?s=r:s||(s=e.canonicalUrl)}return{canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(i=null==t?void 0:t.scrollableSegments)?i:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>o});var n=0;function o(e){return"__private_"+n+++"_"+e}},61481:(e,t,r)=>{"use strict";r.d(t,{LargeSearchToggle:()=>c,SearchToggle:()=>u});var n=r(60687),o=r(99270),i=r(79943),a=r(72527),s=r(82348),l=r(22880);function u({hideIfDisabled:e,size:t="icon",color:r="ghost",...a}){let{setOpenSearch:u,enabled:c}=(0,i.$A)();return e&&!c?null:(0,n.jsx)("button",{type:"button",className:(0,s.QP)((0,l.r)({size:t,color:r}),a.className),"data-search":"","aria-label":"Open Search",onClick:()=>{u(!0)},children:(0,n.jsx)(o.A,{className:"p-px"})})}function c({hideIfDisabled:e,...t}){let{enabled:r,hotKey:l,setOpenSearch:u}=(0,i.$A)(),{text:c}=(0,a.s9)();return e&&!r?null:(0,n.jsxs)("button",{type:"button","data-search-full":"",...t,className:(0,s.QP)("inline-flex items-center gap-2 rounded-full border bg-fd-secondary/50 p-1.5 text-sm text-fd-muted-foreground transition-colors hover:bg-fd-accent hover:text-fd-accent-foreground",t.className),onClick:()=>{u(!0)},children:[(0,n.jsx)(o.A,{className:"ms-1 size-4"}),c.search,(0,n.jsx)("div",{className:"ms-auto inline-flex gap-0.5",children:l.map((e,t)=>(0,n.jsx)("kbd",{className:"rounded-md border bg-fd-background px-1.5",children:e.display},t))})]})}},61794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(79289),o=r(4355);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},61883:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>p});var n=r(60687),o=r(24224),i=r(62688);let a=(0,i.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),s=(0,i.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),l=(0,i.A)("airplay",[["path",{d:"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1",key:"ns4c3b"}],["path",{d:"m12 15 5 6H7Z",key:"14qnn2"}]]);var u=r(10218),c=r(43210),f=r(82348);let d=(0,o.F)("size-6.5 rounded-full p-1.5 text-fd-muted-foreground",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground",false:"text-fd-muted-foreground"}}}),h=[["light",a],["dark",s],["system",l]];function p({className:e,mode:t="light-dark",...r}){let{setTheme:o,theme:i,resolvedTheme:a}=(0,u.D)(),[s,l]=(0,c.useState)(!1),p=(0,f.QP)("inline-flex items-center rounded-full border p-1",e);if("light-dark"===t){let e=s?a:null;return(0,n.jsx)("button",{className:p,"aria-label":"Toggle Theme",onClick:()=>o("light"===e?"dark":"light"),"data-theme-toggle":"",...r,children:h.map(([t,r])=>{if("system"!==t)return(0,n.jsx)(r,{fill:"currentColor",className:(0,f.QP)(d({active:e===t}))},t)})})}let m=s?i:null;return(0,n.jsx)("div",{className:p,"data-theme-toggle":"",...r,children:h.map(([e,t])=>(0,n.jsx)("button",{"aria-label":e,className:(0,f.QP)(d({active:m===e})),onClick:()=>o(e),children:(0,n.jsx)(t,{className:"size-full",fill:"currentColor"})},e))})}},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:i="",children:a,iconNode:u,...c},f)=>(0,n.createElement)("svg",{ref:f,...l,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:s("lucide",i),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(a)?a:[a]])),c=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},l)=>(0,n.createElement)(u,{ref:l,iconNode:t,className:s(`lucide-${o(a(e))}`,`lucide-${e}`,r),...i}));return r.displayName=a(e),r}},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63376:(e,t,r)=>{"use strict";r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},s=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var c=a[r],f=[],d=new Set,h=new Set(u),p=function(e){!e||d.has(e)||(d.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||h.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,s=(o.get(e)||0)+1,l=(c.get(e)||0)+1;o.set(e,s),c.set(e,l),f.push(e),1===s&&a&&i.set(e,!0),1===l&&e.setAttribute(r,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),s++,function(){f.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),a||e.removeAttribute(r)}),--s||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),u(o,i,r,"aria-hidden")):function(){return null}}},63690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return b},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return v}});let n=r(59154),o=r(8830),i=r(43210),a=r(91992);r(50593);let s=r(19129),l=r(96127),u=r(89752),c=r(75076),f=r(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?h({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function h(e){let{actionQueue:t,action:r,setState:n}=e,o=t.state;t.pending=r;let i=r.payload,s=t.action(o,i);function l(e){r.discarded||(t.state=e,d(t,n),r.resolve(e))}(0,a.isThenable)(s)?s.then(l,e=>{d(t,n),r.reject(e)}):l(s)}function p(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let o={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,i.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=a,h({actionQueue:e,action:a,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),h({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(r,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function m(){return null}function g(){return null}function y(e,t,r,o){let i=new URL((0,l.addBasePath)(e),location.href);(0,f.setLinkForCurrentNavigation)(o);(0,s.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:i,isExternalUrl:(0,u.isExternalURL)(i),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function b(e,t){(0,s.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let v={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),o=(0,u.createPrefetchURL)(e);if(null!==o){var i;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:o,kind:null!=(i=null==t?void 0:t.kind)?i:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,i.startTransition)(()=>{var r;y(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,i.startTransition)(()=>{var r;y(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,i.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65551:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var n,o=r(43210),i=r(66156),a=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,s,l]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),s=o.useRef(t);return a(()=>{s.current=t},[t]),o.useEffect(()=>{i.current!==r&&(s.current?.(r),i.current=r)},[r,i]),[r,n,s]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},65951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[i,a]=r,[s,l]=t;return(0,o.matchSegment)(s,i)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),a[l]):!!Array.isArray(s)}}});let n=r(74007),o=r(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return h},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],o=t.parallelRoutes,a=new Map(o);for(let t in n){let r=n[t],s=r[0],l=(0,i.createRouterCacheKey)(s),u=o.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let o=e(n,r),i=new Map(u);i.set(l,o),a.set(t,i)}}}let s=t.rsc,l=y(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let n=r(83913),o=r(14077),i=r(33123),a=r(2030),s=r(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,r,a,s,u,d,h,p){return function e(t,r,a,s,u,d,h,p,m,g,y){let b=a[1],v=s[1],E=null!==d?d[2]:null;u||!0===s[4]&&(u=!0);let _=r.parallelRoutes,w=new Map(_),T={},R=null,S=!1,P={};for(let r in v){let a,s=v[r],f=b[r],d=_.get(r),A=null!==E?E[r]:null,O=s[0],x=g.concat([r,O]),C=(0,i.createRouterCacheKey)(O),N=void 0!==f?f[0]:void 0,I=void 0!==d?d.get(C):void 0;if(null!==(a=O===n.DEFAULT_SEGMENT_KEY?void 0!==f?{route:f,node:null,dynamicRequestTree:null,children:null}:c(t,f,s,I,u,void 0!==A?A:null,h,p,x,y):m&&0===Object.keys(s[1]).length?c(t,f,s,I,u,void 0!==A?A:null,h,p,x,y):void 0!==f&&void 0!==N&&(0,o.matchSegment)(O,N)&&void 0!==I&&void 0!==f?e(t,I,f,s,u,A,h,p,m,x,y):c(t,f,s,I,u,void 0!==A?A:null,h,p,x,y))){if(null===a.route)return l;null===R&&(R=new Map),R.set(r,a);let e=a.node;if(null!==e){let t=new Map(d);t.set(C,e),w.set(r,t)}let t=a.route;T[r]=t;let n=a.dynamicRequestTree;null!==n?(S=!0,P[r]=n):P[r]=t}else T[r]=s,P[r]=s}if(null===R)return null;let A={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:w,navigatedAt:t};return{route:f(s,T),node:A,dynamicRequestTree:S?f(s,P):null,children:R}}(e,t,r,a,!1,s,u,d,h,[],p)}function c(e,t,r,n,o,u,c,h,p,m){return!o&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,r))?l:function e(t,r,n,o,a,l,u,c){let h,p,m,g,y=r[1],b=0===Object.keys(y).length;if(void 0!==n&&n.navigatedAt+s.DYNAMIC_STALETIME_MS>t)h=n.rsc,p=n.loading,m=n.head,g=n.navigatedAt;else if(null===o)return d(t,r,null,a,l,u,c);else if(h=o[1],p=o[3],m=b?a:null,g=t,o[4]||l&&b)return d(t,r,o,a,l,u,c);let v=null!==o?o[2]:null,E=new Map,_=void 0!==n?n.parallelRoutes:null,w=new Map(_),T={},R=!1;if(b)c.push(u);else for(let r in y){let n=y[r],o=null!==v?v[r]:null,s=null!==_?_.get(r):void 0,f=n[0],d=u.concat([r,f]),h=(0,i.createRouterCacheKey)(f),p=e(t,n,void 0!==s?s.get(h):void 0,o,a,l,d,c);E.set(r,p);let m=p.dynamicRequestTree;null!==m?(R=!0,T[r]=m):T[r]=n;let g=p.node;if(null!==g){let e=new Map;e.set(h,g),w.set(r,e)}}return{route:r,node:{lazyData:null,rsc:h,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:w,navigatedAt:g},dynamicRequestTree:R?f(r,T):null,children:E}}(e,r,n,u,c,h,p,m)}function f(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function d(e,t,r,n,o,a,s){let l=f(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,r,n,o,a,s,l){let u=r[1],c=null!==n?n[2]:null,f=new Map;for(let r in u){let n=u[r],d=null!==c?c[r]:null,h=n[0],p=s.concat([r,h]),m=(0,i.createRouterCacheKey)(h),g=e(t,n,void 0===d?null:d,o,a,p,l),y=new Map;y.set(m,g),f.set(r,y)}let d=0===f.size;d&&l.push(s);let h=null!==n?n[1]:null,p=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:f,prefetchRsc:void 0!==h?h:null,prefetchHead:d?o:[null,null],loading:void 0!==p?p:null,rsc:b(),head:d?b():null,navigatedAt:t}}(e,t,r,n,o,a,s),dynamicRequestTree:l,children:null}}function h(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:a,head:s}=t;a&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],i=s.children;if(null!==i){let e=i.get(r);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(n,t)){s=e;continue}}}return}!function e(t,r,n,a){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],c=a[2],f=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=c[t],d=f.get(t),h=r[0],p=(0,i.createRouterCacheKey)(h),g=void 0!==d?d.get(p):void 0;void 0!==g&&(void 0!==n&&(0,o.matchSegment)(h,n[0])&&null!=a?e(g,r,n,a,s):m(r,g,null))}let d=t.rsc,h=a[1];null===d?t.rsc=h:y(d)&&d.resolve(h);let p=t.head;y(p)&&p.resolve(s)}(l,t.route,r,n,a),t.dynamicRequestTree=null);return}let u=r[1],c=n[2];for(let t in r){let r=u[t],n=c[t],i=s.get(t);if(void 0!==i){let t=i.route[0];if((0,o.matchSegment)(r[0],t)&&null!=n)return e(i,r,n,a)}}}(s,r,n,a)}(e,r,n,a,s)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)m(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,r){let n=e[1],o=t.parallelRoutes;for(let e in n){let t=n[e],a=o.get(e);if(void 0===a)continue;let s=t[0],l=(0,i.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&m(t,u,r)}let a=t.rsc;y(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;y(s)&&s.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function b(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=g,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66156:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},66218:(e,t,r)=>{"use strict";r.d(t,{Image:()=>n._V,a8:()=>n.a8,q6:()=>n.q6,rd:()=>n.rd});var n=r(29956);r(22317)},67424:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(61120),o=r(84604),i=r(56025);r(99933);var a=r(86280);r(73913);let s=(0,n.cache)(function(){return{locale:void 0}}),l=(0,n.cache)(async function(){let e=(0,a.b)();return(0,i.yL)(e)?await e:e}),u=(0,n.cache)(async function(){let e;try{e=(await l()).get("X-NEXT-INTL-LOCALE")||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function c(){return s().locale||await u()}var f=r(68444);let d=(0,n.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),h=(0,n.cache)(async function(e,t){let r=e({locale:t,get requestLocale(){return t?Promise.resolve(t):c()}});if((0,i.yL)(r)&&(r=await r),!r.locale)throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");return r}),p=(0,n.cache)(o.b),m=(0,n.cache)(o.d),g=(0,n.cache)(async function(e){let t=await h(f.A,e);return{...(0,o.i)(t),_formatters:p(m()),timeZone:t.timeZone||d()}})},69148:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.RouterContext},69593:(e,t,r)=>{"use strict";r.d(t,{$J:()=>g,$S:()=>function e(t){return Array.isArray(t)?t.some(t=>e(t)):t?.constructor?.name==="AsyncFunction"},He:()=>h,JN:()=>b,JW:()=>w,NF:()=>p,O6:()=>_,g5:()=>m,gG:()=>P,h:()=>u,iR:()=>R,j7:()=>d,nv:()=>c,wH:()=>v,y$:()=>y});var n=r(85665);let o=Date.now().toString().slice(5),i=0,a=BigInt(1e3),s=BigInt(1e6),l=BigInt(1e9);function u(e,t){if(t.length<65535)Array.prototype.push.apply(e,t);else{let r=t.length;for(let n=0;n<r;n+=65535)Array.prototype.push.apply(e,t.slice(n,n+65535))}}function c(e,...t){return e.replace(/%(?:(?<position>\d+)\$)?(?<width>-?\d*\.?\d*)(?<type>[dfs])/g,function(...e){let{width:r,type:n,position:o}=e[e.length-1],i=o?t[Number.parseInt(o)-1]:t.shift(),a=""===r?0:Number.parseInt(r);switch(n){case"d":return i.toString().padStart(a,"0");case"f":{let e=i,[t,n]=r.split(".").map(e=>Number.parseFloat(e));return"number"==typeof n&&n>=0&&(e=e.toFixed(n)),"number"==typeof t&&t>=0?e.toString().padStart(a,"0"):e.toString()}case"s":return a<0?i.toString().padEnd(-a," "):i.toString().padStart(a," ");default:return i}})}function f(){return BigInt(Math.floor(1e6*performance.now()))}function d(e){return("number"==typeof e&&(e=BigInt(e)),e<a)?`${e}ns`:e<s?`${e/a}μs`:e<l?`${e/s}ms`:`${e/l}s`}function h(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?f():"undefined"!=typeof process&&process.release&&"node"===process.release.name||"undefined"!=typeof process&&"function"==typeof process?.hrtime?.bigint?process.hrtime.bigint():"undefined"!=typeof performance?f():BigInt(0)}function p(){return`${o}-${i++}`}function m(e,t){return void 0===Object.hasOwn?Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0:Object.hasOwn(e,t)?e[t]:void 0}function g(e,t){return t[1]===e[1]?e[0]-t[0]:t[1]-e[1]}function y(e){if(0===e.length)return[];if(1===e.length)return e[0];for(let t=1;t<e.length;t++)if(e[t].length<e[0].length){let r=e[0];e[0]=e[t],e[t]=r}let t=new Map;for(let r of e[0])t.set(r,1);for(let r=1;r<e.length;r++){let n=0;for(let o of e[r]){let e=t.get(o);e===r&&(t.set(o,e+1),n++)}if(0===n)return[]}return e[0].filter(r=>{let n=t.get(r);return void 0!==n&&t.set(r,0),n===e.length})}function b(e,t){let r={},n=t.length;for(let o=0;o<n;o++){let n=t[o],i=n.split("."),a=e,s=i.length;for(let e=0;e<s;e++)if("object"==typeof(a=a[i[e]])){if(null!==a&&"lat"in a&&"lon"in a&&"number"==typeof a.lat&&"number"==typeof a.lon){a=r[n]=a;break}else if(!Array.isArray(a)&&null!==a&&e===s-1){a=void 0;break}}else if((null===a||"object"!=typeof a)&&e<s-1){a=void 0;break}void 0!==a&&(r[n]=a)}return r}function v(e,t){return b(e,[t])[t]}let E={cm:.01,m:1,km:1e3,ft:.3048,yd:.9144,mi:1609.344};function _(e,t){let r=E[t];if(void 0===r)throw Error((0,n.$)("INVALID_DISTANCE_SUFFIX",e).message);return e*r}function w(e,t){e.hits=e.hits.map(e=>({...e,document:{...e.document,...t.reduce((e,t)=>{let r=t.split("."),n=r.pop(),o=e;for(let e of r)o[e]=o[e]??{},o=o[e];return o[n]=null,e},e.document)}}))}let T="intersection"in new Set;function R(...e){if(0===e.length)return new Set;if(1===e.length)return e[0];if(2===e.length){let t=e[0],r=e[1];if(T)return t.intersection(r);let n=new Set,o=t.size<r.size?t:r,i=o===t?r:t;for(let e of o)i.has(e)&&n.add(e);return n}let t={index:0,size:e[0].size};for(let r=1;r<e.length;r++)e[r].size<t.size&&(t.index=r,t.size=e[r].size);if(T){let r=e[t.index];for(let n=0;n<e.length;n++)n!==t.index&&(r=r.intersection(e[n]));return r}let r=e[t.index];for(let n=0;n<e.length;n++){if(n===t.index)continue;let o=e[n];for(let e of r)o.has(e)||r.delete(e)}return r}let S="union"in new Set;function P(e,t){return S?e?e.union(t):t:new Set(e?[...e,...t]:t)}},69767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\node_modules\\\\fumadocs-core\\\\dist\\\\link.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-core\\dist\\link.js","default")},70554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},70569:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},70642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),a=i?t[1]:t;!a||a.startsWith(o.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(72859),o=r(83913),i=r(14077),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===o.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(o.PAGE_SEGMENT_KEY))return"";let i=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)i.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&i.push(r)}return l(i)}function c(e,t){let r=function e(t,r){let[o,a]=t,[l,c]=r,f=s(o),d=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>f.startsWith(e)||d.startsWith(e)))return"";if(!(0,i.matchSegment)(o,l)){var h;return null!=(h=u(r))?h:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),o=r(52637),i=r(51846),a=r(31162),s=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let n=r(74722),o=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function a(e){let t,r,i;for(let n of e.split("/"))if(r=o.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=a.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},72527:(e,t,r)=>{"use strict";r.d(t,{Cr:()=>o,I18nLabel:()=>a,gJ:()=>i,s9:()=>s});var n=r(43210);let o={search:"Search",searchNoResult:"No results found",toc:"On this page",tocNoHeadings:"No Headings",lastUpdate:"Last updated on",chooseLanguage:"Choose a language",nextPage:"Next Page",previousPage:"Previous Page",chooseTheme:"Theme",editOnGithub:"Edit on GitHub"},i=(0,n.createContext)({text:o});function a(e){let{text:t}=s();return t[e.label]}function s(){return(0,n.useContext)(i)}},73406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return b},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return E},onNavigationIntent:function(){return _},pingVisibleLinks:function(){return T},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return f},unmountPrefetchableInstance:function(){return v}}),r(63690);let n=r(89752),o=r(59154),i=r(50593),a=r(43210),s=null,l={pending:!0},u={pending:!1};function c(e){(0,a.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),s=e})}function f(e){s===e&&(s=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,h=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;E(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==d.get(e)&&v(e),d.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,r,n,o,i){if(o){let o=g(t);if(null!==o){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:i};return m(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:i}}function b(e,t,r,n){let o=g(t);null!==o&&m(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function v(e){let t=d.get(e);if(void 0!==t){d.delete(e),h.delete(t);let r=t.prefetchTask;null!==r&&(0,i.cancelPrefetchTask)(r)}null!==p&&p.unobserve(e)}function E(e,t){let r=d.get(e);void 0!==r&&(r.isVisible=t,t?h.add(r):h.delete(r),w(r))}function _(e,t){let r=d.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,w(r))}function w(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,i.cancelPrefetchTask)(t);return}}function T(e,t){let r=(0,i.getCurrentCacheVersion)();for(let n of h){let a=n.prefetchTask;if(null!==a&&n.cacheVersion===r&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,i.cancelPrefetchTask)(a);let s=(0,i.createCacheKey)(n.prefetchHref,e),l=n.wasHoveredOrTouched?i.PrefetchPriority.Intent:i.PrefetchPriority.Default;n.prefetchTask=(0,i.schedulePrefetchTask)(s,t,n.kind===o.PrefetchKind.FULL,l),n.cacheVersion=(0,i.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73913:(e,t,r)=>{"use strict";let n=r(63033),o=r(29294),i=r(84971),a=r(76926),s=r(80023),l=r(98479);function u(){let e=o.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return c(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return c(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return d(null);default:return t}}function c(e,t){let r,n=f.get(u);return n||(r=d(e),f.set(e,r),r)}let f=new WeakMap;function d(e){let t=new h(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class h{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=o.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new l.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},74047:(e,t,r)=>{"use strict";r.d(t,{Popover:()=>tb,PopoverContent:()=>tE,PopoverTrigger:()=>tv});var n=r(60687),o=r(43210),i=r(70569),a=r(98599),s=r(11273),l=r(31355),u=r(1359),c=r(32547),f=r(96963);let d=["top","right","bottom","left"],h=Math.min,p=Math.max,m=Math.round,g=Math.floor,y=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},v={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function _(e){return e.split("-")[0]}function w(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function R(e){return"y"===e?"height":"width"}function S(e){return["top","bottom"].includes(_(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>v[e])}function A(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function C(e,t,r){let n,{reference:o,floating:i}=e,a=S(t),s=T(S(t)),l=R(s),u=_(t),c="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,h=o[l]/2-i[l]/2;switch(u){case"top":n={x:f,y:o.y-i.height};break;case"bottom":n={x:f,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:d};break;case"left":n={x:o.x-i.width,y:d};break;default:n={x:o.x,y:o.y}}switch(w(t)){case"start":n[s]-=h*(r&&c?-1:1);break;case"end":n[s]+=h*(r&&c?-1:1)}return n}let N=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:a}=r,s=i.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=C(u,n,l),d=n,h={},p=0;for(let r=0;r<s.length;r++){let{name:i,fn:m}=s[r],{x:g,y:y,data:b,reset:v}=await m({x:c,y:f,initialPlacement:n,placement:d,strategy:o,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=y?y:f,h={...h,[i]:{...h[i],...b}},v&&p<=50&&(p++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(u=!0===v.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):v.rects),{x:c,y:f}=C(u,d,l)),r=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:h}};async function I(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:h=0}=E(t,e),p=O(h),m=s[d?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(m)))||r?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===f?{x:n,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),v=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},_=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:b,strategy:l}):y);return{top:(g.top-_.top+p.top)/v.y,bottom:(_.bottom-g.bottom+p.bottom)/v.y,left:(g.left-_.left+p.left)/v.x,right:(_.right-g.right+p.right)/v.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return d.some(t=>e[t]>=0)}async function L(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),a=_(r),s=w(r),l="y"===S(r),u=["left","top"].includes(a)?-1:1,c=i&&l?-1:1,f=E(t,e),{mainAxis:d,crossAxis:h,alignmentAxis:p}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return s&&"number"==typeof p&&(h="end"===s?-1*p:p),l?{x:h*c,y:d*u}:{x:d*u,y:h*c}}function D(){return"undefined"!=typeof window}function j(e){return U(e)?(e.nodeName||"").toLowerCase():"#document"}function H(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function B(e){var t;return null==(t=(U(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function U(e){return!!D()&&(e instanceof Node||e instanceof H(e).Node)}function F(e){return!!D()&&(e instanceof Element||e instanceof H(e).Element)}function G(e){return!!D()&&(e instanceof HTMLElement||e instanceof H(e).HTMLElement)}function z(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof H(e).ShadowRoot)}function V(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=q(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function $(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(e){let t=K(),r=F(e)?q(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function X(e){return["html","body","#document"].includes(j(e))}function q(e){return H(e).getComputedStyle(e)}function Y(e){return F(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Z(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||z(e)&&e.host||B(e);return z(t)?t.host:t}function J(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=Z(t);return X(r)?t.ownerDocument?t.ownerDocument.body:t.body:G(r)&&V(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),a=H(o);if(i){let e=Q(a);return t.concat(a,a.visualViewport||[],V(o)?o:[],e&&r?J(e):[])}return t.concat(o,J(o,[],r))}function Q(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ee(e){let t=q(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=G(e),i=o?e.offsetWidth:r,a=o?e.offsetHeight:n,s=m(r)!==i||m(n)!==a;return s&&(r=i,n=a),{width:r,height:n,$:s}}function et(e){return F(e)?e:e.contextElement}function er(e){let t=et(e);if(!G(t))return y(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=ee(t),a=(i?m(r.width):r.width)/n,s=(i?m(r.height):r.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let en=y(0);function eo(e){let t=H(e);return K()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:en}function ei(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),a=et(e),s=y(1);t&&(n?F(n)&&(s=er(n)):s=er(e));let l=(void 0===(o=r)&&(o=!1),n&&(!o||n===H(a))&&o)?eo(a):y(0),u=(i.left+l.x)/s.x,c=(i.top+l.y)/s.y,f=i.width/s.x,d=i.height/s.y;if(a){let e=H(a),t=n&&F(n)?H(n):n,r=e,o=Q(r);for(;o&&n&&t!==r;){let e=er(o),t=o.getBoundingClientRect(),n=q(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=a,o=Q(r=H(o))}}return x({width:f,height:d,x:u,y:c})}function ea(e,t){let r=Y(e).scrollLeft;return t?t.left+r:ei(B(e)).left+r}function es(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ea(e,n)),y:n.top+t.scrollTop}}function el(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=H(e),n=B(e),o=r.visualViewport,i=n.clientWidth,a=n.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;let e=K();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=B(e),r=Y(e),n=e.ownerDocument.body,o=p(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=p(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+ea(e),s=-r.scrollTop;return"rtl"===q(n).direction&&(a+=p(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:a,y:s}}(B(e));else if(F(t))n=function(e,t){let r=ei(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=G(e)?er(e):y(1),a=e.clientWidth*i.x,s=e.clientHeight*i.y;return{width:a,height:s,x:o*i.x,y:n*i.y}}(t,r);else{let r=eo(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function eu(e){return"static"===q(e).position}function ec(e,t){if(!G(e)||"fixed"===q(e).position)return null;if(t)return t(e);let r=e.offsetParent;return B(e)===r&&(r=r.ownerDocument.body),r}function ef(e,t){let r=H(e);if($(e))return r;if(!G(e)){let t=Z(e);for(;t&&!X(t);){if(F(t)&&!eu(t))return t;t=Z(t)}return r}let n=ec(e,t);for(;n&&["table","td","th"].includes(j(n))&&eu(n);)n=ec(n,t);return n&&X(n)&&eu(n)&&!W(n)?r:n||function(e){let t=Z(e);for(;G(t)&&!X(t);){if(W(t))return t;if($(t))break;t=Z(t)}return null}(e)||r}let ed=async function(e){let t=this.getOffsetParent||ef,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=G(t),o=B(t),i="fixed"===r,a=ei(e,!0,i,t),s={scrollLeft:0,scrollTop:0},l=y(0);if(n||!n&&!i)if(("body"!==j(t)||V(o))&&(s=Y(t)),n){let e=ei(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=ea(o));let u=!o||n||i?y(0):es(o,s);return{x:a.left+s.scrollLeft-l.x-u.x,y:a.top+s.scrollTop-l.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eh={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,a=B(n),s=!!t&&$(t.floating);if(n===a||s&&i)return r;let l={scrollLeft:0,scrollTop:0},u=y(1),c=y(0),f=G(n);if((f||!f&&!i)&&(("body"!==j(n)||V(a))&&(l=Y(n)),G(n))){let e=ei(n);u=er(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let d=!a||f||i?y(0):es(a,l,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-l.scrollLeft*u.x+c.x+d.x,y:r.y*u.y-l.scrollTop*u.y+c.y+d.y}},getDocumentElement:B,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?$(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=J(e,[],!1).filter(e=>F(e)&&"body"!==j(e)),o=null,i="fixed"===q(e).position,a=i?Z(e):e;for(;F(a)&&!X(a);){let t=q(a),r=W(a);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||V(a)&&!r&&function e(t,r){let n=Z(t);return!(n===r||!F(n)||X(n))&&("fixed"===q(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):o=t,a=Z(a)}return t.set(e,n),n}(t,this._c):[].concat(r),n],a=i[0],s=i.reduce((e,r)=>{let n=el(t,r,o);return e.top=p(n.top,e.top),e.right=h(n.right,e.right),e.bottom=h(n.bottom,e.bottom),e.left=p(n.left,e.left),e},el(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ef,getElementRects:ed,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ee(e);return{width:t,height:r}},getScale:er,isElement:F,isRTL:function(e){return"rtl"===q(e).direction}};function ep(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let em=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:a,elements:s,middlewareData:l}=t,{element:u,padding:c=0}=E(e,t)||{};if(null==u)return{};let f=O(c),d={x:r,y:n},m=T(S(o)),g=R(m),y=await a.getDimensions(u),b="y"===m,v=b?"clientHeight":"clientWidth",_=i.reference[g]+i.reference[m]-d[m]-i.floating[g],P=d[m]-i.reference[m],A=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),x=A?A[v]:0;x&&await (null==a.isElement?void 0:a.isElement(A))||(x=s.floating[v]||i.floating[g]);let C=x/2-y[g]/2-1,N=h(f[b?"top":"left"],C),I=h(f[b?"bottom":"right"],C),M=x-y[g]-I,k=x/2-y[g]/2+(_/2-P/2),L=p(N,h(k,M)),D=!l.arrow&&null!=w(o)&&k!==L&&i.reference[g]/2-(k<N?N:I)-y[g]/2<0,j=D?k<N?k-N:k-M:0;return{[m]:d[m]+j,data:{[m]:L,centerOffset:k-L-j,...D&&{alignmentOffset:j}},reset:D}}}),eg=(e,t,r)=>{let n=new Map,o={platform:eh,...r},i={...o.platform,_c:n};return N(e,t,{...o,platform:i})};var ey=r(51215),eb="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function ev(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ev(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ev(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eE(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e_(e,t){let r=eE(e);return Math.round(t*r)/r}function ew(e){let t=o.useRef(e);return eb(()=>{t.current=e}),t}let eT=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?em({element:r.current,padding:n}).fn(t):{}:r?em({element:r,padding:n}).fn(t):{}}}),eR=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:a,middlewareData:s}=t,l=await L(t,e);return a===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),eS=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=E(e,t),u={x:r,y:n},c=await I(t,l),f=S(_(o)),d=T(f),m=u[d],g=u[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+c[e],n=m-c[t];m=p(r,h(m,n))}if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=g+c[e],n=g-c[t];g=p(r,h(g,n))}let y=s.fn({...t,[d]:m,[f]:g});return{...y,data:{x:y.x-r,y:y.y-n,enabled:{[d]:i,[f]:a}}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=E(e,t),c={x:r,y:n},f=S(o),d=T(f),h=c[d],p=c[f],m=E(s,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,r=i.reference[d]+i.reference[e]-g.mainAxis;h<t?h=t:h>r&&(h=r)}if(u){var y,b;let e="y"===d?"width":"height",t=["top","left"].includes(_(o)),r=i.reference[f]-i.floating[e]+(t&&(null==(y=a.offset)?void 0:y[f])||0)+(t?0:g.crossAxis),n=i.reference[f]+i.reference[e]+(t?0:(null==(b=a.offset)?void 0:b[f])||0)-(t?g.crossAxis:0);p<r?p=r:p>n&&(p=n)}return{[d]:h,[f]:p}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:h=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:b=!0,...v}=E(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let O=_(s),x=S(c),C=_(c)===c,N=await (null==f.isRTL?void 0:f.isRTL(d.floating)),M=m||(C||!b?[A(c)]:function(e){let t=A(e);return[P(e),t,P(t)]}(c)),k="none"!==y;!m&&k&&M.push(...function(e,t,r,n){let o=w(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(_(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(P)))),i}(c,b,y,N));let L=[c,...M],D=await I(t,v),j=[],H=(null==(n=l.flip)?void 0:n.overflows)||[];if(h&&j.push(D[O]),p){let e=function(e,t,r){void 0===r&&(r=!1);let n=w(e),o=T(S(e)),i=R(o),a="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=A(a)),[a,A(a)]}(s,u,N);j.push(D[e[0]],D[e[1]])}if(H=[...H,{placement:s,overflows:j}],!j.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=L[e];if(t)return{data:{index:e,overflows:H},reset:{placement:t}};let r=null==(i=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(g){case"bestFit":{let e=null==(a=H.filter(e=>{if(k){let t=S(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=c}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i,{placement:a,rects:s,platform:l,elements:u}=t,{apply:c=()=>{},...f}=E(e,t),d=await I(t,f),m=_(a),g=w(a),y="y"===S(a),{width:b,height:v}=s.floating;"top"===m||"bottom"===m?(o=m,i=g===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(i=m,o="end"===g?"top":"bottom");let T=v-d.top-d.bottom,R=b-d.left-d.right,P=h(v-d[o],T),A=h(b-d[i],R),O=!t.middlewareData.shift,x=P,C=A;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(C=R),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(x=T),O&&!g){let e=p(d.left,0),t=p(d.right,0),r=p(d.top,0),n=p(d.bottom,0);y?C=b-2*(0!==e||0!==t?e+t:p(d.left,d.right)):x=v-2*(0!==r||0!==n?r+n:p(d.top,d.bottom))}await c({...t,availableWidth:C,availableHeight:x});let N=await l.getDimensions(u.floating);return b!==N.width||v!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=E(e,t);switch(n){case"referenceHidden":{let e=M(await I(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=M(await I(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),eC=(e,t)=>({...eT(e),options:[e,t]});var eN=r(14163),eI=o.forwardRef((e,t)=>{let{children:r,width:o=10,height:i=5,...a}=e;return(0,n.jsx)(eN.sG.svg,{...a,ref:t,width:o,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,n.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eI.displayName="Arrow";var eM=r(13495),ek=r(66156),eL="Popper",[eD,ej]=(0,s.A)(eL),[eH,eB]=eD(eL),eU=e=>{let{__scopePopper:t,children:r}=e,[i,a]=o.useState(null);return(0,n.jsx)(eH,{scope:t,anchor:i,onAnchorChange:a,children:r})};eU.displayName=eL;var eF="PopperAnchor",eG=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...s}=e,l=eB(eF,r),u=o.useRef(null),c=(0,a.s)(t,u);return o.useEffect(()=>{l.onAnchorChange(i?.current||u.current)}),i?null:(0,n.jsx)(eN.sG.div,{...s,ref:c})});eG.displayName=eF;var ez="PopperContent",[eV,e$]=eD(ez),eW=o.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:s=0,align:l="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:f=!0,collisionBoundary:d=[],collisionPadding:m=0,sticky:y="partial",hideWhenDetached:b=!1,updatePositionStrategy:v="optimized",onPlaced:E,..._}=e,w=eB(ez,r),[T,R]=o.useState(null),S=(0,a.s)(t,e=>R(e)),[P,A]=o.useState(null),O=function(e){let[t,r]=o.useState(void 0);return(0,ek.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(P),x=O?.width??0,C=O?.height??0,N="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},I=Array.isArray(d)?d:[d],M=I.length>0,k={padding:N,boundary:I.filter(eY),altBoundary:M},{refs:L,floatingStyles:D,placement:j,isPositioned:H,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:i,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[f,d]=o.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=o.useState(n);ev(h,n)||p(n);let[m,g]=o.useState(null),[y,b]=o.useState(null),v=o.useCallback(e=>{e!==T.current&&(T.current=e,g(e))},[]),E=o.useCallback(e=>{e!==R.current&&(R.current=e,b(e))},[]),_=a||m,w=s||y,T=o.useRef(null),R=o.useRef(null),S=o.useRef(f),P=null!=u,A=ew(u),O=ew(i),x=ew(c),C=o.useCallback(()=>{if(!T.current||!R.current)return;let e={placement:t,strategy:r,middleware:h};O.current&&(e.platform=O.current),eg(T.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==x.current};N.current&&!ev(S.current,t)&&(S.current=t,ey.flushSync(()=>{d(t)}))})},[h,t,r,O,x]);eb(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let N=o.useRef(!1);eb(()=>(N.current=!0,()=>{N.current=!1}),[]),eb(()=>{if(_&&(T.current=_),w&&(R.current=w),_&&w){if(A.current)return A.current(_,w,C);C()}},[_,w,C,A,P]);let I=o.useMemo(()=>({reference:T,floating:R,setReference:v,setFloating:E}),[v,E]),M=o.useMemo(()=>({reference:_,floating:w}),[_,w]),k=o.useMemo(()=>{let e={position:r,left:0,top:0};if(!M.floating)return e;let t=e_(M.floating,f.x),n=e_(M.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...eE(M.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,M.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:C,refs:I,elements:M,floatingStyles:k}),[f,C,I,M,k])}({strategy:"fixed",placement:i+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,c=et(e),f=i||a?[...c?J(c):[],...J(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),a&&e.addEventListener("resize",r)});let d=c&&l?function(e,t){let r,n=null,o=B(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),i();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:m}=u;if(s||t(),!d||!m)return;let y=g(f),b=g(o.clientWidth-(c+d)),v={rootMargin:-y+"px "+-b+"px "+-g(o.clientHeight-(f+m))+"px "+-g(c)+"px",threshold:p(0,h(1,l))||1},E=!0;function _(t){let n=t[0].intersectionRatio;if(n!==l){if(!E)return a();n?a(!1,n):r=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==n||ep(u,e.getBoundingClientRect())||a(),E=!1}try{n=new IntersectionObserver(_,{...v,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(_,v)}n.observe(e)}(!0),i}(c,r):null,m=-1,y=null;s&&(y=new ResizeObserver(e=>{let[n]=e;n&&n.target===c&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),r()}),c&&!u&&y.observe(c),y.observe(t));let b=u?ei(e):null;return u&&function t(){let n=ei(e);b&&!ep(b,n)&&r(),b=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",r),a&&e.removeEventListener("resize",r)}),null==d||d(),null==(e=y)||e.disconnect(),y=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:w.anchor},middleware:[eR({mainAxis:s+C,alignmentAxis:u}),f&&eS({mainAxis:!0,crossAxis:!1,limiter:"partial"===y?eP():void 0,...k}),f&&eA({...k}),eO({...k,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),P&&eC({element:P,padding:c}),eZ({arrowWidth:x,arrowHeight:C}),b&&ex({strategy:"referenceHidden",...k})]}),[F,G]=eJ(j),z=(0,eM.c)(E);(0,ek.N)(()=>{H&&z?.()},[H,z]);let V=U.arrow?.x,$=U.arrow?.y,W=U.arrow?.centerOffset!==0,[K,X]=o.useState();return(0,ek.N)(()=>{T&&X(window.getComputedStyle(T).zIndex)},[T]),(0,n.jsx)("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:H?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,n.jsx)(eV,{scope:r,placedSide:F,onArrowChange:A,arrowX:V,arrowY:$,shouldHideArrow:W,children:(0,n.jsx)(eN.sG.div,{"data-side":F,"data-align":G,..._,ref:S,style:{..._.style,animation:H?void 0:"none"}})})})});eW.displayName=ez;var eK="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eq=o.forwardRef(function(e,t){let{__scopePopper:r,...o}=e,i=e$(eK,r),a=eX[i.placedSide];return(0,n.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,n.jsx)(eI,{...o,ref:t,style:{...o.style,display:"block"}})})});function eY(e){return null!==e}eq.displayName=eK;var eZ=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[l,u]=eJ(r),c={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+s/2,h="",p="";return"bottom"===l?(h=i?c:`${f}px`,p=`${-s}px`):"top"===l?(h=i?c:`${f}px`,p=`${n.floating.height+s}px`):"right"===l?(h=`${-s}px`,p=i?c:`${d}px`):"left"===l&&(h=`${n.floating.width+s}px`,p=i?c:`${d}px`),{data:{x:h,y:p}}}});function eJ(e){let[t,r="center"]=e.split("-");return[t,r]}var eQ=r(25028),e0=r(46059),e1=r(8730),e2=r(65551),e3=r(63376),e6=r(11490),e8="Popover",[e5,e9]=(0,s.A)(e8,[ej]),e4=ej(),[e7,te]=e5(e8),tt=e=>{let{__scopePopover:t,children:r,open:i,defaultOpen:a,onOpenChange:s,modal:l=!1}=e,u=e4(t),c=o.useRef(null),[d,h]=o.useState(!1),[p,m]=(0,e2.i)({prop:i,defaultProp:a??!1,onChange:s,caller:e8});return(0,n.jsx)(eU,{...u,children:(0,n.jsx)(e7,{scope:t,contentId:(0,f.B)(),triggerRef:c,open:p,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:d,onCustomAnchorAdd:o.useCallback(()=>h(!0),[]),onCustomAnchorRemove:o.useCallback(()=>h(!1),[]),modal:l,children:r})})};tt.displayName=e8;var tr="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:r,...i}=e,a=te(tr,r),s=e4(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:u}=a;return o.useEffect(()=>(l(),()=>u()),[l,u]),(0,n.jsx)(eG,{...s,...i,ref:t})}).displayName=tr;var tn="PopoverTrigger",to=o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,s=te(tn,r),l=e4(r),u=(0,a.s)(t,s.triggerRef),c=(0,n.jsx)(eN.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":tg(s.open),...o,ref:u,onClick:(0,i.m)(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?c:(0,n.jsx)(eG,{asChild:!0,...l,children:c})});to.displayName=tn;var ti="PopoverPortal",[ta,ts]=e5(ti,{forceMount:void 0}),tl=e=>{let{__scopePopover:t,forceMount:r,children:o,container:i}=e,a=te(ti,t);return(0,n.jsx)(ta,{scope:t,forceMount:r,children:(0,n.jsx)(e0.C,{present:r||a.open,children:(0,n.jsx)(eQ.Z,{asChild:!0,container:i,children:o})})})};tl.displayName=ti;var tu="PopoverContent",tc=o.forwardRef((e,t)=>{let r=ts(tu,e.__scopePopover),{forceMount:o=r.forceMount,...i}=e,a=te(tu,e.__scopePopover);return(0,n.jsx)(e0.C,{present:o||a.open,children:a.modal?(0,n.jsx)(td,{...i,ref:t}):(0,n.jsx)(th,{...i,ref:t})})});tc.displayName=tu;var tf=(0,e1.TL)("PopoverContent.RemoveScroll"),td=o.forwardRef((e,t)=>{let r=te(tu,e.__scopePopover),s=o.useRef(null),l=(0,a.s)(t,s),u=o.useRef(!1);return o.useEffect(()=>{let e=s.current;if(e)return(0,e3.Eq)(e)},[]),(0,n.jsx)(e6.A,{as:tf,allowPinchZoom:!0,children:(0,n.jsx)(tp,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),u.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),th=o.forwardRef((e,t)=>{let r=te(tu,e.__scopePopover),i=o.useRef(!1),a=o.useRef(!1);return(0,n.jsx)(tp,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(i.current||r.triggerRef.current?.focus(),t.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),tp=o.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:s,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:p,...m}=e,g=te(tu,r),y=e4(r);return(0,u.Oh)(),(0,n.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,n.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:p,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:h,onDismiss:()=>g.onOpenChange(!1),children:(0,n.jsx)(eW,{"data-state":tg(g.open),role:"dialog",id:g.contentId,...y,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),tm="PopoverClose";function tg(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=te(tm,r);return(0,n.jsx)(eN.sG.button,{type:"button",...o,ref:t,onClick:(0,i.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=tm,o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,i=e4(r);return(0,n.jsx)(eq,{...i,...o,ref:t})}).displayName="PopoverArrow";var ty=r(82348);let tb=tt,tv=to,tE=o.forwardRef(({className:e,align:t="center",sideOffset:r=4,...o},i)=>(0,n.jsx)(tl,{children:(0,n.jsx)(tc,{ref:i,align:t,sideOffset:r,side:"bottom",className:(0,ty.QP)("z-50 min-w-[220px] max-w-[98vw] rounded-lg border bg-fd-popover p-2 text-sm text-fd-popover-foreground shadow-lg focus-visible:outline-none data-[state=closed]:animate-fd-popover-out data-[state=open]:animate-fd-popover-in",e),...o})}));tE.displayName=tc.displayName},74722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let n=r(85531),o=r(35499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},75076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return i},prefetchReducer:function(){return a}});let n=r(5144),o=r(5334),i=new n.PromiseQueue(5),a=function(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,o.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},76759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return i}});let n=r(42785),o=r(23736);function i(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},77022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(43210),o=r(51215),i="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(i)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(i);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(i)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,o.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77728:(e,t,r)=>{"use strict";r.d(t,{DO:()=>o,Fc:()=>i,JK:()=>a});let n={arabic:"ar",armenian:"am",bulgarian:"bg",czech:"cz",danish:"dk",dutch:"nl",english:"en",finnish:"fi",french:"fr",german:"de",greek:"gr",hungarian:"hu",indian:"in",indonesian:"id",irish:"ie",italian:"it",lithuanian:"lt",nepali:"np",norwegian:"no",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"rs",slovenian:"ru",spanish:"es",swedish:"se",tamil:"ta",turkish:"tr",ukrainian:"uk",sanskrit:"sk"},o={dutch:/[^A-Za-zàèéìòóù0-9_'-]+/gim,english:/[^A-Za-zàèéìòóù0-9_'-]+/gim,french:/[^a-z0-9äâàéèëêïîöôùüûœç-]+/gim,italian:/[^A-Za-zàèéìòóù0-9_'-]+/gim,norwegian:/[^a-z0-9_æøåÆØÅäÄöÖüÜ]+/gim,portuguese:/[^a-z0-9à-úÀ-Ú]/gim,russian:/[^a-z0-9а-яА-ЯёЁ]+/gim,spanish:/[^a-z0-9A-Zá-úÁ-ÚñÑüÜ]+/gim,swedish:/[^a-z0-9_åÅäÄöÖüÜ-]+/gim,german:/[^a-z0-9A-ZäöüÄÖÜß]+/gim,finnish:/[^a-z0-9äöÄÖ]+/gim,danish:/[^a-z0-9æøåÆØÅ]+/gim,hungarian:/[^a-z0-9áéíóöőúüűÁÉÍÓÖŐÚÜŰ]+/gim,romanian:/[^a-z0-9ăâîșțĂÂÎȘȚ]+/gim,serbian:/[^a-z0-9čćžšđČĆŽŠĐ]+/gim,turkish:/[^a-z0-9çÇğĞıİöÖşŞüÜ]+/gim,lithuanian:/[^a-z0-9ąčęėįšųūžĄČĘĖĮŠŲŪŽ]+/gim,arabic:/[^a-z0-9أ-ي]+/gim,nepali:/[^a-z0-9अ-ह]+/gim,irish:/[^a-z0-9áéíóúÁÉÍÓÚ]+/gim,indian:/[^a-z0-9अ-ह]+/gim,armenian:/[^a-z0-9ա-ֆ]+/gim,greek:/[^a-z0-9α-ωά-ώ]+/gim,indonesian:/[^a-z0-9]+/gim,ukrainian:/[^a-z0-9а-яА-ЯіїєІЇЄ]+/gim,slovenian:/[^a-z0-9čžšČŽŠ]+/gim,bulgarian:/[^a-z0-9а-яА-Я]+/gim,tamil:/[^a-z0-9அ-ஹ]+/gim,sanskrit:/[^a-z0-9A-Zāīūṛḷṃṁḥśṣṭḍṇṅñḻḹṝ]+/gim,czech:/[^A-Z0-9a-zěščřžýáíéúůóťďĚŠČŘŽÝÁÍÉÓÚŮŤĎ-]+/gim},i=Object.keys(n);function a(e){return void 0!==e&&i.includes(e)?n[e]:void 0}},78034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(44827);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=o[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>i(e)):a[e]=i(r))}return a}}},78866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(59008),o=r(57391),i=r(86770),a=r(2030),s=r(25232),l=r(59435),u=r(41500),c=r(89752),f=r(96493),d=r(68214),h=r(22308);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),b=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,n.fetchServerResponse)(new URL(m,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:b?e.nextUrl:null});let v=Date.now();return y.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){let{tree:n,seedData:l,head:d,isRootRender:E}=r;if(!E)return console.log("REFRESH FAILED"),e;let _=(0,i.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===_)return(0,f.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(g,_))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let w=c?(0,o.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=w),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(v,y,void 0,n,l,d,void 0),p.prefetchCache=new Map}await (0,h.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:_,updatedCache:y,includeNextUrl:b,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=_,g=_}return(0,l.handleMutable)(e,p)},()=>e)}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78941:(e,t,r)=>{"use strict";r.d(t,{LanguageToggle:()=>o,LanguageToggleText:()=>i});var n=r(12907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call LanguageToggle() from the server but LanguageToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\language-toggle.js","LanguageToggle"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call LanguageToggleText() from the server but LanguageToggleText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\components\\layout\\language-toggle.js","LanguageToggleText")},79167:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return g}});let n=r(14985),o=r(40740),i=r(60687),a=n._(r(51215)),s=o._(r(43210)),l=r(89513),u=r(7448),c=r(40932),f=new Map,d=new Set,h=e=>{if(a.default.preinit)return void e.forEach(e=>{a.default.preinit(e,{as:"style"})})},p=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:o=null,dangerouslySetInnerHTML:i,children:a="",strategy:s="afterInteractive",onError:l,stylesheets:c}=e,p=r||t;if(p&&d.has(p))return;if(f.has(t)){d.add(p),f.get(t).then(n,l);return}let m=()=>{o&&o(),d.add(p)},g=document.createElement("script"),y=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});i?(g.innerHTML=i.__html||"",m()):a?(g.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",m()):t&&(g.src=t,f.set(t,y)),(0,u.setAttributesFromProps)(g,e),"worker"===s&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",s),c&&h(c),document.body.appendChild(g)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}):p(e)}function g(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");d.add(t)})}function y(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:o=null,strategy:u="afterInteractive",onError:f,stylesheets:h,...m}=e,{updateScripts:g,scripts:y,getIsSsr:b,appDir:v,nonce:E}=(0,s.useContext)(l.HeadManagerContext),_=(0,s.useRef)(!1);(0,s.useEffect)(()=>{let e=t||r;_.current||(o&&e&&d.has(e)&&o(),_.current=!0)},[o,t,r]);let w=(0,s.useRef)(!1);if((0,s.useEffect)(()=>{if(!w.current){if("afterInteractive"===u)p(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}));w.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(y[u]=(y[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:o,onError:f,...m}]),g(y)):b&&b()?d.add(t||r):b&&!b()&&p(e)),v){if(h&&h.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,i.jsx)("script",{nonce:E,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return a.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:E,crossOrigin:m.crossOrigin}:{as:"script",nonce:E,crossOrigin:m.crossOrigin}),(0,i.jsx)("script",{nonce:E,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===u&&r&&a.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:E,crossOrigin:m.crossOrigin}:{as:"script",nonce:E,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let b=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return u},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,h=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},79667:(e,t,r)=>{"use strict";r.d(t,{L:()=>c,TreeContextProvider:()=>u,t:()=>f});var n=r(60687),o=r(66218),i=r(43210),a=r(96768);let s=(0,o.q6)("TreeContext"),l=(0,o.q6)("PathContext",[]);function u(e){let t=(0,i.useRef)(0),r=(0,o.a8)(),u=(0,i.useMemo)(()=>e.tree,[e.tree.$id??e.tree]),c=(0,i.useMemo)(()=>(0,a.oe)(u.children,r)??[],[u,r]),f=c.findLast(e=>"folder"===e.type&&e.root)??u;return f.$id??(f.$id=String(t.current++)),(0,n.jsx)(s.Provider,{value:(0,i.useMemo)(()=>({root:f}),[f]),children:(0,n.jsx)(l.Provider,{value:c,children:e.children})})}function c(){return l.use()}function f(){return s.use("You must wrap this component under <DocsLayout />")}},79943:(e,t,r)=>{"use strict";r.d(t,{$A:()=>a,YL:()=>l});var n=r(60687),o=r(43210);let i=(0,r(66218).q6)("SearchContext",{enabled:!1,hotKey:[],setOpenSearch:()=>void 0});function a(){return i.use()}function s(){let[e,t]=(0,o.useState)("⌘");return e}function l({SearchDialog:e,children:t,preload:r=!0,options:a,hotKey:l=[{key:e=>e.metaKey||e.ctrlKey,display:(0,n.jsx)(s,{})},{key:"k",display:"K"}],links:u}){let[c,f]=(0,o.useState)(!r&&void 0);return(0,n.jsxs)(i.Provider,{value:(0,o.useMemo)(()=>({enabled:!0,hotKey:l,setOpenSearch:f}),[l]),children:[void 0!==c&&(0,n.jsx)(e,{open:c,onOpenChange:f,links:u,...a}),t]})}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\production\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\next-intl\\dist\\esm\\production\\shared\\NextIntlClientProvider.js","default")},82080:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},82348:(e,t,r)=>{"use strict";r.d(t,{QP:()=>eu});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,a=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)l(r[e],n,e,t);return n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void l(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{l(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},d=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,o=0,i=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===n&&0===o){if(":"===s){r.push(e.slice(i,a)),i=a+1;continue}if("/"===s){t=a;continue}}"["===s?n++:"]"===s?n--:"("===s?o++:")"===s&&o--}let a=0===r.length?e:e.substring(i),s=h(a);return{modifiers:r,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},m=e=>({cache:f(e.cacheSize),parseClassName:d(e),sortModifiers:p(e),...n(e)}),g=/\s+/,y=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=t,a=[],s=e.trim().split(g),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:u,modifiers:c,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:h}=r(t);if(u){l=t+(l.length>0?" "+l:l);continue}let p=!!h,m=n(p?d.substring(0,h):d);if(!m){if(!p||!(m=n(d))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=i(c).join(":"),y=f?g+"!":g,b=y+m;if(a.includes(b))continue;a.push(b);let v=o(m,p);for(let e=0;e<v.length;++e){let t=v[e];a.push(y+t)}l=t+(l.length>0?" "+l:l)}return l};function b(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=v(e))&&(n&&(n+=" "),n+=t);return n}let v=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=v(e[n]))&&(r&&(r+=" "),r+=t);return r},E=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},_=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,w=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>T.test(e),C=e=>!!e&&!Number.isNaN(Number(e)),N=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&C(e.slice(0,-1)),M=e=>R.test(e),k=()=>!0,L=e=>S.test(e)&&!P.test(e),D=()=>!1,j=e=>A.test(e),H=e=>O.test(e),B=e=>!F(e)&&!K(e),U=e=>ee(e,eo,D),F=e=>_.test(e),G=e=>ee(e,ei,L),z=e=>ee(e,ea,C),V=e=>ee(e,er,D),$=e=>ee(e,en,H),W=e=>ee(e,el,j),K=e=>w.test(e),X=e=>et(e,ei),q=e=>et(e,es),Y=e=>et(e,er),Z=e=>et(e,eo),J=e=>et(e,en),Q=e=>et(e,el,!0),ee=(e,t,r)=>{let n=_.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=w.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ei=e=>"length"===e,ea=e=>"number"===e,es=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,n,o,i=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=a,a(s)};function a(e){let t=n(e);if(t)return t;let i=y(e,r);return o(e,i),i}return function(){return i(b.apply(null,arguments))}}(()=>{let e=E("color"),t=E("font"),r=E("text"),n=E("font-weight"),o=E("tracking"),i=E("leading"),a=E("breakpoint"),s=E("container"),l=E("spacing"),u=E("radius"),c=E("shadow"),f=E("inset-shadow"),d=E("text-shadow"),h=E("drop-shadow"),p=E("blur"),m=E("perspective"),g=E("aspect"),y=E("ease"),b=E("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[..._(),K,F],T=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto","contain","none"],S=()=>[K,F,l],P=()=>[x,"full","auto",...S()],A=()=>[N,"none","subgrid",K,F],O=()=>["auto",{span:["full",N,K,F]},N,K,F],L=()=>[N,"auto",K,F],D=()=>["auto","min","max","fr",K,F],j=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],H=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...S()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...S()],er=()=>[e,K,F],en=()=>[..._(),Y,V,{position:[K,F]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",Z,U,{size:[K,F]}],ea=()=>[I,X,G],es=()=>["","none","full",u,K,F],el=()=>["",C,X,G],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ef=()=>[C,I,Y,V],ed=()=>["","none",p,K,F],eh=()=>["none",C,K,F],ep=()=>["none",C,K,F],em=()=>[C,K,F],eg=()=>[x,"full",...S()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[M],breakpoint:[M],color:[k],container:[M],"drop-shadow":[M],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[M],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[M],shadow:[M],spacing:["px",C],text:[M],"text-shadow":[M],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,F,K,g]}],container:["container"],columns:[{columns:[C,F,K,s]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[N,"auto",K,F]}],basis:[{basis:[x,"full","auto",s,...S()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,x,"auto","initial","none",F]}],grow:[{grow:["",C,K,F]}],shrink:[{shrink:["",C,K,F]}],order:[{order:[N,"first","last","none",K,F]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:O()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:O()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:S()}],"gap-x":[{"gap-x":S()}],"gap-y":[{"gap-y":S()}],"justify-content":[{justify:[...j(),"normal"]}],"justify-items":[{"justify-items":[...H(),"normal"]}],"justify-self":[{"justify-self":["auto",...H()]}],"align-content":[{content:["normal",...j()]}],"align-items":[{items:[...H(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...H(),{baseline:["","last"]}]}],"place-content":[{"place-content":j()}],"place-items":[{"place-items":[...H(),"baseline"]}],"place-self":[{"place-self":["auto",...H()]}],p:[{p:S()}],px:[{px:S()}],py:[{py:S()}],ps:[{ps:S()}],pe:[{pe:S()}],pt:[{pt:S()}],pr:[{pr:S()}],pb:[{pb:S()}],pl:[{pl:S()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen",...et()]}],"min-h":[{"min-h":["screen","none",...et()]}],"max-h":[{"max-h":["screen",...et()]}],"font-size":[{text:["base",r,X,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,K,z]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,F]}],"font-family":[{font:[q,F,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,K,F]}],"line-clamp":[{"line-clamp":[C,"none",K,z]}],leading:[{leading:[i,...S()]}],"list-image":[{"list-image":["none",K,F]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",K,F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",K,G]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[C,"auto",K,F]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K,F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K,F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},N,K,F],radial:["",K,F],conic:[N,K,F]},J,$]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,K,F]}],"outline-w":[{outline:["",C,X,G]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,Q,W]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",f,Q,W]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[C,G]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",d,Q,W]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[C,K,F]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":ef()}],"mask-image-linear-to-pos":[{"mask-linear-to":ef()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ef()}],"mask-image-t-to-pos":[{"mask-t-to":ef()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ef()}],"mask-image-r-to-pos":[{"mask-r-to":ef()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ef()}],"mask-image-b-to-pos":[{"mask-b-to":ef()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ef()}],"mask-image-l-to-pos":[{"mask-l-to":ef()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ef()}],"mask-image-x-to-pos":[{"mask-x-to":ef()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ef()}],"mask-image-y-to-pos":[{"mask-y-to":ef()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[K,F]}],"mask-image-radial-from-pos":[{"mask-radial-from":ef()}],"mask-image-radial-to-pos":[{"mask-radial-to":ef()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":_()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":ef()}],"mask-image-conic-to-pos":[{"mask-conic-to":ef()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",K,F]}],filter:[{filter:["","none",K,F]}],blur:[{blur:ed()}],brightness:[{brightness:[C,K,F]}],contrast:[{contrast:[C,K,F]}],"drop-shadow":[{"drop-shadow":["","none",h,Q,W]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",C,K,F]}],"hue-rotate":[{"hue-rotate":[C,K,F]}],invert:[{invert:["",C,K,F]}],saturate:[{saturate:[C,K,F]}],sepia:[{sepia:["",C,K,F]}],"backdrop-filter":[{"backdrop-filter":["","none",K,F]}],"backdrop-blur":[{"backdrop-blur":ed()}],"backdrop-brightness":[{"backdrop-brightness":[C,K,F]}],"backdrop-contrast":[{"backdrop-contrast":[C,K,F]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,K,F]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,K,F]}],"backdrop-invert":[{"backdrop-invert":["",C,K,F]}],"backdrop-opacity":[{"backdrop-opacity":[C,K,F]}],"backdrop-saturate":[{"backdrop-saturate":[C,K,F]}],"backdrop-sepia":[{"backdrop-sepia":["",C,K,F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",K,F]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",K,F]}],ease:[{ease:["linear","initial",y,K,F]}],delay:[{delay:[C,K,F]}],animate:[{animate:["none",b,K,F]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,K,F]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:eh()}],"rotate-x":[{"rotate-x":eh()}],"rotate-y":[{"rotate-y":eh()}],"rotate-z":[{"rotate-z":eh()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[K,F,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K,F]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K,F]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[C,X,G,z]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},83930:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(61120),o=r(67424);let i=(0,n.cache)(async function(e){var t=await (0,o.A)(e);if(!t.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return t.messages});async function a(e){return i(e?.locale)}},84604:(e,t,r)=>{"use strict";r.d(t,{b:()=>eS,c:()=>eC,d:()=>ew,e:()=>eA,f:()=>eE,g:()=>e_,i:()=>eN,r:()=>eO});var n,o,i,a,s,l,u,c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function f(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return(d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function h(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function p(e,t){var r=t&&t.cache?t.cache:v,n=t&&t.serializer?t.serializer:y;return(t&&t.strategy?t.strategy:function(e,t){var r,n,o=1===e.length?m:g;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function m(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function g(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}Object.create,"function"==typeof SuppressedError&&SuppressedError;var y=function(){return JSON.stringify(arguments)},b=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),v={create:function(){return new b}},E={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,g.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,m.bind(this,e,r,n)}};function _(e){return e.type===o.literal}function w(e){return e.type===o.number}function T(e){return e.type===o.date}function R(e){return e.type===o.time}function S(e){return e.type===o.select}function P(e){return e.type===o.plural}function A(e){return e.type===o.tag}function O(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function x(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var C=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,N=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,I=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,M=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,k=/^(@+)?(\+|#+)?[rs]?$/g,L=/(\*)(0+)|(#+)(0+)|(0+)/g,D=/^(0+)$/;function j(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(k,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function H(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function B(e){var t=H(e);return t||{}}var U={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},F=new RegExp("^".concat(C.source,"*")),G=new RegExp("".concat(C.source,"*$"));function z(e,t){return{start:e,end:t}}var V=!!String.prototype.startsWith&&"_a".startsWith("a",1),$=!!String.fromCodePoint,W=!!Object.fromEntries,K=!!String.prototype.codePointAt,X=!!String.prototype.trimStart,q=!!String.prototype.trimEnd,Y=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},Z=!0;try{Z=(null==(a=eo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){Z=!1}var J=V?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},Q=$?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},ee=W?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},et=K?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},er=X?function(e){return e.trimStart()}:function(e){return e.replace(F,"")},en=q?function(e){return e.trimEnd()}:function(e){return e.replace(G,"")};function eo(e,t){return new RegExp(e,t)}if(Z){var ei=eo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return ei.lastIndex=t,null!=(r=ei.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,o=et(e,t);if(void 0===o||el(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return Q.apply(void 0,r)};var ea=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var l=this.clonePosition();this.bump(),i.push({type:o.pound,location:z(l,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&es(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,z(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:z(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,z(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,l=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,z(r,this.clonePosition()));if(this.isEOF()||!es(this.char()))return this.error(n.INVALID_TAG,z(l,this.clonePosition()));var u=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,z(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:z(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,z(l,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var l=z(r,this.clonePosition());return{val:{type:o.literal,value:n,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(es(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return Q.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),Q(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,z(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,z(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,z(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,z(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:z(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,z(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,z(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:z(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,z(l,c));case"number":case"date":case"time":this.bumpSpace();var f=null;if(this.bumpIf(",")){this.bumpSpace();var h=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=en(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,z(this.clonePosition(),this.clonePosition()));f={style:m,styleLocation:z(h,this.clonePosition())}}var g=this.tryParseArgumentClose(a);if(g.err)return g;var y=z(a,this.clonePosition());if(f&&J(null==f?void 0:f.style,"::",0)){var b=er(f.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(b,f.styleLocation);if(p.err)return p;return{val:{type:o.number,value:r,location:y,style:p.val},err:null}}if(0===b.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,y);var v,E=b;this.locale&&(E=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),l=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(U[t||""]||U[n||""]||U["".concat(n,"-001")]||U["001"])[0]}(t);for(("H"==l||"k"==l)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=l+r}else"J"===o?r+="H":r+=o}return r}(b,this.locale));var m={type:i.dateTime,pattern:E,location:f.styleLocation,parsedOptions:this.shouldParseSkeletons?(v={},E.replace(N,function(e){var t=e.length;switch(e[0]){case"G":v.era=4===t?"long":5===t?"narrow":"short";break;case"y":v.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":v.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":v.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":v.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");v.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");v.weekday=["short","long","narrow","short"][t-4];break;case"a":v.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":v.hourCycle="h12",v.hour=["numeric","2-digit"][t-1];break;case"H":v.hourCycle="h23",v.hour=["numeric","2-digit"][t-1];break;case"K":v.hourCycle="h11",v.hour=["numeric","2-digit"][t-1];break;case"k":v.hourCycle="h24",v.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":v.minute=["numeric","2-digit"][t-1];break;case"s":v.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":v.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),v):{}};return{val:{type:"date"===u?o.date:o.time,value:r,location:y,style:m},err:null}}return{val:{type:"number"===u?o.number:"date"===u?o.date:o.time,value:r,location:y,style:null!=(s=null==f?void 0:f.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,z(_,d({},_)));this.bumpSpace();var w=this.parseIdentifierIfPossible(),T=0;if("select"!==u&&"offset"===w.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,z(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),w=this.parseIdentifierIfPossible(),T=p.val}var R=this.tryParsePluralOrSelectOptions(e,u,t,w);if(R.err)return R;var g=this.tryParseArgumentClose(a);if(g.err)return g;var S=z(a,this.clonePosition());if("select"===u)return{val:{type:o.select,value:r,options:ee(R.val),location:S},err:null};return{val:{type:o.plural,value:r,options:ee(R.val),offset:T,pluralType:"plural"===u?"cardinal":"ordinal",location:S},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,z(l,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,z(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,z(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(I).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=d(d(d({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return d(d({},e),B(t))},{}));continue;case"engineering":t=d(d(d({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return d(d({},e),B(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(L,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(D.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(M.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(M,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=d(d({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=d(d({},t),j(o)));continue}if(k.test(n.stem)){t=d(d({},t),j(n.stem));continue}var i=H(n.stem);i&&(t=d(d({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!D.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=d(d({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],l=new Set,u=o.value,c=o.location;;){if(0===u.length){var f=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(d.err)return d;c=z(f,this.clonePosition()),u=this.message.slice(f.offset,this.offset())}else break}if(l.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===u&&(a=!0),this.bumpSpace();var h=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,z(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(h);if(m.err)return m;s.push([u,{value:p.val,location:z(h,this.clonePosition())}]),l.add(u),this.bumpSpace(),u=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,z(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,z(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=z(n,this.clonePosition());return o?Y(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=et(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(J(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&el(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function es(e){return e>=97&&e<=122||e>=65&&e<=90}function el(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function eu(e,t){void 0===t&&(t={});var r=new ea(e,t=d({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,S(t)||P(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else w(t)&&O(t.style)||(T(t)||R(t))&&x(t.style)?delete t.style.location:A(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(l||(l={}));var ec=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return f(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ef=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),l.INVALID_VALUE,o)||this}return f(t,e),t}(ec),ed=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),l.INVALID_VALUE,n)||this}return f(t,e),t}(ec),eh=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),l.MISSING_VALUE,r)||this}return f(t,e),t}(ec);function ep(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var em=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var a,s,c=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=c.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,i,a,s,c){if(1===t.length&&_(t[0]))return[{type:u.literal,value:t[0].value}];for(var f=[],d=0;d<t.length;d++){var h=t[d];if(_(h)){f.push({type:u.literal,value:h.value});continue}if(h.type===o.pound){"number"==typeof s&&f.push({type:u.literal,value:n.getNumberFormat(r).format(s)});continue}var p=h.value;if(!(a&&p in a))throw new eh(p,c);var m=a[p];if(h.type===o.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),f.push({type:"string"==typeof m?u.literal:u.object,value:m});continue}if(T(h)){var g="string"==typeof h.style?i.date[h.style]:x(h.style)?h.style.parsedOptions:void 0;f.push({type:u.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(R(h)){var g="string"==typeof h.style?i.time[h.style]:x(h.style)?h.style.parsedOptions:i.time.medium;f.push({type:u.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(w(h)){var g="string"==typeof h.style?i.number[h.style]:O(h.style)?h.style.parsedOptions:void 0;g&&g.scale&&(m*=g.scale||1),f.push({type:u.literal,value:n.getNumberFormat(r,g).format(m)});continue}if(A(h)){var y=h.children,b=h.value,v=a[b];if("function"!=typeof v)throw new ed(b,"function",c);var E=v(e(y,r,n,i,a,s).map(function(e){return e.value}));Array.isArray(E)||(E=[E]),f.push.apply(f,E.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(S(h)){var C=h.options[m]||h.options.other;if(!C)throw new ef(h.value,m,Object.keys(h.options),c);f.push.apply(f,e(C.value,r,n,i,a));continue}if(P(h)){var C=h.options["=".concat(m)];if(!C){if(!Intl.PluralRules)throw new ec('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',l.MISSING_INTL_API,c);var N=n.getPluralRules(r,{type:h.pluralType}).select(m-(h.offset||0));C=h.options[N]||h.options.other}if(!C)throw new ef(h.value,m,Object.keys(h.options),c);f.push.apply(f,e(C.value,r,n,i,a,m-(h.offset||0)));continue}}return f.length<2?f:f.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}(c.ast,c.locales,c.formatters,c.formats,e,void 0,c.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=c.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(c.locales)[0]}},this.getAst=function(){return c.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var f=i||{},m=(f.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(f,["formatters"]));this.ast=e.__parse(t,d(d({},m),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(a=e.formats,n?Object.keys(a).reduce(function(e,t){var r,o;return e[t]=(r=a[t],(o=n[t])?d(d(d({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=d(d({},r[t]),o[t]||{}),e},{})):r),e},d({},a)):a),this.formatters=i&&i.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:p(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,h([void 0],t,!1)))},{cache:ep(s.number),strategy:E.variadic}),getDateTimeFormat:p(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,h([void 0],t,!1)))},{cache:ep(s.dateTime),strategy:E.variadic}),getPluralRules:p(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,h([void 0],t,!1)))},{cache:ep(s.pluralRules),strategy:E.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=eu,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),eg=r(61120);class ey extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var eb=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(eb||{});function ev(...e){return e.filter(Boolean).join(".")}function eE(e){return ev(e.namespace,e.key)}function e_(e){console.error(e)}function ew(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eT(e,t){return p(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:E.variadic})}function eR(e,t){return eT((...t)=>new e(...t),t)}function eS(e){return{getDateTimeFormat:eR(Intl.DateTimeFormat,e.dateTime),getNumberFormat:eR(Intl.NumberFormat,e.number),getPluralRules:eR(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:eR(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:eR(Intl.ListFormat,e.list),getDisplayNames:eR(Intl.DisplayNames,e.displayNames)}}function eP(e,t,r,n){let o=ev(n,r);if(!t)throw Error(o);let i=t;return r.split(".").forEach(t=>{let r=i[t];if(null==t||null==r)throw Error(o+` (${e})`);i=r}),i}function eA(e){let t=function(e,t,r,n=e_){try{if(!t)throw Error(void 0);let n=r?eP(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new ey(eb.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=eE,locale:o,messagesOrError:i,namespace:a,onError:s,timeZone:l}){let u=i instanceof ey;function c(e,t,r){let o=new ey(t,r);return s(o),n({error:o,key:e,namespace:a})}function f(s,f,d){var h;let p,m;if(u)return n({error:i,key:s,namespace:a});try{p=eP(o,i,s,a)}catch(e){return c(s,eb.MISSING_MESSAGE,e.message)}if("object"==typeof p){let e;return c(s,Array.isArray(p)?eb.INVALID_MESSAGE:eb.INSUFFICIENT_PATH,e)}let g=(h=p,f?void 0:h);if(g)return g;r.getMessageFormat||(r.getMessageFormat=eT((...e)=>new em(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{m=r.getMessageFormat(p,o,function(e,t,r){let n=em.formats.date,o=em.formats.time,i={...e?.dateTime,...t?.dateTime},a={date:{...n,...i},time:{...o,...i},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=a[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),a}(t,d,l),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:l,...t})}})}catch(e){return c(s,eb.INVALID_MESSAGE,e.message)}try{let e=m.format(f?function(e){let t={};return Object.keys(e).forEach(r=>{let n,o=0,i=e[r];n="function"==typeof i?e=>{let t=i(e);return(0,eg.isValidElement)(t)?(0,eg.cloneElement)(t,{key:r+o++}):t}:i,t[r]=n}),t}(f):f);if(null==e)throw Error(void 0);return(0,eg.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return c(s,eb.FORMATTING_ERROR,e.message)}}function d(e,t,r){let n=f(e,t,r);return"string"!=typeof n?c(e,eb.INVALID_MESSAGE,void 0):n}return d.rich=f,d.markup=(e,t,r)=>f(e,t,r),d.raw=e=>{if(u)return n({error:i,key:e,namespace:a});try{return eP(o,i,e,a)}catch(t){return c(e,eb.MISSING_MESSAGE,t.message)}},d.has=e=>{if(u)return!1;try{return eP(o,i,e,a),!0}catch{return!1}},d}({...e,messagesOrError:t})}function eO(e,t){return e===t?void 0:e.slice((t+".").length)}let ex={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:2628e3,months:2628e3,quarter:7884e3,quarters:7884e3,year:31536e3,years:31536e3};function eC(e){let{_cache:t=ew(),_formatters:r=eS(t),formats:n,locale:o,onError:i=e_,timeZone:a}=e;function s(e){return e?.timeZone||(a?e={...e,timeZone:a}:i(new ey(eb.ENVIRONMENT_FALLBACK,void 0))),e}function l(e,t,r,n,o){let a;try{a=function(e,t,r){let n;if("string"==typeof t){if(!(n=e?.[t])){let e=new ey(eb.MISSING_FORMAT,void 0);throw i(e),e}}else n=t;return r&&(n={...n,...r}),n}(r,e,t)}catch{return o()}try{return n(a)}catch(e){return i(new ey(eb.FORMATTING_ERROR,e.message)),o()}}function u(e,t,i){return l(t,i,n?.dateTime,t=>(t=s(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function c(){return e.now?e.now:(i(new ey(eb.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:u,number:function(e,t,i){return l(t,i,n?.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):c(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=c());let l=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<2628e3?"week":t<31536e3?"month":"year"}(l)),s.numeric="second"===a?"auto":"always";let u=(n=a,Math.round(l/ex[n]));return r.getRelativeTimeFormat(o,s).format(u,a)}catch(t){return i(new ey(eb.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t,i){let a=[],s=new Map,u=0;for(let t of e){let e;"object"==typeof t?(e=String(u),s.set(e,t)):e=String(t),a.push(e),u++}return l(t,i,n?.list,e=>{let t=r.getListFormat(o,e).formatToParts(a).map(e=>"literal"===e.type?e.value:s.get(e.value)||e.value);return s.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i,a){return l(i,a,n?.dateTime,n=>(n=s(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[u(e),u(t)].join(" – "))}}}function eN({formats:e,getMessageFallback:t,messages:r,onError:n,...o}){return{...o,formats:e||void 0,messages:r||void 0,onError:n||e_,getMessageFallback:t||eE}}},84949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},85531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},85665:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});var n=r(77728),o=r(69593);let i=n.Fc.join("\n - "),a={NO_LANGUAGE_WITH_CUSTOM_TOKENIZER:"Do not pass the language option to create when using a custom tokenizer.",LANGUAGE_NOT_SUPPORTED:`Language "%s" is not supported.
Supported languages are:
 - ${i}`,INVALID_STEMMER_FUNCTION_TYPE:"config.stemmer property must be a function.",MISSING_STEMMER:'As of version 1.0.0 @orama/orama does not ship non English stemmers by default. To solve this, please explicitly import and specify the "%s" stemmer from the package @orama/stemmers. See https://docs.orama.com/open-source/text-analysis/stemming for more information.',CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY:"Custom stop words array must only contain strings.",UNSUPPORTED_COMPONENT:'Unsupported component "%s".',COMPONENT_MUST_BE_FUNCTION:'The component "%s" must be a function.',COMPONENT_MUST_BE_FUNCTION_OR_ARRAY_FUNCTIONS:'The component "%s" must be a function or an array of functions.',INVALID_SCHEMA_TYPE:'Unsupported schema type "%s" at "%s". Expected "string", "boolean" or "number" or array of them.',DOCUMENT_ID_MUST_BE_STRING:'Document id must be of type "string". Got "%s" instead.',DOCUMENT_ALREADY_EXISTS:'A document with id "%s" already exists.',DOCUMENT_DOES_NOT_EXIST:'A document with id "%s" does not exists.',MISSING_DOCUMENT_PROPERTY:'Missing searchable property "%s".',INVALID_DOCUMENT_PROPERTY:'Invalid document property "%s": expected "%s", got "%s"',UNKNOWN_INDEX:'Invalid property name "%s". Expected a wildcard string ("*") or array containing one of the following properties: %s',INVALID_BOOST_VALUE:"Boost value must be a number greater than, or less than 0.",INVALID_FILTER_OPERATION:"You can only use one operation per filter, you requested %d.",SCHEMA_VALIDATION_FAILURE:'Cannot insert document due schema validation failure on "%s" property.',INVALID_SORT_SCHEMA_TYPE:'Unsupported sort schema type "%s" at "%s". Expected "string" or "number".',CANNOT_SORT_BY_ARRAY:'Cannot configure sort for "%s" because it is an array (%s).',UNABLE_TO_SORT_ON_UNKNOWN_FIELD:'Unable to sort on unknown field "%s". Allowed fields: %s',SORT_DISABLED:"Sort is disabled. Please read the documentation at https://docs.oramasearch for more information.",UNKNOWN_GROUP_BY_PROPERTY:'Unknown groupBy property "%s".',INVALID_GROUP_BY_PROPERTY:'Invalid groupBy property "%s". Allowed types: "%s", but given "%s".',UNKNOWN_FILTER_PROPERTY:'Unknown filter property "%s".',INVALID_VECTOR_SIZE:'Vector size must be a number greater than 0. Got "%s" instead.',INVALID_VECTOR_VALUE:'Vector value must be a number greater than 0. Got "%s" instead.',INVALID_INPUT_VECTOR:`Property "%s" was declared as a %s-dimensional vector, but got a %s-dimensional vector instead.
Input vectors must be of the size declared in the schema, as calculating similarity between vectors of different sizes can lead to unexpected results.`,WRONG_SEARCH_PROPERTY_TYPE:'Property "%s" is not searchable. Only "string" properties are searchable.',FACET_NOT_SUPPORTED:'Facet doens\'t support the type "%s".',INVALID_DISTANCE_SUFFIX:'Invalid distance suffix "%s". Valid suffixes are: cm, m, km, mi, yd, ft.',INVALID_SEARCH_MODE:'Invalid search mode "%s". Valid modes are: "fulltext", "vector", "hybrid".',MISSING_VECTOR_AND_SECURE_PROXY:"No vector was provided and no secure proxy was configured. Please provide a vector or configure an Orama Secure Proxy to perform hybrid search.",MISSING_TERM:'"term" is a required parameter when performing hybrid search. Please provide a search term.',INVALID_VECTOR_INPUT:'Invalid "vector" property. Expected an object with "value" and "property" properties, but got "%s" instead.',PLUGIN_CRASHED:"A plugin crashed during initialization. Please check the error message for more information:",PLUGIN_SECURE_PROXY_NOT_FOUND:`Could not find '@orama/secure-proxy-plugin' installed in your Orama instance.
Please install it before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,PLUGIN_SECURE_PROXY_MISSING_CHAT_MODEL:`Could not find a chat model defined in the secure proxy plugin configuration.
Please provide a chat model before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,ANSWER_SESSION_LAST_MESSAGE_IS_NOT_ASSISTANT:"The last message in the session is not an assistant message. Cannot regenerate non-assistant messages.",PLUGIN_COMPONENT_CONFLICT:'The component "%s" is already defined. The plugin "%s" is trying to redefine it.'};function s(e,...t){let r=Error((0,o.nv)(a[e]??`Unsupported Orama Error code: ${e}`,...t));return r.code=e,"captureStackTrace"in Error.prototype&&Error.captureStackTrace(r),r}},85752:(e,t,r)=>{"use strict";r.d(t,{CY:()=>u,NI:()=>c,Q0:()=>f});var n=r(37413),o=r(61120);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){var a;let e,s,l=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(u.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,l):l),o.cloneElement(r,u)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:i,...a}=e,s=o.Children.toArray(i),u=s.find(l);if(u){let e=u.props.children,i=s.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...a,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,i):null})}return(0,n.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}("Slot"),s=Symbol("radix.slottable");function l(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}function u(e=[],t){let r=e??[];return t&&(r=[...r,{type:"icon",url:t,text:"Github",label:"GitHub",icon:(0,n.jsx)("svg",{role:"img",viewBox:"0 0 24 24",fill:"currentColor",children:(0,n.jsx)("path",{d:"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"})}),external:!0}]),r}function c(e,t,r,o){return e?.enabled===!1?o:e?.component!==void 0?(0,n.jsx)(a,{...r,children:e.component}):t}function f(e,t,r){if(t?.enabled!==!1)return t?.components?.[e]!==void 0?(0,n.jsx)(a,{children:t.components[e]}):r}},85766:(e,t,r)=>{"use strict";r.d(t,{JK:()=>n.J}),r(69593);var n=r(11160)},85814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return b}});let n=r(40740),o=r(60687),i=n._(r(43210)),a=r(30195),s=r(22142),l=r(59154),u=r(53038),c=r(79289),f=r(96127);r(50148);let d=r(73406),h=r(61794),p=r(63690);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){let t,r,n,[a,g]=(0,i.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,i.useRef)(null),{href:v,as:E,children:_,prefetch:w=null,passHref:T,replace:R,shallow:S,scroll:P,onClick:A,onMouseEnter:O,onTouchStart:x,legacyBehavior:C=!1,onNavigate:N,ref:I,unstable_dynamicOnHover:M,...k}=e;t=_,C&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let L=i.default.useContext(s.AppRouterContext),D=!1!==w,j=null===w?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:H,as:B}=i.default.useMemo(()=>{let e=m(v);return{href:e,as:E?m(E):e}},[v,E]);C&&(r=i.default.Children.only(t));let U=C?r&&"object"==typeof r&&r.ref:I,F=i.default.useCallback(e=>(null!==L&&(b.current=(0,d.mountLinkInstance)(e,H,L,j,D,g)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[D,H,L,j,g]),G={ref:(0,u.useMergedRef)(F,U),onClick(e){C||"function"!=typeof A||A(e),C&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,o,a,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,h.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==a||a,n.current)})}}(e,H,B,b,R,P,N))},onMouseEnter(e){C||"function"!=typeof O||O(e),C&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){C||"function"!=typeof x||x(e),C&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,c.isAbsoluteUrl)(B)?G.href=B:C&&!T&&("a"!==r.type||"href"in r.props)||(G.href=(0,f.addBasePath)(B)),n=C?i.default.cloneElement(r,G):(0,o.jsx)("a",{...k,...G,children:t}),(0,o.jsx)(y.Provider,{value:a,children:n})}r(32708);let y=(0,i.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return f}});let n=r(92584),o=r(29294),i=r(63033),a=r(84971),s=r(80023),l=r(68388),u=r(76926),c=(r(44523),r(8719));function f(){let e=o.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return h(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,u=t;let n=d.get(u);if(n)return n;let o=(0,l.makeHangingPromise)(u.renderSignal,"`headers()`");return d.set(u,o),Object.defineProperties(o,{append:{value:function(){let e=`\`headers().append(${p(arguments[0])}, ...)\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},delete:{value:function(){let e=`\`headers().delete(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},get:{value:function(){let e=`\`headers().get(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},has:{value:function(){let e=`\`headers().has(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},set:{value:function(){let e=`\`headers().set(${p(arguments[0])}, ...)\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},keys:{value:function(){let e="`headers().keys()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},values:{value:function(){let e="`headers().values()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},entries:{value:function(){let e="`headers().entries()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}}}),o}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return h((0,i.getExpectedRequestStore)("headers").headers)}let d=new WeakMap;function h(e){let t=d.get(e);if(t)return t;let r=Promise.resolve(e);return d.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function p(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},86770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,l){let u,[c,f,d,h,p]=r;if(1===t.length){let e=s(r,n);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,i.matchSegment)(m,c))return null;if(2===t.length)u=s(f[g],n);else if(null===(u=e((0,o.getNextFlightSegmentPath)(t),f[g],n,l)))return null;let y=[t[0],{...f,[g]:u},d,h];return p&&(y[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let n=r(83913),o=r(74007),i=r(14077),a=r(22308);function s(e,t){let[r,o]=e,[a,l]=t;if(a===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,i.matchSegment)(r,a)){let t={};for(let e in o)void 0!==l[e]?t[e]=s(o[e],l[e]):t[e]=o[e];for(let e in l)t[e]||(t[e]=l[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return f},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return s}});let n=r(52836),o=r(49026),i=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?o.RedirectType.push:o.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=o.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function f(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(26415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},88598:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(26373).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},88946:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(61120),o=r(67424);let i=(0,n.cache)(async function(e){return(await (0,o.A)(e)).now}),a=(0,n.cache)(async function(){return(await (0,o.A)()).formats});var s=r(80994),l=r(37413);let u=(0,n.cache)(async function(e){return(await (0,o.A)(e)).timeZone});async function c(e){return u(e?.locale)}var f=r(83930);let d=(0,n.cache)(async function(){return(await (0,o.A)()).locale});async function h({formats:e,locale:t,messages:r,now:n,timeZone:o,...u}){return(0,l.jsx)(s.default,{formats:void 0===e?await a():e,locale:t??await d(),messages:void 0===r?await (0,f.A)():r,now:n??await i(),timeZone:o??await c(),...u})}},89513:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.HeadManagerContext},89752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return x},createPrefetchURL:function(){return A},default:function(){return M},isExternalURL:function(){return P}});let n=r(40740),o=r(60687),i=n._(r(43210)),a=r(22142),s=r(59154),l=r(57391),u=r(10449),c=r(19129),f=n._(r(35656)),d=r(35416),h=r(96127),p=r(77022),m=r(67086),g=r(44397),y=r(89330),b=r(25942),v=r(4355),E=r(70642),_=r(12776),w=r(63690),T=r(36875),R=r(97860);r(73406);let S={};function P(e){return e.origin!==window.location.origin}function A(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,h.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return P(t)?null:t}function O(e){let{appRouterState:t}=e;return(0,i.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,o={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(o,"",n)):window.history.replaceState(o,"",n)},[t]),(0,i.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function x(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function C(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function N(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,i.useDeferredValue)(r,o)}function I(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,d=(0,c.useActionQueue)(r),{canonicalUrl:h}=d,{searchParams:_,pathname:P}=(0,i.useMemo)(()=>{let e=new URL(h,"http://n");return{searchParams:e.searchParams,pathname:(0,v.hasBasePath)(e.pathname)?(0,b.removeBasePath)(e.pathname):e.pathname}},[h]);(0,i.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(S.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,i.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,R.isRedirectError)(t)){e.preventDefault();let r=(0,T.getURLFromRedirectError)(t);(0,T.getRedirectTypeFromError)(t)===R.RedirectType.push?w.publicAppRouterInstance.push(r,{}):w.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:A}=d;if(A.mpaNavigation){if(S.pendingMpaPath!==h){let e=window.location;A.pendingPush?e.assign(h):e.replace(h),S.pendingMpaPath=h}(0,i.use)(y.unresolvedThenable)}(0,i.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,i.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=C(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=C(e),o&&r(o)),t(e,n,o)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,i.startTransition)(()=>{(0,w.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:x,tree:I,nextUrl:M,focusAndScrollRef:k}=d,L=(0,i.useMemo)(()=>(0,g.findHeadInCache)(x,I[1]),[x,I]),j=(0,i.useMemo)(()=>(0,E.getSelectedParams)(I),[I]),H=(0,i.useMemo)(()=>({parentTree:I,parentCacheNode:x,parentSegmentPath:null,url:h}),[I,x,h]),B=(0,i.useMemo)(()=>({tree:I,focusAndScrollRef:k,nextUrl:M}),[I,k,M]);if(null!==L){let[e,r]=L;t=(0,o.jsx)(N,{headCacheNode:e},r)}else t=null;let U=(0,o.jsxs)(m.RedirectBoundary,{children:[t,x.rsc,(0,o.jsx)(p.AppRouterAnnouncer,{tree:I})]});return U=(0,o.jsx)(f.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:U}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(O,{appRouterState:d}),(0,o.jsx)(D,{}),(0,o.jsx)(u.PathParamsContext.Provider,{value:j,children:(0,o.jsx)(u.PathnameContext.Provider,{value:P,children:(0,o.jsx)(u.SearchParamsContext.Provider,{value:_,children:(0,o.jsx)(a.GlobalLayoutRouterContext.Provider,{value:B,children:(0,o.jsx)(a.AppRouterContext.Provider,{value:w.publicAppRouterInstance,children:(0,o.jsx)(a.LayoutRouterContext.Provider,{value:H,children:U})})})})})})]})}function M(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:i}=e;return(0,_.useNavFailureHandler)(),(0,o.jsx)(f.ErrorBoundary,{errorComponent:f.default,children:(0,o.jsx)(I,{actionQueue:t,assetPrefix:i,globalError:[r,n]})})}let k=new Set,L=new Set;function D(){let[,e]=i.default.useState(0),t=k.size;return(0,i.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==k.size&&r(),()=>{L.delete(r)}},[t,e]),[...k].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90150:(e,t,r)=>{"use strict";r.d(t,{vt:()=>ey,$P:()=>eH});var n=r(85665),o=r(69593);function i(e){return{raw:Number(e),formatted:(0,o.j7)(e)}}function a(e){if(e.id){if("string"!=typeof e.id)throw(0,n.$)("DOCUMENT_ID_MUST_BE_STRING",typeof e.id);return e.id}return(0,o.NF)()}let s={string:!1,number:!1,boolean:!1,enum:!1,geopoint:!1,"string[]":!0,"number[]":!0,"boolean[]":!0,"enum[]":!0},l={"string[]":"string","number[]":"number","boolean[]":"boolean","enum[]":"enum"};function u(e){return"string"==typeof e&&/^vector\[\d+\]$/.test(e)}function c(e){return"string"==typeof e&&s[e]}function f(e){let t=Number(e.slice(7,-1));switch(!0){case isNaN(t):throw(0,n.$)("INVALID_VECTOR_VALUE",e);case t<=0:throw(0,n.$)("INVALID_VECTOR_SIZE",e);default:return t}}function d(e){return{internalIdToId:e.internalIdToId}}function h(e,t){let{internalIdToId:r}=t;e.internalDocumentIDStore.idToInternalId.clear(),e.internalDocumentIDStore.internalIdToId=[];let n=r.length;for(let t=0;t<n;t++){let n=r[t];e.internalDocumentIDStore.idToInternalId.set(n,t+1),e.internalDocumentIDStore.internalIdToId.push(n)}}function p(e,t){if("string"==typeof t){let r=e.idToInternalId.get(t);if(r)return r;let n=e.idToInternalId.size+1;return e.idToInternalId.set(t,n),e.internalIdToId.push(t),n}return t>e.internalIdToId.length?p(e,t.toString()):t}function m(e,t){if(e.internalIdToId.length<t)throw Error(`Invalid internalId ${t}`);return e.internalIdToId[t-1]}function g(e,t){return{sharedInternalDocumentStore:t,docs:{},count:0}}function y(e,t){let r=p(e.sharedInternalDocumentStore,t);return e.docs[r]}function b(e,t){let r=t.length,n=Array.from({length:r});for(let o=0;o<r;o++){let r=p(e.sharedInternalDocumentStore,t[o]);n[o]=e.docs[r]}return n}function v(e){return e.docs}function E(e,t,r,n){return void 0===e.docs[r]&&(e.docs[r]=n,e.count++,!0)}function _(e,t){let r=p(e.sharedInternalDocumentStore,t);return void 0!==e.docs[r]&&(delete e.docs[r],e.count--,!0)}function w(e){return e.count}function T(e,t){return{docs:t.docs,count:t.count,sharedInternalDocumentStore:e}}function R(e){return{docs:e.docs,count:e.count}}let S=["beforeInsert","afterInsert","beforeRemove","afterRemove","beforeUpdate","afterUpdate","beforeSearch","afterSearch","beforeInsertMultiple","afterInsertMultiple","beforeRemoveMultiple","afterRemoveMultiple","beforeUpdateMultiple","afterUpdateMultiple","beforeLoad","afterLoad","afterCreate"],P=["tokenizer","index","documentsStore","sorter"],A=["validateSchema","getDocumentIndexId","getDocumentProperties","formatElapsedTime"];function O(e,t,r,n,i){if(e.some(o.$S))return(async()=>{for(let o of e)await o(t,r,n,i)})();for(let o of e)o(t,r,n,i)}function x(e,t,r,n){if(e.some(o.$S))return(async()=>{for(let o of e)await o(t,r,n)})();for(let o of e)o(t,r,n)}class C{k;v;l=null;r=null;h=1;constructor(e,t){this.k=e,this.v=new Set(t)}updateHeight(){this.h=Math.max(C.getHeight(this.l),C.getHeight(this.r))+1}static getHeight(e){return e?e.h:0}getBalanceFactor(){return C.getHeight(this.l)-C.getHeight(this.r)}rotateLeft(){let e=this.r;return this.r=e.l,e.l=this,this.updateHeight(),e.updateHeight(),e}rotateRight(){let e=this.l;return this.l=e.r,e.r=this,this.updateHeight(),e.updateHeight(),e}toJSON(){return{k:this.k,v:Array.from(this.v),l:this.l?this.l.toJSON():null,r:this.r?this.r.toJSON():null,h:this.h}}static fromJSON(e){let t=new C(e.k,e.v);return t.l=e.l?C.fromJSON(e.l):null,t.r=e.r?C.fromJSON(e.r):null,t.h=e.h,t}}class N{root=null;insertCount=0;constructor(e,t){void 0!==e&&void 0!==t&&(this.root=new C(e,t))}insert(e,t,r=1e3){this.root=this.insertNode(this.root,e,t,r)}insertMultiple(e,t,r=1e3){for(let n of t)this.insert(e,n,r)}rebalance(){this.root&&(this.root=this.rebalanceNode(this.root))}toJSON(){return{root:this.root?this.root.toJSON():null,insertCount:this.insertCount}}static fromJSON(e){let t=new N;return t.root=e.root?C.fromJSON(e.root):null,t.insertCount=e.insertCount||0,t}insertNode(e,t,r,n){if(null===e)return new C(t,[r]);let o=[],i=e,a=null;for(;null!==i;)if(o.push({parent:a,node:i}),t<i.k)if(null===i.l){i.l=new C(t,[r]),o.push({parent:i,node:i.l});break}else a=i,i=i.l;else if(!(t>i.k))return i.v.add(r),e;else if(null===i.r){i.r=new C(t,[r]),o.push({parent:i,node:i.r});break}else a=i,i=i.r;let s=!1;this.insertCount++%n==0&&(s=!0);for(let t=o.length-1;t>=0;t--){let{parent:r,node:n}=o[t];if(n.updateHeight(),s){let t=this.rebalanceNode(n);r?r.l===n?r.l=t:r.r===n&&(r.r=t):e=t}}return e}rebalanceNode(e){let t=e.getBalanceFactor();if(t>1){if(e.l&&e.l.getBalanceFactor()>=0)return e.rotateRight();else if(e.l)return e.l=e.l.rotateLeft(),e.rotateRight()}if(t<-1){if(e.r&&0>=e.r.getBalanceFactor())return e.rotateLeft();else if(e.r)return e.r=e.r.rotateRight(),e.rotateLeft()}return e}find(e){let t=this.findNodeByKey(e);return t?t.v:null}contains(e){return null!==this.find(e)}getSize(){let e=0,t=[],r=this.root;for(;r||t.length>0;){for(;r;)t.push(r),r=r.l;r=t.pop(),e++,r=r.r}return e}isBalanced(){if(!this.root)return!0;let e=[this.root];for(;e.length>0;){let t=e.pop();if(Math.abs(t.getBalanceFactor())>1)return!1;t.l&&e.push(t.l),t.r&&e.push(t.r)}return!0}remove(e){this.root=this.removeNode(this.root,e)}removeDocument(e,t){let r=this.findNodeByKey(e);r&&(1===r.v.size?this.root=this.removeNode(this.root,e):r.v=new Set([...r.v.values()].filter(e=>e!==t)))}findNodeByKey(e){let t=this.root;for(;t;)if(e<t.k)t=t.l;else{if(!(e>t.k))return t;t=t.r}return null}removeNode(e,t){if(null===e)return null;let r=[],n=e;for(;null!==n&&n.k!==t;)r.push(n),n=t<n.k?n.l:n.r;if(null===n)return e;if(null===n.l||null===n.r){let t=n.l?n.l:n.r;if(0===r.length)e=t;else{let e=r[r.length-1];e.l===n?e.l=t:e.r=t}}else{let e=n,t=n.r;for(;null!==t.l;)e=t,t=t.l;n.k=t.k,n.v=t.v,e.l===t?e.l=t.r:e.r=t.r,n=e}r.push(n);for(let t=r.length-1;t>=0;t--){let n=r[t];n.updateHeight();let o=this.rebalanceNode(n);if(t>0){let e=r[t-1];e.l===n?e.l=o:e.r===n&&(e.r=o)}else e=o}return e}rangeSearch(e,t){let r=new Set,n=[],i=this.root;for(;i||n.length>0;){for(;i;)n.push(i),i=i.l;if((i=n.pop()).k>=e&&i.k<=t&&(r=(0,o.gG)(r,i.v)),i.k>t)break;i=i.r}return r}greaterThan(e,t=!1){let r=new Set,n=[],i=this.root;for(;i||n.length>0;){for(;i;)n.push(i),i=i.r;if(i=n.pop(),t&&i.k>=e||!t&&i.k>e)r=(0,o.gG)(r,i.v);else if(i.k<=e)break;i=i.l}return r}lessThan(e,t=!1){let r=new Set,n=[],i=this.root;for(;i||n.length>0;){for(;i;)n.push(i),i=i.l;if(i=n.pop(),t&&i.k<=e||!t&&i.k<e)r=(0,o.gG)(r,i.v);else if(i.k>e)break;i=i.r}return r}}class I{numberToDocumentId;constructor(){this.numberToDocumentId=new Map}insert(e,t){this.numberToDocumentId.has(e)?this.numberToDocumentId.get(e).add(t):this.numberToDocumentId.set(e,new Set([t]))}find(e){let t=this.numberToDocumentId.get(e);return t?Array.from(t):null}remove(e){this.numberToDocumentId.delete(e)}removeDocument(e,t){let r=this.numberToDocumentId.get(t);r&&(r.delete(e),0===r.size&&this.numberToDocumentId.delete(t))}contains(e){return this.numberToDocumentId.has(e)}getSize(){let e=0;for(let t of this.numberToDocumentId.values())e+=t.size;return e}filter(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"eq":{let t=e[r],n=this.numberToDocumentId.get(t);return n?Array.from(n):[]}case"in":{let t=e[r],n=new Set;for(let e of t){let t=this.numberToDocumentId.get(e);if(t)for(let e of t)n.add(e)}return Array.from(n)}case"nin":{let t=new Set(e[r]),n=new Set;for(let[e,r]of this.numberToDocumentId.entries())if(!t.has(e))for(let e of r)n.add(e);return Array.from(n)}default:throw Error("Invalid operation")}}filterArr(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"containsAll":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e].filter(e=>t.has(e)))))}case"containsAny":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e,...t])))}default:throw Error("Invalid operation")}}static fromJSON(e){if(!e.numberToDocumentId)throw Error("Invalid Flat Tree JSON");let t=new I;for(let[r,n]of e.numberToDocumentId)t.numberToDocumentId.set(r,new Set(n));return t}toJSON(){return{numberToDocumentId:Array.from(this.numberToDocumentId.entries()).map(([e,t])=>[e,Array.from(t)])}}}function M(e,t,r){let n=function(e,t,r){if(r<0)return -1;if(e===t)return 0;let n=e.length,o=t.length;if(0===n)return o<=r?o:-1;if(0===o)return n<=r?n:-1;let i=Math.abs(n-o);if(e.startsWith(t))return i<=r?i:-1;if(t.startsWith(e))return 0;if(i>r)return -1;let a=[];for(let e=0;e<=n;e++){a[e]=[e];for(let t=1;t<=o;t++)a[e][t]=0===e?t:0}for(let i=1;i<=n;i++){let n=1/0;for(let r=1;r<=o;r++)e[i-1]===t[r-1]?a[i][r]=a[i-1][r-1]:a[i][r]=Math.min(a[i-1][r]+1,a[i][r-1]+1,a[i-1][r-1]+1),n=Math.min(n,a[i][r]);if(n>r)return -1}return a[n][o]<=r?a[n][o]:-1}(e,t,r);return{distance:n,isBounded:n>=0}}class k{k;s;c=new Map;d=new Set;e;w="";constructor(e,t,r){this.k=e,this.s=t,this.e=r}updateParent(e){this.w=e.w+this.s}addDocument(e){this.d.add(e)}removeDocument(e){return this.d.delete(e)}findAllWords(e,t,r,n){let i=[this];for(;i.length>0;){let a=i.pop();if(a.e){let{w:i,d:s}=a;if(r&&i!==t)continue;if(null!==(0,o.g5)(e,i))if(n){if(!(Math.abs(t.length-i.length)<=n)||!M(t,i,n).isBounded)continue;e[i]=[]}else e[i]=[];if(null!=(0,o.g5)(e,i)&&s.size>0){let t=e[i];for(let e of s)t.includes(e)||t.push(e)}}a.c.size>0&&i.push(...a.c.values())}return e}insert(e,t){let r=this,n=0,o=e.length;for(;n<o;){let i=e[n],a=r.c.get(i);if(a){let i=a.s,s=i.length,l=0;for(;l<s&&n+l<o&&i[l]===e[n+l];)l++;if(l===s){if(r=a,(n+=l)===o){a.e||(a.e=!0),a.addDocument(t);return}continue}let u=i.slice(0,l),c=i.slice(l),f=e.slice(n+l),d=new k(u[0],u,!1);if(r.c.set(u[0],d),d.updateParent(r),a.s=c,a.k=c[0],d.c.set(c[0],a),a.updateParent(d),f){let e=new k(f[0],f,!0);e.addDocument(t),d.c.set(f[0],e),e.updateParent(d)}else d.e=!0,d.addDocument(t);return}{let o=new k(i,e.slice(n),!0);o.addDocument(t),r.c.set(i,o),o.updateParent(r);return}}r.e||(r.e=!0),r.addDocument(t)}_findLevenshtein(e,t,r,n,i){let a=[{node:this,index:t,tolerance:r}];for(;a.length>0;){let{node:t,index:r,tolerance:s}=a.pop();if(t.w.startsWith(e)){t.findAllWords(i,e,!1,0);continue}if(s<0)continue;if(t.e){let{w:r,d:a}=t;if(r&&(M(e,r,n).isBounded&&(i[r]=[]),void 0!==(0,o.g5)(i,r)&&a.size>0)){let e=new Set(i[r]);for(let t of a)e.add(t);i[r]=Array.from(e)}}if(r>=e.length)continue;let l=e[r];if(t.c.has(l)){let e=t.c.get(l);a.push({node:e,index:r+1,tolerance:s})}for(let[e,n]of(a.push({node:t,index:r+1,tolerance:s-1}),t.c))a.push({node:n,index:r,tolerance:s-1}),e!==l&&a.push({node:n,index:r+1,tolerance:s-1})}}find(e){let{term:t,exact:r,tolerance:n}=e;if(n&&!r){let e={};return this._findLevenshtein(t,0,n,n,e),e}{let e=this,o=0,i=t.length;for(;o<i;){let a=t[o],s=e.c.get(a);if(!s)return{};{let a=s.s,l=a.length,u=0;for(;u<l&&o+u<i&&a[u]===t[o+u];)u++;if(u===l)e=s,o+=u;else{if(o+u!==i||r)return{};let e={};return s.findAllWords(e,t,r,n),e}}}let a={};return e.findAllWords(a,t,r,n),a}}contains(e){let t=this,r=0,n=e.length;for(;r<n;){let o=e[r],i=t.c.get(o);if(!i)return!1;{let o=i.s,a=o.length,s=0;for(;s<a&&r+s<n&&o[s]===e[r+s];)s++;if(s<a)return!1;r+=a,t=i}}return!0}removeWord(e){if(!e)return!1;let t=this,r=e.length,n=[];for(let o=0;o<r;o++){let r=e[o];if(!t.c.has(r))return!1;{let e=t.c.get(r);n.push({parent:t,character:r}),o+=e.s.length-1,t=e}}for(t.d.clear(),t.e=!1;n.length>0&&0===t.c.size&&!t.e&&0===t.d.size;){let{parent:e,character:r}=n.pop();e.c.delete(r),t=e}return!0}removeDocumentByWord(e,t,r=!0){if(!e)return!0;let n=this,o=e.length;for(let i=0;i<o;i++){let o=e[i];if(!n.c.has(o))return!1;{let a=n.c.get(o);i+=a.s.length-1,n=a,r&&n.w!==e||n.removeDocument(t)}}return!0}static getCommonPrefix(e,t){let r=Math.min(e.length,t.length),n=0;for(;n<r&&e.charCodeAt(n)===t.charCodeAt(n);)n++;return e.slice(0,n)}toJSON(){return{w:this.w,s:this.s,e:this.e,k:this.k,d:Array.from(this.d),c:Array.from(this.c?.entries())?.map(([e,t])=>[e,t.toJSON()])}}static fromJSON(e){let t=new k(e.k,e.s,e.e);return t.w=e.w,t.d=new Set(e.d),t.c=new Map(e?.c?.map(([e,t])=>[e,k.fromJSON(t)])),t}}class L extends k{constructor(){super("","",!1)}static fromJSON(e){let t=new L;return t.w=e.w,t.s=e.s,t.e=e.e,t.k=e.k,t.d=new Set(e.d),t.c=new Map(e.c?.map(([e,t])=>[e,k.fromJSON(t)])),t}toJSON(){return super.toJSON()}}class D{point;docIDs;left;right;parent;constructor(e,t){this.point=e,this.docIDs=new Set(t),this.left=null,this.right=null,this.parent=null}toJSON(){return{point:this.point,docIDs:Array.from(this.docIDs),left:this.left?this.left.toJSON():null,right:this.right?this.right.toJSON():null}}static fromJSON(e,t=null){let r=new D(e.point,e.docIDs);return r.parent=t,e.left&&(r.left=D.fromJSON(e.left,r)),e.right&&(r.right=D.fromJSON(e.right,r)),r}}class j{root;nodeMap;constructor(){this.root=null,this.nodeMap=new Map}getPointKey(e){return`${e.lon},${e.lat}`}insert(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);if(n)return void t.forEach(e=>n.docIDs.add(e));let o=new D(e,t);if(this.nodeMap.set(r,o),null==this.root){this.root=o;return}let i=this.root,a=0;for(;;){if(0==a%2)if(e.lon<i.point.lon){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}else if(e.lat<i.point.lat){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}a++}}contains(e){let t=this.getPointKey(e);return this.nodeMap.has(t)}getDocIDsByCoordinates(e){let t=this.getPointKey(e),r=this.nodeMap.get(t);return r?Array.from(r.docIDs):null}removeDocByID(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);n&&(n.docIDs.delete(t),0===n.docIDs.size&&(this.nodeMap.delete(r),this.deleteNode(n)))}deleteNode(e){let t=e.parent,r=e.left?e.left:e.right;r&&(r.parent=t),t?t.left===e?t.left=r:t.right===e&&(t.right=r):(this.root=r,this.root&&(this.root.parent=null))}searchByRadius(e,t,r=!0,n="asc",o=!1){let i=o?j.vincentyDistance:j.haversineDistance,a=[{node:this.root,depth:0}],s=[];for(;a.length>0;){let{node:n,depth:o}=a.pop();if(null==n)continue;let l=i(e,n.point);(r?l<=t:l>t)&&s.push({point:n.point,docIDs:Array.from(n.docIDs)}),null!=n.left&&a.push({node:n.left,depth:o+1}),null!=n.right&&a.push({node:n.right,depth:o+1})}return n&&s.sort((t,r)=>{let o=i(e,t.point),a=i(e,r.point);return"asc"===n.toLowerCase()?o-a:a-o}),s}searchByPolygon(e,t=!0,r=null,n=!1){let o=[{node:this.root,depth:0}],i=[];for(;o.length>0;){let{node:r,depth:n}=o.pop();if(null==r)continue;null!=r.left&&o.push({node:r.left,depth:n+1}),null!=r.right&&o.push({node:r.right,depth:n+1});let a=j.isPointInPolygon(e,r.point);(a&&t||!a&&!t)&&i.push({point:r.point,docIDs:Array.from(r.docIDs)})}let a=j.calculatePolygonCentroid(e);if(r){let e=n?j.vincentyDistance:j.haversineDistance;i.sort((t,n)=>{let o=e(a,t.point),i=e(a,n.point);return"asc"===r.toLowerCase()?o-i:i-o})}return i}toJSON(){return{root:this.root?this.root.toJSON():null}}static fromJSON(e){let t=new j;return e.root&&(t.root=D.fromJSON(e.root),t.buildNodeMap(t.root)),t}buildNodeMap(e){if(null==e)return;let t=this.getPointKey(e.point);this.nodeMap.set(t,e),e.left&&this.buildNodeMap(e.left),e.right&&this.buildNodeMap(e.right)}static calculatePolygonCentroid(e){let t=0,r=0,n=0,o=e.length;for(let i=0,a=o-1;i<o;a=i++){let o=e[i].lon,s=e[i].lat,l=e[a].lon,u=e[a].lat,c=o*u-l*s;t+=c,r+=(o+l)*c,n+=(s+u)*c}let i=6*(t/=2);return{lon:r/=i,lat:n/=i}}static isPointInPolygon(e,t){let r=!1,n=t.lon,o=t.lat,i=e.length;for(let t=0,a=i-1;t<i;a=t++){let i=e[t].lon,s=e[t].lat,l=e[a].lon,u=e[a].lat;s>o!=u>o&&n<(l-i)*(o-s)/(u-s)+i&&(r=!r)}return r}static haversineDistance(e,t){let r=Math.PI/180,n=e.lat*r,o=t.lat*r,i=(t.lat-e.lat)*r,a=(t.lon-e.lon)*r,s=Math.sin(i/2)*Math.sin(i/2)+Math.cos(n)*Math.cos(o)*Math.sin(a/2)*Math.sin(a/2);return 2*Math.atan2(Math.sqrt(s),Math.sqrt(1-s))*6371e3}static vincentyDistance(e,t){let r,n,o,i,a,s,l,u=1/298.257223563,c=(1-1/298.257223563)*6378137,f=Math.PI/180,d=e.lat*f,h=t.lat*f,p=(t.lon-e.lon)*f,m=Math.atan((1-u)*Math.tan(d)),g=Math.atan((1-u)*Math.tan(h)),y=Math.sin(m),b=Math.cos(m),v=Math.sin(g),E=Math.cos(g),_=p,w=1e3;do{let e=Math.sin(_),t=Math.cos(_);if(0===(n=Math.sqrt(E*e*(E*e)+(b*v-y*E*t)*(b*v-y*E*t))))return 0;i=Math.atan2(n,o=y*v+b*E*t),isNaN(l=o-2*y*v/(s=1-(a=b*E*e/n)*a))&&(l=0);let c=u/16*s*(4+u*(4-3*s));r=_,_=p+(1-c)*u*a*(i+c*n*(l+c*o*(-1+2*l*l)))}while(Math.abs(_-r)>1e-12&&--w>0);if(0===w)return NaN;let T=s*(0x24ffb2985f71-c*c)/(c*c),R=1+T/16384*(4096+T*(-768+T*(320-175*T))),S=T/1024*(256+T*(-128+T*(74-47*T)));return c*R*(i-S*n*(l+S/4*(o*(-1+2*l*l)-S/6*l*(-3+4*n*n)*(-3+4*l*l))))}}class H{true;false;constructor(){this.true=new Set,this.false=new Set}insert(e,t){t?this.true.add(e):this.false.add(e)}delete(e,t){t?this.true.delete(e):this.false.delete(e)}getSize(){return this.true.size+this.false.size}toJSON(){return{true:Array.from(this.true),false:Array.from(this.false)}}static fromJSON(e){let t=new H;return t.true=new Set(e.true),t.false=new Set(e.false),t}}class B{size;vectors=new Map;constructor(e){this.size=e}add(e,t){t instanceof Float32Array||(t=new Float32Array(t));let r=U(t,this.size);this.vectors.set(e,[r,t])}remove(e){this.vectors.delete(e)}find(e,t,r){return e instanceof Float32Array||(e=new Float32Array(e)),function(e,t,r,n,o){let i=U(e,n),a=[];for(let s of t||r.keys()){let t=r.get(s);if(!t)continue;let l=t[0],u=t[1],c=0;for(let t=0;t<n;t++)c+=e[t]*u[t];let f=c/(i*l);f>=o&&a.push([s,f])}return a}(e,r,this.vectors,this.size,t)}toJSON(){let e=[];for(let[t,[r,n]]of this.vectors)e.push([t,[r,Array.from(n)]]);return{size:this.size,vectors:e}}static fromJSON(e){let t=new B(e.size);for(let[r,[n,o]]of e.vectors)t.vectors.set(r,[n,new Float32Array(o)]);return t}}function U(e,t){let r=0;for(let n=0;n<t;n++)r+=e[n]*e[n];return Math.sqrt(r)}function F(e,t,r,n,o){let i=p(e.sharedInternalDocumentStore,r);e.avgFieldLength[t]=((e.avgFieldLength[t]??0)*(o-1)+n.length)/o,e.fieldLengths[t][i]=n.length,e.frequencies[t][i]={}}function G(e,t,r,n,o){let i=0;for(let e of n)e===o&&i++;let a=p(e.sharedInternalDocumentStore,r),s=i/n.length;e.frequencies[t][a][o]=s,o in e.tokenOccurrences[t]||(e.tokenOccurrences[t][o]=0),e.tokenOccurrences[t][o]=(e.tokenOccurrences[t][o]??0)+1}function z(e,t,r,n){let o=p(e.sharedInternalDocumentStore,r);n>1?e.avgFieldLength[t]=(e.avgFieldLength[t]*n-e.fieldLengths[t][o])/(n-1):e.avgFieldLength[t]=void 0,e.fieldLengths[t][o]=void 0,e.frequencies[t][o]=void 0}function V(e,t,r){e.tokenOccurrences[t][r]--}function $(e,t,r,n,o,i,a,s,l,f,d){if(u(a)){var h,p,m,g,y;return h=t,p=r,m=i,g=0,y=o,void h.vectorIndexes[p].node.add(y,m)}let b=n=>{let{type:i,node:a}=t.indexes[r];switch(i){case"Bool":a[n?"true":"false"].add(o);break;case"AVL":{let e=d?.avlRebalanceThreshold??1;a.insert(n,o,e);break}case"Radix":{let i=l.tokenize(n,s,r,!1);for(let n of(e.insertDocumentScoreParameters(t,r,o,i,f),i))e.insertTokenScoreParameters(t,r,o,i,n),a.insert(n,o);break}case"Flat":a.insert(n,o);break;case"BKD":a.insert(n,[o])}};if(!c(a))return b(i);let v=i.length;for(let e=0;e<v;e++)b(i[e])}function W(e,t,r,n,o,i,a,s,l,c){if(u(a))return t.vectorIndexes[r].node.remove(o),!0;let{type:f,node:d}=t.indexes[r];switch(f){case"AVL":return d.removeDocument(i,o),!0;case"Bool":return d[i?"true":"false"].delete(o),!0;case"Radix":{let a=l.tokenize(i,s,r);for(let i of(e.removeDocumentScoreParameters(t,r,n,c),a))e.removeTokenScoreParameters(t,r,i),d.removeDocumentByWord(i,o);return!0}case"Flat":return d.removeDocument(o,i),!0;case"BKD":return d.removeDocByID(i,o),!1}}function K(e,t,r,n,o,i,a,s,u,f){if(!c(a))return W(e,t,r,n,o,i,a,s,u,f);let d=l[a],h=i.length;for(let a=0;a<h;a++)W(e,t,r,n,o,i[a],d,s,u,f);return!0}function X(e,t,r,n,o,i,a,s,l,u){let c=Array.from(n),f=e.avgFieldLength[t],d=e.fieldLengths[t],h=e.tokenOccurrences[t],p=e.frequencies[t],m="number"==typeof h[r]?h[r]??0:0,g=c.length;for(let e=0;e<g;e++){let n=c[e];if(l&&!l.has(n))continue;u.has(n)||u.set(n,new Map);let h=u.get(n);h.set(t,(h.get(t)||0)+1);let g=function(e,t,r,n,o,{k:i,b:a,d:s}){return Math.log(1+(r-t+.5)/(t+.5))*(s+e*(i+1))/(e+i*(1-a+a*n/o))}(p?.[n]?.[r]??0,m,o,d[n],f,i);a.has(n)?a.set(n,a.get(n)+g*s):a.set(n,g*s)}}function q(e,t,r,o,i,a,s,l,u,c,f,d=0){let h=r.tokenize(t,o),p=h.length||1,m=new Map,g=new Map;for(let r of i){if(!(r in e.indexes))continue;let o=e.indexes[r],{type:i}=o;if("Radix"!==i)throw(0,n.$)("WRONG_SEARCH_PROPERTY_TYPE",r);let d=l[r]??1;if(d<=0)throw(0,n.$)("INVALID_BOOST_VALUE",d);0!==h.length||t||h.push(""),function(e,t,r,n,o,i,a,s,l,u,c,f){let d=n.length;for(let h=0;h<d;h++){let d=n[h],p=t.find({term:d,exact:o,tolerance:i}),m=Object.keys(p),g=m.length;for(let t=0;t<g;t++){let n=m[t],o=p[n];X(e,r,n,o,u,l,a,s,c,f)}}}(e,o.node,r,h,a,s,g,d,u,c,f,m)}let y=Array.from(g.entries()).map(([e,t])=>[e,t]).sort((e,t)=>t[1]-e[1]);if(0===y.length)return[];if(1===d)return y;let b=y.filter(([e])=>{let t=m.get(e);return!!t&&Array.from(t.values()).some(e=>e===p)});if(0===d)return b;if(b.length>0){let e=y.filter(([e])=>!b.some(([t])=>t===e)),t=Math.ceil(e.length*d);return[...b,...e.slice(0,t)]}return y}function Y(e,t,r,i){let a=Object.keys(r),s=a.reduce((e,t)=>({[t]:new Set,...e}),{});for(let l of a){let a=r[l];if(void 0===e.indexes[l])throw(0,n.$)("UNKNOWN_FILTER_PROPERTY",l);let{node:u,type:c,isArray:f}=e.indexes[l];if("Bool"===c){let e=a?u.true:u.false;s[l]=(0,o.gG)(s[l],e);continue}if("BKD"===c){let e;if("radius"in a)e="radius";else if("polygon"in a)e="polygon";else throw Error(`Invalid operation ${a}`);if("radius"===e){let{value:t,coordinates:r,unit:n="m",inside:i=!0,highPrecision:c=!1}=a[e],f=(0,o.O6)(t,n),d=u.searchByRadius(r,f,i,void 0,c);s[l]=et(s[l],d)}else{let{coordinates:t,inside:r=!0,highPrecision:n=!1}=a[e],o=u.searchByPolygon(t,r,void 0,n);s[l]=et(s[l],o)}continue}if("Radix"===c&&("string"==typeof a||Array.isArray(a))){for(let e of[a].flat())for(let r of t.tokenize(e,i,l)){let e=u.find({term:r,exact:!0});s[l]=function(e,t){e||(e=new Set);let r=Object.keys(t),n=r.length;for(let o=0;o<n;o++){let n=t[r[o]],i=n.length;for(let t=0;t<i;t++)e.add(n[t])}return e}(s[l],e)}continue}let d=Object.keys(a);if(d.length>1)throw(0,n.$)("INVALID_FILTER_OPERATION",d.length);if("Flat"===c){let e=new Set(f?u.filterArr(a):u.filter(a));s[l]=(0,o.gG)(s[l],e);continue}if("AVL"===c){let e,t=d[0],r=a[t];switch(t){case"gt":e=u.greaterThan(r,!1);break;case"gte":e=u.greaterThan(r,!0);break;case"lt":e=u.lessThan(r,!1);break;case"lte":e=u.lessThan(r,!0);break;case"eq":e=u.find(r)??new Set;break;case"between":{let[t,n]=r;e=u.rangeSearch(t,n);break}default:throw(0,n.$)("INVALID_FILTER_OPERATION",t)}s[l]=(0,o.gG)(s[l],e)}}return(0,o.iR)(...Object.values(s))}function Z(e){return e.searchableProperties}function J(e){return e.searchablePropertiesWithTypes}function Q(e,t){let{indexes:r,vectorIndexes:n,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:a,tokenOccurrences:s,avgFieldLength:l,fieldLengths:u}=t,c={},f={};for(let e of Object.keys(r)){let{node:t,type:n,isArray:o}=r[e];switch(n){case"Radix":c[e]={type:"Radix",node:L.fromJSON(t),isArray:o};break;case"Flat":c[e]={type:"Flat",node:I.fromJSON(t),isArray:o};break;case"AVL":c[e]={type:"AVL",node:N.fromJSON(t),isArray:o};break;case"BKD":c[e]={type:"BKD",node:j.fromJSON(t),isArray:o};break;case"Bool":c[e]={type:"Bool",node:H.fromJSON(t),isArray:o};break;default:c[e]=r[e]}}for(let e of Object.keys(n))f[e]={type:"Vector",isArray:!1,node:B.fromJSON(n[e])};return{sharedInternalDocumentStore:e,indexes:c,vectorIndexes:f,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:a,tokenOccurrences:s,avgFieldLength:l,fieldLengths:u}}function ee(e){let{indexes:t,vectorIndexes:r,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:a,avgFieldLength:s,fieldLengths:l}=e,u={};for(let e of Object.keys(r))u[e]=r[e].node.toJSON();let c={};for(let e of Object.keys(t)){let{type:r,node:n,isArray:o}=t[e];"Flat"===r||"Radix"===r||"AVL"===r||"BKD"===r||"Bool"===r?c[e]={type:r,node:n.toJSON(),isArray:o}:(c[e]=t[e],c[e].node=c[e].node.toJSON())}return{indexes:c,vectorIndexes:u,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:a,avgFieldLength:s,fieldLengths:l}}function et(e,t){e||(e=new Set);let r=t.length;for(let n=0;n<r;n++){let r=t[n].docIDs,o=r.length;for(let t=0;t<o;t++)e.add(r[t])}return e}var er=r(77728);function en(e,t,r,i){return i?.enabled===!1?{disabled:!0}:function e(t,r,i,a,s){let l={language:t.tokenizer.language,sharedInternalDocumentStore:r,enabled:!0,isSorted:!0,sortableProperties:[],sortablePropertiesWithTypes:{},sorts:{}};for(let[c,f]of Object.entries(i)){let i=`${s}${s?".":""}${c}`;if(!a.includes(i)){if("object"==typeof f&&!Array.isArray(f)){let n=e(t,r,f,a,i);(0,o.h)(l.sortableProperties,n.sortableProperties),l.sorts={...l.sorts,...n.sorts},l.sortablePropertiesWithTypes={...l.sortablePropertiesWithTypes,...n.sortablePropertiesWithTypes};continue}if(!u(f))switch(f){case"boolean":case"number":case"string":l.sortableProperties.push(i),l.sortablePropertiesWithTypes[i]=f,l.sorts[i]={docs:new Map,orderedDocsToRemove:new Map,orderedDocs:[],type:f};break;case"geopoint":case"enum":case"enum[]":case"boolean[]":case"number[]":case"string[]":continue;default:throw(0,n.$)("INVALID_SORT_SCHEMA_TYPE",Array.isArray(f)?"array":f,i)}}}return l}(e,t,r,(i||{}).unsortableProperties||[],"")}function eo(e,t,r,n){if(!e.enabled)return;e.isSorted=!1;let o=p(e.sharedInternalDocumentStore,r),i=e.sorts[t];i.orderedDocsToRemove.has(o)&&eu(e,t),i.docs.set(o,i.orderedDocs.length),i.orderedDocs.push([o,n])}function ei(e){if(!e.isSorted&&e.enabled){for(let t of Object.keys(e.sorts))!function(e,t){let r,n=e.sorts[t];switch(n.type){case"string":r=ea.bind(null,e.language);break;case"number":r=es.bind(null);break;case"boolean":r=el.bind(null)}n.orderedDocs.sort(r);let o=n.orderedDocs.length;for(let e=0;e<o;e++){let t=n.orderedDocs[e][0];n.docs.set(t,e)}}(e,t);e.isSorted=!0}}function ea(e,t,r){return t[1].localeCompare(r[1],(0,er.JK)(e))}function es(e,t){return e[1]-t[1]}function el(e,t){return t[1]?-1:1}function eu(e,t){let r=e.sorts[t];r.orderedDocsToRemove.size&&(r.orderedDocs=r.orderedDocs.filter(e=>!r.orderedDocsToRemove.has(e[0])),r.orderedDocsToRemove.clear())}function ec(e,t,r){if(!e.enabled)return;let n=e.sorts[t],o=p(e.sharedInternalDocumentStore,r);n.docs.get(o)&&(n.docs.delete(o),n.orderedDocsToRemove.set(o,!0))}function ef(e,t,r){if(!e.enabled)throw(0,n.$)("SORT_DISABLED");let o=r.property,i="DESC"===r.order,a=e.sorts[o];if(!a)throw(0,n.$)("UNABLE_TO_SORT_ON_UNKNOWN_FIELD",o,e.sortableProperties.join(", "));return eu(e,o),ei(e),t.sort((t,r)=>{let n=a.docs.get(p(e.sharedInternalDocumentStore,t[0])),o=a.docs.get(p(e.sharedInternalDocumentStore,r[0])),s=void 0!==n,l=void 0!==o;return s||l?s?l?i?o-n:n-o:-1:1:0}),t}function ed(e){return e.enabled?e.sortableProperties:[]}function eh(e){return e.enabled?e.sortablePropertiesWithTypes:{}}function ep(e,t){if(!t.enabled)return{enabled:!1};let r=Object.keys(t.sorts).reduce((e,r)=>{let{docs:n,orderedDocs:o,type:i}=t.sorts[r];return e[r]={docs:new Map(Object.entries(n).map(([e,t])=>[+e,t])),orderedDocsToRemove:new Map,orderedDocs:o,type:i},e},{});return{sharedInternalDocumentStore:e,language:t.language,sortableProperties:t.sortableProperties,sortablePropertiesWithTypes:t.sortablePropertiesWithTypes,sorts:r,enabled:!0,isSorted:t.isSorted}}function em(e){if(!e.enabled)return{enabled:!1};for(let t of Object.keys(e.sorts))eu(e,t);ei(e);let t=Object.keys(e.sorts).reduce((t,r)=>{let{docs:n,orderedDocs:o,type:i}=e.sorts[r];return t[r]={docs:Object.fromEntries(n.entries()),orderedDocs:o,type:i},t},{});return{language:e.language,sortableProperties:e.sortableProperties,sortablePropertiesWithTypes:e.sortablePropertiesWithTypes,sorts:t,enabled:e.enabled,isSorted:e.isSorted}}var eg=r(11160);function ey({schema:e,sort:t,language:r,components:s,id:p,plugins:m}){for(let t of(s||(s={}),m??[])){if(!("getComponents"in t)||"function"!=typeof t.getComponents)continue;let r=t.getComponents(e);for(let e of Object.keys(r))if(s[e])throw(0,n.$)("PLUGIN_COMPONENT_CONFLICT",e,t.name);s={...s,...r}}p||(p=(0,o.NF)());let O=s.tokenizer,x=s.index,C=s.documentsStore,M=s.sorter;if(O=O?O.tokenize?O:(0,eg.e)(O):(0,eg.e)({language:r??"english"}),s.tokenizer&&r)throw(0,n.$)("NO_LANGUAGE_WITH_CUSTOM_TOKENIZER");let k={idToInternalId:new Map,internalIdToId:[],save:d,load:h};x||={create:function e(t,r,o,i,a=""){for(let[s,l]of(i||(i={sharedInternalDocumentStore:r,indexes:{},vectorIndexes:{},searchableProperties:[],searchablePropertiesWithTypes:{},frequencies:{},tokenOccurrences:{},avgFieldLength:{},fieldLengths:{}}),Object.entries(o))){let o=`${a}${a?".":""}${s}`;if("object"==typeof l&&!Array.isArray(l)){e(t,r,l,i,o);continue}if(u(l))i.searchableProperties.push(o),i.searchablePropertiesWithTypes[o]=l,i.vectorIndexes[o]={type:"Vector",node:new B(f(l)),isArray:!1};else{let e=/\[/.test(l);switch(l){case"boolean":case"boolean[]":i.indexes[o]={type:"Bool",node:new H,isArray:e};break;case"number":case"number[]":i.indexes[o]={type:"AVL",node:new N(0,[]),isArray:e};break;case"string":case"string[]":i.indexes[o]={type:"Radix",node:new L,isArray:e},i.avgFieldLength[o]=0,i.frequencies[o]={},i.tokenOccurrences[o]={},i.fieldLengths[o]={};break;case"enum":case"enum[]":i.indexes[o]={type:"Flat",node:new I,isArray:e};break;case"geopoint":i.indexes[o]={type:"BKD",node:new j,isArray:e};break;default:throw(0,n.$)("INVALID_SCHEMA_TYPE",Array.isArray(l)?"array":l,o)}i.searchableProperties.push(o),i.searchablePropertiesWithTypes[o]=l}}return i},insert:$,remove:K,insertDocumentScoreParameters:F,insertTokenScoreParameters:G,removeDocumentScoreParameters:z,removeTokenScoreParameters:V,calculateResultScores:X,search:q,searchByWhereClause:Y,getSearchableProperties:Z,getSearchablePropertiesWithTypes:J,load:Q,save:ee},M||={create:en,insert:eo,remove:ec,save:em,load:ep,sortBy:ef,getSortableProperties:ed,getSortablePropertiesWithTypes:eh},C||={create:g,get:y,getMultiple:b,getAll:v,store:E,remove:_,count:w,load:T,save:R};var D=s;let U={formatElapsedTime:i,getDocumentIndexId:a,getDocumentProperties:o.JN,validateSchema:function e(t,r){for(let[o,i]of Object.entries(r)){let r=t[o];if(void 0!==r&&("geopoint"!==i||"object"!=typeof r||"number"!=typeof r.lon||"number"!=typeof r.lat)&&("enum"!==i||"string"!=typeof r&&"number"!=typeof r)){if("enum[]"===i&&Array.isArray(r)){let e=r.length;for(let t=0;t<e;t++)if("string"!=typeof r[t]&&"number"!=typeof r[t])return o+"."+t;continue}if(u(i)){let e=f(i);if(!Array.isArray(r)||r.length!==e)throw(0,n.$)("INVALID_INPUT_VECTOR",o,e,r.length);continue}if(c(i)){if(!Array.isArray(r))return o;let e=l[i],t=r.length;for(let n=0;n<t;n++)if(typeof r[n]!==e)return o+"."+n;continue}if("object"==typeof i){if(!r||"object"!=typeof r)return o;let t=e(r,i);if(t)return o+"."+t;continue}if(typeof r!==i)return o}}}};for(let e of A)if(D[e]){if("function"!=typeof D[e])throw(0,n.$)("COMPONENT_MUST_BE_FUNCTION",e)}else D[e]=U[e];for(let e of Object.keys(D))if(!P.includes(e)&&!A.includes(e))throw(0,n.$)("UNSUPPORTED_COMPONENT",e);let{getDocumentProperties:W,getDocumentIndexId:et,validateSchema:er,formatElapsedTime:ei}=s,ea={data:{},caches:{},schema:e,tokenizer:O,index:x,sorter:M,documentsStore:C,internalDocumentIDStore:k,getDocumentProperties:W,getDocumentIndexId:et,validateSchema:er,beforeInsert:[],afterInsert:[],beforeRemove:[],afterRemove:[],beforeUpdate:[],afterUpdate:[],beforeSearch:[],afterSearch:[],beforeInsertMultiple:[],afterInsertMultiple:[],beforeRemoveMultiple:[],afterRemoveMultiple:[],afterUpdateMultiple:[],beforeUpdateMultiple:[],afterCreate:[],formatElapsedTime:ei,id:p,plugins:m,version:"{{VERSION}}"};for(let r of(ea.data={index:ea.index.create(ea,k,e),docs:ea.documentsStore.create(ea,k),sorting:ea.sorter.create(ea,k,e,t)},S))ea[r]=(ea[r]??[]).concat(function(e,t){let r=[],o=e.plugins?.length;if(!o)return r;for(let i=0;i<o;i++)try{let n=e.plugins[i];"function"==typeof n[t]&&r.push(n[t])}catch(e){throw console.error("Caught error in getAllPluginsByHook:",e),(0,n.$)("PLUGIN_CRASHED")}return r}(ea,r));let es=ea.afterCreate;return es&&function(e,t){if(e.some(o.$S))return(async()=>{for(let r of e)await r(t)})();for(let r of e)r(t)}(es,ea),ea}function eb(e,t,r,n,o){let i=e.validateSchema(t,e.schema);if(i)throw createError("SCHEMA_VALIDATION_FAILURE",i);return isAsyncFunction(e.beforeInsert)||isAsyncFunction(e.afterInsert)||isAsyncFunction(e.index.beforeInsert)||isAsyncFunction(e.index.insert)||isAsyncFunction(e.index.afterInsert)?e_(e,t,r,n,o):function(e,t,r,n,o){let{index:i,docs:a}=e.data,s=e.getDocumentIndexId(t);if("string"!=typeof s)throw createError("DOCUMENT_ID_MUST_BE_STRING",typeof s);let l=getInternalDocumentId(e.internalDocumentIDStore,s);if(!e.documentsStore.store(a,s,l,t))throw createError("DOCUMENT_ALREADY_EXISTS",s);let u=e.documentsStore.count(a);n||runSingleHook(e.beforeInsert,e,s,t);let c=e.index.getSearchableProperties(i),f=e.index.getSearchablePropertiesWithTypes(i),d=e.getDocumentProperties(t,c);for(let[e,t]of Object.entries(d))void 0!==t&&ew(typeof t,f[e],e,t);return function(e,t,r,n,o,i,a,s){for(let a of r){let r=n[a];if(void 0===r)continue;let l=e.index.getSearchablePropertiesWithTypes(e.data.index)[a],u=getInternalDocumentId(e.internalDocumentIDStore,t);e.index.beforeInsert?.(e.data.index,a,t,r,l,i,e.tokenizer,o),e.index.insert(e.index,e.data.index,a,t,u,r,l,i,e.tokenizer,o,s),e.index.afterInsert?.(e.data.index,a,t,r,l,i,e.tokenizer,o)}let l=e.sorter.getSortableProperties(e.data.sorting),u=e.getDocumentProperties(a,l);for(let r of l){let n=u[r];if(void 0===n)continue;let o=e.sorter.getSortablePropertiesWithTypes(e.data.sorting)[r];e.sorter.insert(e.data.sorting,r,t,n,o,i)}}(e,s,c,d,u,r,t,o),n||runSingleHook(e.afterInsert,e,s,t),trackInsertion(e),s}(e,t,r,n,o)}Symbol("orama.insertions"),Symbol("orama.removals"),globalThis.process?.emitWarning;let ev=new Set(["enum","enum[]"]),eE=new Set(["string","number"]);async function e_(e,t,r,n,o){let{index:i,docs:a}=e.data,s=e.getDocumentIndexId(t);if("string"!=typeof s)throw createError("DOCUMENT_ID_MUST_BE_STRING",typeof s);let l=getInternalDocumentId(e.internalDocumentIDStore,s);if(!e.documentsStore.store(a,s,l,t))throw createError("DOCUMENT_ALREADY_EXISTS",s);let u=e.documentsStore.count(a);n||await runSingleHook(e.beforeInsert,e,s,t);let c=e.index.getSearchableProperties(i),f=e.index.getSearchablePropertiesWithTypes(i),d=e.getDocumentProperties(t,c);for(let[e,t]of Object.entries(d))void 0!==t&&ew(typeof t,f[e],e,t);return await eT(e,s,c,d,u,r,t,o),n||await runSingleHook(e.afterInsert,e,s,t),trackInsertion(e),s}function ew(e,t,r,n){if(!(isGeoPointType(t)&&"object"==typeof n&&"number"==typeof n.lon&&"number"==typeof n.lat||isVectorType(t)&&Array.isArray(n)||isArrayType(t)&&Array.isArray(n))&&!(ev.has(t)&&eE.has(e))&&e!==t)throw createError("INVALID_DOCUMENT_PROPERTY",r,t,e)}async function eT(e,t,r,n,o,i,a,s){for(let a of r){let r=n[a];if(void 0===r)continue;let l=e.index.getSearchablePropertiesWithTypes(e.data.index)[a];await e.index.beforeInsert?.(e.data.index,a,t,r,l,i,e.tokenizer,o);let u=e.internalDocumentIDStore.idToInternalId.get(t);await e.index.insert(e.index,e.data.index,a,t,u,r,l,i,e.tokenizer,o,s),await e.index.afterInsert?.(e.data.index,a,t,r,l,i,e.tokenizer,o)}let l=e.sorter.getSortableProperties(e.data.sorting),u=e.getDocumentProperties(a,l);for(let r of l){let n=u[r];if(void 0===n)continue;let o=e.sorter.getSortablePropertiesWithTypes(e.data.sorting)[r];e.sorter.insert(e.data.sorting,r,t,n,o,i)}}async function eR(e,t,r,n){let o=!0,{index:i,docs:a}=e.data,s=e.documentsStore.get(a,t);if(!s)return!1;let l=getInternalDocumentId(e.internalDocumentIDStore,t),u=getDocumentIdFromInternalId(e.internalDocumentIDStore,l),c=e.documentsStore.count(a);n||await runSingleHook(e.beforeRemove,e,u);let f=e.index.getSearchableProperties(i),d=e.index.getSearchablePropertiesWithTypes(i),h=e.getDocumentProperties(s,f);for(let n of f){let i=h[n];if(void 0===i)continue;let a=d[n];await e.index.beforeRemove?.(e.data.index,n,u,i,a,r,e.tokenizer,c),await e.index.remove(e.index,e.data.index,n,t,l,i,a,r,e.tokenizer,c)||(o=!1),await e.index.afterRemove?.(e.data.index,n,u,i,a,r,e.tokenizer,c)}let p=await e.sorter.getSortableProperties(e.data.sorting),m=await e.getDocumentProperties(s,p);for(let r of p)void 0!==m[r]&&e.sorter.remove(e.data.sorting,r,t);return n||await runSingleHook(e.afterRemove,e,u),e.documentsStore.remove(e.data.docs,t,l),trackRemoval(e),o}let eS="fulltext";function eP(e,t){return e[1]-t[1]}function eA(e,t){return t[1]-e[1]}function eO(e,t,r){let i={},a=t.map(([e])=>e),s=e.documentsStore.getMultiple(e.data.docs,a),l=Object.keys(r),u=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e of l){let t;if("number"===u[e]){let{ranges:n}=r[e],o=n.length,i=Array.from({length:o});for(let e=0;e<o;e++){let t=n[e];i[e]=[`${t.from}-${t.to}`,0]}t=Object.fromEntries(i)}i[e]={count:0,values:t??{}}}let c=s.length;for(let e=0;e<c;e++){let t=s[e];for(let e of l){let a=e.includes(".")?(0,o.wH)(t,e):t[e],s=u[e],l=i[e].values;switch(s){case"number":ex(r[e].ranges,l)(a);break;case"number[]":{let t=new Set,n=ex(r[e].ranges,l,t);for(let e of a)n(e);break}case"boolean":case"enum":case"string":eC(l,s)(a);break;case"boolean[]":case"enum[]":case"string[]":{let e=eC(l,"boolean[]"===s?"boolean":"string",new Set);for(let t of a)e(t);break}default:throw(0,n.$)("FACET_NOT_SUPPORTED",s)}}}for(let e of l){let t=i[e];if(t.count=Object.keys(t.values).length,"string"===u[e]){let n=r[e],o=function(e="desc"){return"asc"===e.toLowerCase()?eP:eA}(n.sort);t.values=Object.fromEntries(Object.entries(t.values).sort(o).slice(n.offset??0,n.limit??10))}}return i}function ex(e,t,r){return n=>{for(let o of e){let e=`${o.from}-${o.to}`;!r?.has(e)&&n>=o.from&&n<=o.to&&(void 0===t[e]?t[e]=1:(t[e]++,r?.add(e)))}}}function eC(e,t,r){let n="boolean"===t?"false":"";return t=>{let o=t?.toString()??n;r?.has(o)||(e[o]=(e[o]??0)+1,r?.add(o))}}let eN={reducer:(e,t,r,n)=>(t[n]=r,t),getInitialValue:e=>Array.from({length:e})},eI=["string","number","boolean"];function eM(e,t,r){let i=r.properties,a=i.length,s=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e=0;e<a;e++){let t=i[e];if(void 0===s[t])throw(0,n.$)("UNKNOWN_GROUP_BY_PROPERTY",t);if(!eI.includes(s[t]))throw(0,n.$)("INVALID_GROUP_BY_PROPERTY",t,eI.join(", "),s[t])}let l=t.map(([t])=>m(e.internalDocumentIDStore,t)),u=e.documentsStore.getMultiple(e.data.docs,l),c=u.length,f=r.maxResult||Number.MAX_SAFE_INTEGER,d=[],h={};for(let e=0;e<a;e++){let t=i[e],r={property:t,perValue:{}},n=new Set;for(let e=0;e<c;e++){let i=u[e],a=(0,o.wH)(i,t);if(void 0===a)continue;let s="boolean"!=typeof a?a:""+a,l=r.perValue[s]??{indexes:[],count:0};l.count>=f||(l.indexes.push(e),l.count++,r.perValue[s]=l,n.add(a))}d.push(Array.from(n)),h[t]=r}let p=function e(t,r=0){if(r+1===t.length)return t[r].map(e=>[e]);let n=t[r],i=e(t,r+1),a=[];for(let e of n)for(let t of i){let r=[e];(0,o.h)(r,t),a.push(r)}return a}(d),g=p.length,y=[];for(let e=0;e<g;e++){let t=p[e],r=t.length,n={values:[],indexes:[]},a=[];for(let e=0;e<r;e++){let r=t[e],o=i[e];a.push(h[o].perValue["boolean"!=typeof r?r:""+r].indexes),n.values.push(r)}n.indexes=(0,o.y$)(a).sort((e,t)=>e-t),0!==n.indexes.length&&y.push(n)}let b=y.length,v=Array.from({length:b});for(let e=0;e<b;e++){let n=y[e],o=r.reduce||eN,i=n.indexes.map(e=>({id:l[e],score:t[e][1],document:u[e]})),a=o.reducer.bind(null,n.values),s=o.getInitialValue(n.indexes.length),c=i.reduce(a,s);v[e]={values:n.values,result:c}}return v}function ek(e,t,r){let o,i,{term:a,properties:s}=t,l=e.data.index,u=e.caches.propertiesToSearch;if(!u){let t=e.index.getSearchablePropertiesWithTypes(l);u=(u=e.index.getSearchableProperties(l)).filter(e=>t[e].startsWith("string")),e.caches.propertiesToSearch=u}if(s&&"*"!==s){for(let e of s)if(!u.includes(e))throw(0,n.$)("UNKNOWN_INDEX",e,u.join(", "));u=u.filter(e=>s.includes(e))}if(Object.keys(t.where??{}).length>0&&(o=e.index.searchByWhereClause(l,e.tokenizer,t.where,r)),a||s){let n=e.documentsStore.count(e.data.docs);i=e.index.search(l,a||"",e.tokenizer,r,u,t.exact||!1,t.tolerance||0,t.boost||{},function(e){let t=e??{};return t.k=t.k??eL.k,t.b=t.b??eL.b,t.d=t.d??eL.d,t}(t.relevance),n,o,void 0!==t.threshold&&null!==t.threshold?t.threshold:1)}else i=(o?Array.from(o):Object.keys(e.documentsStore.getAll(e.data.docs))).map(e=>[+e,0]);return i}let eL={k:1.2,b:.75,d:.5};function eD(e,t,r){let o,i=t.vector;if(i&&(!("value"in i)||!("property"in i)))throw(0,n.$)("INVALID_VECTOR_INPUT",Object.keys(i).join(", "));let a=e.data.index.vectorIndexes[i.property],s=a.node.size;if(i?.value.length!==s){if(i?.property===void 0||i?.value.length===void 0)throw(0,n.$)("INVALID_INPUT_VECTOR","undefined",s,"undefined");throw(0,n.$)("INVALID_INPUT_VECTOR",i.property,s,i.value.length)}let l=e.data.index;return Object.keys(t.where??{}).length>0&&(o=e.index.searchByWhereClause(l,e.tokenizer,t.where,r)),a.node.find(i.value,t.similarity??.8,o)}function ej(e){return e[1]}function eH(e,t,r){let i=t.mode??eS;if(i===eS){let n=(0,o.He)();function a(){let i,a=Object.keys(e.data.index.vectorIndexes),s=t.facets&&Object.keys(t.facets).length>0,{limit:l=10,offset:u=0,distinctOn:c,includeVectors:f=!1}=t,d=!0===t.preflight,h=ek(e,t,r);if(t.sortBy)if("function"==typeof t.sortBy){let r=h.map(([e])=>e),n=e.documentsStore.getMultiple(e.data.docs,r).map((e,t)=>[h[t][0],h[t][1],e]);n.sort(t.sortBy),h=n.map(([e,t])=>[e,t])}else h=e.sorter.sortBy(e.data.sorting,h,t.sortBy).map(([t,r])=>[p(e.internalDocumentIDStore,t),r]);else h=h.sort(o.$J);d||(i=c?function(e,t,r,n,i){let a=e.data.docs,s=new Map,l=[],u=new Set,c=t.length,f=0;for(let d=0;d<c;d++){let c=t[d];if(void 0===c)continue;let[h,p]=c;if(u.has(h))continue;let g=e.documentsStore.get(a,h),y=(0,o.wH)(g,i);if(!(void 0===y||s.has(y))&&(s.set(y,!0),!(++f<=r)&&(l.push({id:m(e.internalDocumentIDStore,h),score:p,document:g}),u.add(h),f>=r+n)))break}return l}(e,h,u,l,c):eB(e,h,u,l));let g={elapsed:{formatted:"",raw:0},hits:[],count:h.length};return void 0!==i&&(g.hits=i.filter(Boolean),f||(0,o.JW)(g,a)),s&&(g.facets=eO(e,h,t.facets)),t.groupBy&&(g.groups=eM(e,h,t.groupBy)),g.elapsed=e.formatElapsedTime((0,o.He)()-n),g}async function s(){e.beforeSearch&&await x(e.beforeSearch,e,t,r);let n=a();return e.afterSearch&&await O(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?s():a()}if("vector"===i)return function(e,t,r="english"){let n=(0,o.He)();function i(){let i=eD(e,t,r).sort(o.$J),a=[];t.facets&&Object.keys(t.facets).length>0&&(a=eO(e,i,t.facets));let s=t.vector.property,l=t.includeVectors??!1,u=t.limit??10,c=t.offset??0,f=Array.from({length:u});for(let t=0;t<u;t++){let r=i[t+c];if(!r)break;let n=e.data.docs.docs[r[0]];if(n){l||(n[s]=null);let o={id:m(e.internalDocumentIDStore,r[0]),score:r[1],document:n};f[t]=o}}let d=[];t.groupBy&&(d=eM(e,i,t.groupBy));let h=(0,o.He)()-n;return{count:i.length,hits:f.filter(Boolean),elapsed:{raw:Number(h),formatted:(0,o.j7)(h)},...a?{facets:a}:{},...d?{groups:d}:{}}}async function a(){e.beforeSearch&&await x(e.beforeSearch,e,t,r);let n=i();return e.afterSearch&&await O(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?a():i()}(e,t);if("hybrid"===i)return function(e,t,r){let n=(0,o.He)();function i(){let r,i,a=function(e,t,r){let n=function(e){let t=Math.max.apply(Math,e.map(ej));return e.map(([e,r])=>[e,r/t])}(ek(e,t,r)),o=eD(e,t,r),i=t.hybridWeights;return function(e,t,r,n){var o;let i=Math.max.apply(Math,e.map(ej)),a=Math.max.apply(Math,t.map(ej)),{text:s,vector:l}=n&&n.text&&n.vector?n:(o=0,{text:.5,vector:.5}),u=new Map,c=e.length,f=(e,t)=>e*s+t*l;for(let t=0;t<c;t++){let[r,n]=e[t],o=f(n/i,0);u.set(r,o)}let d=t.length;for(let e=0;e<d;e++){let[r,n]=t[e],o=n/a,i=u.get(r)??0;u.set(r,i+f(0,o))}return[...u].sort((e,t)=>t[1]-e[1])}(n,o,t.term??"",i)}(e,t,void 0);t.facets&&Object.keys(t.facets).length>0&&(r=eO(e,a,t.facets)),t.groupBy&&(i=eM(e,a,t.groupBy));let s=eB(e,a,t.offset??0,t.limit??10).filter(Boolean),l=(0,o.He)(),u={count:a.length,elapsed:{raw:Number(l-n),formatted:(0,o.j7)(l-n)},hits:s,...r?{facets:r}:{},...i?{groups:i}:{}};if(!t.includeVectors){let t=Object.keys(e.data.index.vectorIndexes);(0,o.JW)(u,t)}return u}async function a(){e.beforeSearch&&await x(e.beforeSearch,e,t,void 0);let n=i();return e.afterSearch&&await O(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?a():i()}(e,t);throw(0,n.$)("INVALID_SEARCH_MODE",i)}function eB(e,t,r,n){let o=e.data.docs,i=Array.from({length:n}),a=new Set;for(let s=r;s<n+r;s++){let r=t[s];if(void 0===r)break;let[n,l]=r;if(!a.has(n)){let t=e.documentsStore.get(o,n);i[s]={id:m(e.internalDocumentIDStore,n),score:l,document:t},a.add(n)}}return i}r(85766)},92584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return o}});let n=r(43763);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return n.ReflectAdapter.get(t,a,o)},set(t,r,o,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,o,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,s??r,o,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},93889:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},94069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return l},appendMutableCookies:function(){return f},areCookiesMutableInCurrentPhase:function(){return p},getModifiedCookieValues:function(){return c},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return h}});let n=r(23158),o=r(43763),i=r(29294),a=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function c(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function f(e,t){let r=c(t);if(0===r.length)return!1;let o=new n.ResponseCookies(e),i=o.getAll();for(let e of r)o.set(e);for(let e of i)o.set(e);return!0}class d{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,l=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case u:return a;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{l()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{l()}};default:return o.ReflectAdapter.get(e,t,r)}}});return c}}function h(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return o.ReflectAdapter.get(e,r,n)}}});return t}function p(e){return"action"===e.phase}function m(e){if(!p((0,a.getExpectedRequestStore)(e)))throw new s}function g(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},95341:(e,t,r)=>{"use strict";r.d(t,{GB:()=>l,Pg:()=>c,x2:()=>u}),r(22317);var n=r(43210),o=r(11490),i=r(60687),a=(0,n.createContext)(null);function s(){let e=(0,n.useContext)(a);if(!e)throw Error("Missing sidebar provider");return e}function l(e){let[t,r]=void 0===e.open?(0,n.useState)(!1):[e.open,e.onOpenChange];return(0,i.jsx)(a.Provider,{value:(0,n.useMemo)(()=>({open:t,setOpen:r??(()=>void 0)}),[t,r]),children:e.children})}function u({as:e,...t}){let{open:r,setOpen:n}=s();return(0,i.jsx)(e??"button",{"aria-label":"Toggle Sidebar","data-open":r,onClick:()=>{n(!r)},...t})}function c({as:e,blockScrollingWidth:t,removeScrollOn:r=t?`(width < ${t}px)`:void 0,...a}){let{open:l}=s(),[u,c]=(0,n.useState)(!1);return(0,i.jsx)(o.A,{as:e??"aside","data-open":l,enabled:!!(u&&l),...a,children:a.children})}},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return i}});let n=r(98834),o=r(54674);function i(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let n=r(25232);function o(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96768:(e,t,r)=>{"use strict";function n(e,t,r){let{includePage:n=!0,includeSeparator:o=!1,includeRoot:i}=r,a=[];return t.forEach((e,r)=>{if("separator"===e.type&&o&&a.push({name:e.name}),"folder"===e.type){let n=t.at(r+1);if(n&&e.index===n)return;if(e.root){a=[];return}a.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&a.push({name:e.name,url:e.url})}),i&&a.unshift({name:e.name,url:"object"==typeof i?i.url:void 0}),a}r.d(t,{Pp:()=>n,oe:()=>function e(t,r){let n;for(let o of(r.endsWith("/")&&(r=r.slice(0,-1)),t)){if("separator"===o.type&&(n=o),"folder"===o.type){if(o.index?.url===r){let e=[];return n&&e.push(n),e.push(o,o.index),e}let t=e(o.children,r);if(t)return t.unshift(o),n&&t.unshift(n),t}if("page"===o.type&&o.url===r){let e=[];return n&&e.push(n),e.push(o),e}}return null}}),r(22317),r(43210)},96963:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n,o=r(43210),i=r(66156),a=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,r]=o.useState(a());return(0,i.N)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},97363:(e,t,r)=>{"use strict";r.d(t,{BaseLinkItem:()=>l});var n=r(60687),o=r(46250),i=r(66218),a=r(43210),s=r(349);let l=(0,a.forwardRef)(({item:e,...t},r)=>{let a=(0,i.a8)(),l=e.active??"url",u="none"!==l&&(0,s.$)(e.url,a,"nested-url"===l);return(0,n.jsx)(o.default,{ref:r,href:e.url,external:e.external,...t,"data-active":u,children:t.children})});l.displayName="BaseLinkItem"},97464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let a=i.length<=2,[s,l]=i,u=(0,o.createRouterCacheKey)(l),c=r.parallelRoutes.get(s),f=t.parallelRoutes.get(s);f&&f!==c||(f=new Map(c),t.parallelRoutes.set(s,f));let d=null==c?void 0:c.get(u),h=f.get(u);if(a){h&&h.lazyData&&h!==d||f.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!h||!d){h||f.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return h===d&&(h={lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading},f.set(u,h)),e(h,d,(0,n.getNextFlightSegmentPath)(i))}}});let n=r(74007),o=r(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return o.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),o=r(49026),i=r(62765),a=r(48976),s=r(70899),l=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(59008),r(57391),r(86770),r(2030),r(25232),r(59435),r(56928),r(89752),r(96493),r(68214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>i});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function a(...e){return n.useCallback(i(...e),e)}},98834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(19169);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:i}=(0,n.parsePath)(e);return""+t+r+o+i}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99933:(e,t,r)=>{"use strict";let n=r(94069),o=r(23158),i=r(29294),a=r(63033),s=r(84971),l=r(80023),u=r(68388),c=r(76926),f=(r(44523),r(8719)),d=new WeakMap;function h(e){let t=d.get(e);if(t)return t;let r=Promise.resolve(e);return d.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):y.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function p(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function y(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}}};