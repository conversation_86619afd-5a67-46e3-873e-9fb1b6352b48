"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[316],{244:(e,t,r)=>{function n(e,t,r){let{includePage:n=!0,includeSeparator:a=!1,includeRoot:l}=r,i=[];return t.forEach((e,r)=>{if("separator"===e.type&&a&&i.push({name:e.name}),"folder"===e.type){let n=t.at(r+1);if(n&&e.index===n)return;if(e.root){i=[];return}i.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&i.push({name:e.name,url:e.url})}),l&&i.unshift({name:e.name,url:"object"==typeof l?l.url:void 0}),i}r.d(t,{Pp:()=>n,oe:()=>function e(t,r){let n;for(let a of(r.endsWith("/")&&(r=r.slice(0,-1)),t)){if("separator"===a.type&&(n=a),"folder"===a.type){if(a.index?.url===r){let e=[];return n&&e.push(n),e.push(a,a.index),e}let t=e(a.children,r);if(t)return t.unshift(a),n&&t.unshift(n),t}if("page"===a.type&&a.url===r){let e=[];return n&&e.push(n),e.push(a),e}}return null}}),r(9189),r(2115)},263:(e,t,r)=>{r.d(t,{G:()=>c,c:()=>o});var n=r(5155),a=r(2115),l=r(344),i=r(5547),s=r(3259);let d=(0,l.q6)("SidebarContext");function o(){return d.use()}function c(e){let{children:t}=e,r=(0,a.useRef)(!0),[o,c]=(0,a.useState)(!1),[u,f]=(0,a.useState)(!1),h=(0,l.a8)();return(0,s.T)(h,()=>{r.current&&c(!1),r.current=!0}),(0,n.jsx)(d.Provider,{value:(0,a.useMemo)(()=>({open:o,setOpen:c,collapsed:u,setCollapsed:f,closeOnRedirect:r}),[o,u]),children:(0,n.jsx)(i.GB,{open:o,onOpenChange:c,children:t})})}},2397:(e,t,r)=>{r.r(t),r.d(t,{CollapsibleSidebar:()=>j,Sidebar:()=>N,SidebarCollapseTrigger:()=>L,SidebarFolder:()=>T,SidebarFolderContent:()=>A,SidebarFolderLink:()=>M,SidebarFolderTrigger:()=>Q,SidebarFooter:()=>P,SidebarHeader:()=>w,SidebarItem:()=>C,SidebarPageTree:()=>F,SidebarSeparator:()=>k,SidebarViewport:()=>S});var n=r(5155);let a=(0,r(3536).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var l=r(2829),i=r(5547),s=r(344),d=r(2115),o=r(9949),c=r(3259),u=r(9688),f=r(5675),h=r(5455),m=r(8758),x=r(263),p=r(2085),v=r(8693);let g=(0,p.F)("relative flex flex-row items-center gap-2 rounded-md p-2 text-start text-fd-muted-foreground [overflow-wrap:anywhere] md:py-1.5 [&_svg]:size-4 [&_svg]:shrink-0",{variants:{active:{true:"bg-fd-primary/10 text-fd-primary",false:"transition-colors hover:bg-fd-accent/50 hover:text-fd-accent-foreground/80 hover:transition-none"}}}),b=(0,d.createContext)(null),y=(0,d.createContext)(null);function j(e){let{collapsed:t}=(0,x.c)(),[r,a]=(0,d.useState)(!1),l=(0,d.useRef)(0),i=(0,d.useRef)(0);return(0,c.T)(t,()=>{a(!1),i.current=Date.now()+150}),(0,n.jsx)(N,{...e,onPointerEnter:e=>{!t||"touch"===e.pointerType||i.current>Date.now()||(window.clearTimeout(l.current),a(!0))},onPointerLeave:e=>{t&&"touch"!==e.pointerType&&(window.clearTimeout(l.current),l.current=window.setTimeout(()=>{a(!1),i.current=Date.now()+150},Math.min(e.clientX,document.body.clientWidth-e.clientX)>100?0:500))},"data-collapsed":t,className:(0,u.QP)("md:transition-all",t&&"md:-me-(--fd-sidebar-width) md:-translate-x-(--fd-sidebar-offset) rtl:md:translate-x-(--fd-sidebar-offset)",t&&r&&"z-50 md:translate-x-0",t&&!r&&"md:opacity-0",e.className),style:{"--fd-sidebar-offset":"calc(var(--fd-sidebar-width) - 6px)",...e.style}})}function N(e){let{defaultOpenLevel:t=0,prefetch:r=!0,inner:a,...l}=e,s=(0,d.useMemo)(()=>({defaultOpenLevel:t,prefetch:r,level:1}),[t,r]);return(0,n.jsx)(b.Provider,{value:s,children:(0,n.jsx)(i.Pg,{id:"nd-sidebar",removeScrollOn:"(width < 768px)",...l,className:(0,u.QP)("fixed top-[calc(var(--fd-banner-height)+var(--fd-nav-height))] z-20 bg-fd-card text-sm md:sticky md:h-(--fd-sidebar-height)","max-md:inset-x-0 max-md:bottom-0 max-md:bg-fd-background/80 max-md:text-[15px] max-md:backdrop-blur-lg max-md:data-[open=false]:invisible",l.className),style:{...l.style,"--fd-sidebar-height":"calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"},children:(0,n.jsx)("div",{...a,className:(0,u.QP)("flex size-full max-w-full flex-col pt-2 md:ms-auto md:w-(--fd-sidebar-width) md:border-e md:pt-4",null==a?void 0:a.className),children:l.children})})})}function w(e){return(0,n.jsx)("div",{...e,className:(0,u.QP)("flex flex-col gap-3 px-4 empty:hidden",e.className),children:e.children})}function P(e){return(0,n.jsx)("div",{...e,className:(0,u.QP)("flex flex-col border-t px-4 py-3 empty:hidden",e.className),children:e.children})}function S(e){return(0,n.jsx)(f.FK,{...e,className:(0,u.QP)("h-full",e.className),children:(0,n.jsx)(f.Gl,{className:"p-4",style:{maskImage:"linear-gradient(to bottom, transparent, white 12px)"},children:e.children})})}function k(e){let{level:t}=E();return(0,n.jsx)("p",{...e,className:(0,u.QP)("inline-flex items-center gap-2 mb-2 px-2 text-sm font-medium [&_svg]:size-4 [&_svg]:shrink-0",e.className),style:{paddingInlineStart:_(t),...e.style},children:e.children})}function C(e){let{icon:t,...r}=e,l=(0,s.a8)(),i=void 0!==r.href&&(0,h.$)(r.href,l,!1),{prefetch:d,level:c}=E();return(0,n.jsxs)(o.default,{...r,"data-active":i,className:(0,u.QP)(g({active:i}),r.className),prefetch:d,style:{paddingInlineStart:_(c),...r.style},children:[(0,n.jsx)($,{level:c,active:i}),null!=t?t:r.external?(0,n.jsx)(a,{}):null,r.children]})}function T(e){let{defaultOpen:t=!1,...r}=e,[a,l]=(0,d.useState)(t);return(0,c.T)(t,e=>{e&&l(e)}),(0,n.jsx)(m.Nt,{open:a,onOpenChange:l,...r,children:(0,n.jsx)(y.Provider,{value:(0,d.useMemo)(()=>({open:a,setOpen:l}),[a]),children:r.children})})}function Q(e){let{level:t}=E(),{open:r}=z();return(0,n.jsxs)(m.R6,{...e,className:(0,u.QP)(g({active:!1}),"w-full"),style:{paddingInlineStart:_(t),...e.style},children:[(0,n.jsx)($,{level:t}),e.children,(0,n.jsx)(l.A,{"data-icon":!0,className:(0,u.QP)("ms-auto transition-transform",!r&&"-rotate-90")})]})}function M(e){let{open:t,setOpen:r}=z(),{prefetch:a,level:i}=E(),d=(0,s.a8)(),c=void 0!==e.href&&(0,h.$)(e.href,d,!1);return(0,n.jsxs)(o.default,{...e,"data-active":c,className:(0,u.QP)(g({active:c}),"w-full",e.className),onClick:e=>{e.target.hasAttribute("data-icon")?(r(e=>!e),e.preventDefault()):r(e=>!c||!e)},prefetch:a,style:{paddingInlineStart:_(i),...e.style},children:[(0,n.jsx)($,{level:i,active:c}),e.children,(0,n.jsx)(l.A,{"data-icon":!0,className:(0,u.QP)("ms-auto transition-transform",!t&&"-rotate-90")})]})}function A(e){let t=E();return(0,n.jsx)(m.Ke,{...e,className:(0,u.QP)("relative",e.className),children:(0,n.jsxs)(b.Provider,{value:(0,d.useMemo)(()=>({...t,level:t.level+1}),[t]),children:[(0,n.jsx)("div",{className:"absolute w-px inset-y-0 bg-fd-border start-3"}),e.children]})})}function L(e){let{collapsed:t,setCollapsed:r}=(0,x.c)();return(0,n.jsx)("button",{type:"button","aria-label":"Collapse Sidebar","data-collapsed":t,...e,onClick:()=>{r(e=>!e)},children:e.children})}function z(){let e=(0,d.useContext)(y);if(!e)throw Error("Missing sidebar folder");return e}function E(){let e=(0,d.useContext)(b);if(!e)throw Error("<Sidebar /> component required.");return e}function F(e){let{root:t}=(0,v.t)();return(0,d.useMemo)(()=>{var r;let{Separator:a,Item:l,Folder:i}=null!=(r=e.components)?r:{};return(0,n.jsx)(d.Fragment,{children:function e(t,r){return t.map((t,s)=>{if("separator"===t.type)return a?(0,n.jsx)(a,{item:t},s):(0,n.jsxs)(k,{className:(0,u.QP)(0!==s&&"mt-8"),children:[t.icon,t.name]},s);if("folder"===t.type){let a=e(t.children,r+1);return i?(0,n.jsx)(i,{item:t,level:r,children:a},s):(0,n.jsx)(O,{item:t,children:a},s)}return l?(0,n.jsx)(l,{item:t},t.url):(0,n.jsx)(C,{href:t.url,external:t.external,icon:t.icon,children:t.name},t.url)})}(t.children,1)},t.$id)},[e.components,t])}function O(e){var t;let{item:r,...a}=e,{defaultOpenLevel:l,level:i}=E(),s=(0,v.L)();return(0,n.jsxs)(T,{defaultOpen:(null!=(t=r.defaultOpen)?t:l>=i)||s.includes(r),children:[r.index?(0,n.jsxs)(M,{href:r.index.url,external:r.index.external,...a,children:[r.icon,r.name]}):(0,n.jsxs)(Q,{...a,children:[r.icon,r.name]}),(0,n.jsx)(A,{children:a.children})]})}function _(e){return"calc(var(--spacing) * ".concat((e>1?e:0)*2+2,")")}function $(e){let{level:t,active:r}=e;return t<=1?null:(0,n.jsx)("div",{className:(0,u.QP)("absolute w-px inset-y-2 z-[2] start-3",r&&"bg-fd-primary")})}},3259:(e,t,r)=>{r.d(t,{T:()=>n.T});var n=r(5936);r(9189)},5547:(e,t,r)=>{r.d(t,{GB:()=>d,Pg:()=>c,x2:()=>o}),r(9189);var n=r(2115),a=r(1114),l=r(5155),i=(0,n.createContext)(null);function s(){let e=(0,n.useContext)(i);if(!e)throw Error("Missing sidebar provider");return e}function d(e){let[t,r]=void 0===e.open?(0,n.useState)(!1):[e.open,e.onOpenChange];return(0,l.jsx)(i.Provider,{value:(0,n.useMemo)(()=>({open:t,setOpen:null!=r?r:()=>void 0}),[t,r]),children:e.children})}function o(e){let{as:t,...r}=e,{open:n,setOpen:a}=s();return(0,l.jsx)(null!=t?t:"button",{"aria-label":"Toggle Sidebar","data-open":n,onClick:()=>{a(!n)},...r})}function c(e){let{as:t,blockScrollingWidth:r,removeScrollOn:i=r?"(width < ".concat(r,"px)"):void 0,...d}=e,{open:o}=s(),[c,u]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{if(!i)return;let e=window.matchMedia(i),t=()=>{u(e.matches)};return t(),e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}},[i]),(0,l.jsx)(a.A,{as:null!=t?t:"aside","data-open":o,enabled:!!(c&&o),...d,children:d.children})}},5936:(e,t,r)=>{r.d(t,{T:()=>a});var n=r(2115);function a(e,t,r=function e(t,r){if(Array.isArray(t)&&Array.isArray(r))return r.length!==t.length||t.some((t,n)=>e(t,r[n]));if("object"==typeof t&&t&&"object"==typeof r&&r){let n=Object.keys(t),a=Object.keys(r);return n.length!==a.length||n.some(n=>e(t[n],r[n]))}return t!==r}){let[l,i]=(0,n.useState)(e);r(l,e)&&(t(e,l),i(e))}},6563:(e,t,r)=>{r.d(t,{LayoutTab:()=>b,LayoutTabs:()=>v,Navbar:()=>x,NavbarSidebarTrigger:()=>p,SidebarLayoutTab:()=>y});var n=r(5155),a=r(9688),l=r(263),i=r(1339),s=r(5547),d=r(7936),o=r(3536);let c=(0,o.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),u=(0,o.A)("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var f=r(9949),h=r(344),m=r(5455);function x(e){let{mode:t,...r}=e,{open:s,collapsed:d}=(0,l.c)(),{isTransparent:o}=(0,i.h)();return(0,n.jsx)("header",{id:"nd-subnav",...r,className:(0,a.QP)("fixed inset-x-0 top-(--fd-banner-height) z-10 px-(--fd-layout-offset) backdrop-blur-sm transition-colors",(!o||s)&&"bg-fd-background/80","auto"===t&&!d&&"ps-[calc(var(--fd-layout-offset)+var(--fd-sidebar-width))]",r.className),children:r.children})}function p(e){let{open:t}=(0,l.c)();return(0,n.jsx)(s.x2,{...e,className:(0,a.QP)((0,d.r)({color:"ghost",size:"icon"}),e.className),children:t?(0,n.jsx)(c,{}):(0,n.jsx)(u,{})})}function v(e){return(0,n.jsx)("div",{...e,className:(0,a.QP)("flex flex-row items-end gap-6 overflow-auto",e.className),children:e.children})}function g(e){let t=(0,h.a8)();return e.urls?e.urls.has(t.endsWith("/")?t.slice(0,-1):t):(0,m.$)(e.url,t,!0)}function b(e){let{closeOnRedirect:t}=(0,l.c)(),r=g(e);return(0,n.jsx)(f.default,{className:(0,a.QP)("inline-flex items-center py-2.5 border-b border-transparent gap-2 text-fd-muted-foreground text-sm text-nowrap",r&&"text-fd-foreground font-medium border-fd-primary"),href:e.url,onClick:()=>{t.current=!1},children:e.title})}function y(e){let{item:t,...r}=e,l=g(t);return(0,n.jsxs)(f.default,{...r,className:(0,a.QP)("flex flex-row items-center px-2 -mx-2 py-1.5 gap-2.5 text-fd-muted-foreground [&_svg]:!size-4.5",l?"text-fd-primary font-medium":"hover:text-fd-accent-foreground",r.className),"data-active":l,href:t.url,children:[t.icon,t.title]})}},8693:(e,t,r)=>{r.d(t,{L:()=>c,TreeContextProvider:()=>o,t:()=>u});var n=r(5155),a=r(344),l=r(2115),i=r(244);let s=(0,a.q6)("TreeContext"),d=(0,a.q6)("PathContext",[]);function o(e){var t,r,o;let c=(0,l.useRef)(0),u=(0,a.a8)(),f=(0,l.useMemo)(()=>e.tree,[null!=(t=e.tree.$id)?t:e.tree]),h=(0,l.useMemo)(()=>{var e;return null!=(e=(0,i.oe)(f.children,u))?e:[]},[f,u]),m=null!=(r=h.findLast(e=>"folder"===e.type&&e.root))?r:f;return null!=m.$id||(m.$id=String(c.current++)),(0,n.jsx)(s.Provider,{value:(0,l.useMemo)(()=>({root:m}),[m]),children:(0,n.jsx)(d.Provider,{value:h,children:e.children})})}function c(){return d.use()}function u(){return s.use("You must wrap this component under <DocsLayout />")}},8870:(e,t,r)=>{r.d(t,{RootToggle:()=>f});var n=r(5155);let a=(0,r(3536).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);var l=r(2115),i=r(9949),s=r(344),d=r(9688),o=r(5455),c=r(263),u=r(408);function f(e){let{options:t,placeholder:r,...f}=e,[m,x]=(0,l.useState)(!1),{closeOnRedirect:p}=(0,c.c)(),v=(0,s.a8)(),g=(0,l.useMemo)(()=>t.findLast(e=>e.urls?e.urls.has(v.endsWith("/")?v.slice(0,-1):v):(0,o.$)(e.url,v,!0)),[t,v]),b=()=>{p.current=!1,x(!1)},y=g?(0,n.jsx)(h,{...g}):r;return(0,n.jsxs)(u.Popover,{open:m,onOpenChange:x,children:[y?(0,n.jsxs)(u.PopoverTrigger,{...f,className:(0,d.QP)("flex items-center gap-2.5 rounded-lg pe-2 hover:text-fd-accent-foreground",f.className),children:[y,(0,n.jsx)(a,{className:"size-4 text-fd-muted-foreground"})]}):null,(0,n.jsx)(u.PopoverContent,{className:"w-(--radix-popover-trigger-width) overflow-hidden p-0",children:t.map(e=>{var t;return(0,n.jsx)(i.default,{href:e.url,onClick:b,...e.props,className:(0,d.QP)("flex w-full flex-row items-center gap-2 px-2 py-1.5",g===e?"bg-fd-accent text-fd-accent-foreground":"hover:bg-fd-accent/50",null==(t=e.props)?void 0:t.className),children:(0,n.jsx)(h,{...e})},e.url)})})]})}function h(e){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(n.Fragment,{children:e.icon}),(0,n.jsxs)("div",{className:"flex-1 text-start",children:[(0,n.jsx)("p",{className:"text-sm font-medium",children:e.title}),e.description?(0,n.jsx)("p",{className:"text-xs text-fd-muted-foreground",children:e.description}):null]})]})}}}]);