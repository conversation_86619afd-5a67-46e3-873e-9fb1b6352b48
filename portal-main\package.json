{"name": "portal", "version": "0.0.0", "private": true, "scripts": {"prebuild": "node scripts/build-github-stars.js && node scripts/build-search-index.js", "build": "next build", "build:static": "node scripts/build-static.js", "export": "npm run build:static", "dev": "next dev", "start": "next start", "serve": "npx serve out", "postinstall": "fumadocs-mdx"}, "dependencies": {"@orama/orama": "^3.1.4", "@orama/stopwords": "^3.1.4", "@orama/tokenizers": "^3.1.4", "@radix-ui/react-slot": "^1.2.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fumadocs-core": "^15.2.8", "fumadocs-mdx": "^11.6.6", "fumadocs-ui": "^15.2.8", "lodash": "^4.17.21", "lucide-react": "^0.486.0", "motion": "^12.7.4", "next": "^15.3.1", "next-intl": "^4.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.16", "@types/mdx": "^2.0.13", "@types/node": "22.13.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "eslint": "^8", "eslint-config-next": "15.2.4", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.4", "tailwindcss-motion": "^1.1.0", "typescript": "^5.8.2"}}