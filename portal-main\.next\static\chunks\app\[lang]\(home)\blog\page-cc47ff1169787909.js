(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[103],{469:(e,t,n)=>{"use strict";n.d(t,{DT:()=>f,FP:()=>u,TK:()=>i,Zn:()=>o,aM:()=>l,x3:()=>a});var r=n(3225);function i(e){return"string"==typeof e?{pathname:e}:e}function o(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}function u({pathname:e,locale:t,params:n,pathnames:i,query:u}){function l(e){let t=i[e];return t||(t=e),t}function f(e,i){let l=(0,r.Wl)(e,t,i);return n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),l=l.replace(RegExp(n,"g"),r)}),l=l.replace(/\[\[\.\.\..+\]\]/g,""),l=(0,r.po)(l),u&&(l+=o(u)),l}if("string"==typeof e)return f(l(e),e);{let{pathname:t,...n}=e;return{...n,pathname:f(l(t),t)}}}function l(e,t,n){let i=(0,r.FD)(Object.keys(n)),o=decodeURI(t);for(let t of i){let i=n[t];if("string"==typeof i){if((0,r.ql)(i,o))return t}else if((0,r.ql)((0,r.Wl)(i,e,t),o))return t}return t}function f(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function a(e,t,n,i){let o,{mode:u}=n.localePrefix;return void 0!==i?o=i:(0,r._x)(e)&&("always"===u?o=!0:"as-needed"===u&&(o=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),o?(0,r.PJ)((0,r.XP)(t,n.localePrefix),e):e}},1839:(e,t,n)=>{Promise.resolve().then(n.bind(n,8532)),Promise.resolve().then(n.bind(n,6160)),Promise.resolve().then(n.bind(n,6096))},3225:(e,t,n)=>{"use strict";function r(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function i(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function o(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function u(e,t){return t===e||t.startsWith(`${e}/`)}function l(e,t,n){return"string"==typeof e?e:e[t]||n}function f(e){let t=function(){try{return!0}catch{return!1}}();if("/"!==e){let n=e.endsWith("/");t&&!n?e+="/":!t&&n&&(e=e.slice(0,-1))}return e}function a(e,t){let n=f(e),r=f(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(n).test(r)}function c(e,t){return"never"!==t.mode&&t.prefixes?.[e]||s(e)}function s(e){return"/"+e}function p(e){return e.includes("[[...")}function h(e){return e.includes("[...")}function d(e){return e.includes("[")}function m(e,t){let n=e.split("/"),r=t.split("/"),i=Math.max(n.length,r.length);for(let e=0;e<i;e++){let t=n[e],i=r[e];if(!t&&i)return -1;if(t&&!i)return 1;if(t||i){if(!d(t)&&d(i))return -1;if(d(t)&&!d(i))return 1;if(!h(t)&&h(i))return -1;if(h(t)&&!h(i))return 1;if(!p(t)&&p(i))return -1;if(p(t)&&!p(i))return 1}}return 0}function g(e){return e.sort(m)}function y(e){return"function"==typeof e.then}n.d(t,{FD:()=>g,MY:()=>i,PJ:()=>o,Wl:()=>l,XP:()=>c,_x:()=>r,bL:()=>s,po:()=>f,ql:()=>a,wO:()=>u,yL:()=>y})},6096:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(6453),i=n(5155);function o(e){let{locale:t,...n}=e;if(!t)throw Error(void 0);return(0,i.jsx)(r.Dk,{locale:t,...n})}},6160:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(6874),i=n(5695),o=n(2115),u=n(6453),l=n(8986),f=n(5155),a=(0,o.forwardRef)(function(e,t){let{href:n,locale:o,localeCookie:a,onClick:c,prefetch:s,...p}=e,h=(0,u.Ym)(),d=null!=o&&o!==h,m=(0,i.usePathname)();return d&&(s=!1),(0,f.jsx)(r,{ref:t,href:n,hrefLang:d?o:void 0,onClick:function(e){(0,l.A)(a,m,h,o),c&&c(e)},prefetch:s,...p})})},8986:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(469);function i(e,t,n,i){if(!e||i===n||null==i||!t)return;let o=(0,r.DT)(t),{name:u,...l}=e;l.path||(l.path=""!==o?o:"/");let f=`${u}=${i};`;for(let[e,t]of Object.entries(l))f+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(f+="="+t),f+=";";document.cookie=f}}},e=>{var t=t=>e(e.s=t);e.O(0,[93,453,808,244,532,441,684,358],()=>t(1839)),_N_E=e.O()}]);