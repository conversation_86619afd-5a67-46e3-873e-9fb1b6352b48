"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_fetch-W5EHIBOE_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/fetch-W5EHIBOE.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/fetch-W5EHIBOE.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchDocs: () => (/* binding */ fetchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n\n\n// src/search/client/fetch.ts\nvar cache = /* @__PURE__ */ new Map();\nasync function fetchDocs(query, locale, tag, options) {\n  const params = new URLSearchParams();\n  params.set(\"query\", query);\n  if (locale) params.set(\"locale\", locale);\n  if (tag) params.set(\"tag\", tag);\n  const key = `${options.api ?? \"/api/search\"}?${params}`;\n  const cached = cache.get(key);\n  if (cached) return cached;\n  const res = await fetch(key);\n  if (!res.ok) throw new Error(await res.text());\n  const result = await res.json();\n  cache.set(key, result);\n  return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZmV0Y2gtVzVFSElCT0UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkI7O0FBRTdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLDZCQUE2QixHQUFHLE9BQU87QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxmdW1hZG9jcy1jb3JlXFxkaXN0XFxmZXRjaC1XNUVISUJPRS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgXCIuL2NodW5rLU1MS0dBQk1LLmpzXCI7XG5cbi8vIHNyYy9zZWFyY2gvY2xpZW50L2ZldGNoLnRzXG52YXIgY2FjaGUgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuYXN5bmMgZnVuY3Rpb24gZmV0Y2hEb2NzKHF1ZXJ5LCBsb2NhbGUsIHRhZywgb3B0aW9ucykge1xuICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gIHBhcmFtcy5zZXQoXCJxdWVyeVwiLCBxdWVyeSk7XG4gIGlmIChsb2NhbGUpIHBhcmFtcy5zZXQoXCJsb2NhbGVcIiwgbG9jYWxlKTtcbiAgaWYgKHRhZykgcGFyYW1zLnNldChcInRhZ1wiLCB0YWcpO1xuICBjb25zdCBrZXkgPSBgJHtvcHRpb25zLmFwaSA/PyBcIi9hcGkvc2VhcmNoXCJ9PyR7cGFyYW1zfWA7XG4gIGNvbnN0IGNhY2hlZCA9IGNhY2hlLmdldChrZXkpO1xuICBpZiAoY2FjaGVkKSByZXR1cm4gY2FjaGVkO1xuICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaChrZXkpO1xuICBpZiAoIXJlcy5vaykgdGhyb3cgbmV3IEVycm9yKGF3YWl0IHJlcy50ZXh0KCkpO1xuICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXMuanNvbigpO1xuICBjYWNoZS5zZXQoa2V5LCByZXN1bHQpO1xuICByZXR1cm4gcmVzdWx0O1xufVxuZXhwb3J0IHtcbiAgZmV0Y2hEb2NzXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/fetch-W5EHIBOE.js\n"));

/***/ })

}]);