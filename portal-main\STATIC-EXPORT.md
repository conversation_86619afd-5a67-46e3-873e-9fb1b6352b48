# 静态导出指南

本项目已配置为支持完全静态导出，可以部署到任何静态托管服务（如 GitHub Pages、Netlify、Vercel Static 等）。

## 🚀 快速开始

### 构建静态网站

```bash
# 完整的静态导出构建
npm run build:static

# 或者使用别名
npm run export
```

### 本地预览

```bash
# 预览静态网站
npm run serve

# 或者手动使用serve
npx serve out
```

## 📁 输出结构

静态导出会生成以下文件结构：

```
out/
├── index.html                 # 重定向到默认语言
├── en/                        # 英文版本
│   ├── index.html
│   ├── docs/
│   └── blog/
├── cn/                        # 中文版本
│   ├── index.html
│   ├── docs/
│   └── blog/
├── github-stars.json          # GitHub星标数据
├── search-index.json          # 搜索索引
├── _next/                     # Next.js静态资源
└── ...
```

## 🔧 技术实现

### 1. API路由替换

原来的服务端API路由已被替换为构建时生成的静态数据：

- **GitHub星标API** → `public/github-stars.json`
- **搜索API** → `public/search-index.json` + 客户端搜索

### 2. 搜索功能

- 使用 **Orama** 搜索引擎的客户端实现
- 构建时生成搜索索引，支持中英文分词
- 完全在浏览器中运行，无需服务器

### 3. 国际化支持

- 支持中文 (`cn`) 和英文 (`en`) 
- 静态生成所有语言版本的页面
- 客户端路由切换

### 4. 构建流程

1. **预构建阶段**:
   - 获取GitHub星标数据 (`scripts/build-github-stars.js`)
   - 生成搜索索引 (`scripts/build-search-index.js`)

2. **Next.js构建**:
   - 静态生成所有页面
   - 优化资源文件
   - 生成路由映射

3. **后处理**:
   - 验证输出文件
   - 生成部署报告

## 🌐 部署选项

### GitHub Pages

```bash
# 构建
npm run build:static

# 部署到gh-pages分支
npx gh-pages -d out
```

### Netlify

1. 连接GitHub仓库
2. 设置构建命令: `npm run build:static`
3. 设置发布目录: `out`

### Vercel (静态)

```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
vercel --prod out
```

### 自定义服务器

将 `out/` 目录的内容上传到任何支持静态文件的Web服务器。

## ⚙️ 配置选项

### 环境变量

```bash
# GitHub API Token (可选，用于避免API限流)
GITHUB_TOKEN=your_github_token_here
```

### 自定义配置

编辑以下文件来自定义构建：

- `scripts/build-github-stars.js` - GitHub星标数据获取
- `scripts/build-search-index.js` - 搜索索引生成
- `next.config.mjs` - Next.js配置

## 🔍 搜索功能详情

### 支持的搜索特性

- ✅ 全文搜索
- ✅ 中英文分词
- ✅ 模糊匹配
- ✅ 结果高亮
- ✅ 按类型筛选（文档/博客）
- ✅ 键盘快捷键 (`Cmd/Ctrl + K`)

### 搜索索引内容

- 文档标题和内容
- 博客文章标题和内容
- 元数据（描述、标签等）

## 📊 性能优化

### 构建优化

- 图片优化已禁用（静态导出要求）
- 启用了资源压缩
- 生成了预加载提示

### 运行时优化

- 搜索索引按需加载
- 客户端缓存GitHub星标数据
- 懒加载非关键资源

## 🐛 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理缓存重试
   rm -rf .next out
   npm run build:static
   ```

2. **搜索不工作**
   - 检查 `public/search-index.json` 是否存在
   - 确认浏览器控制台无错误

3. **GitHub星标显示错误**
   - 检查 `public/github-stars.json` 是否存在
   - 验证网络连接

### 调试模式

```bash
# 启用详细日志
DEBUG=1 npm run build:static
```

## 📝 更新内容

要更新静态网站内容：

1. 修改 `content/` 目录中的Markdown文件
2. 运行 `npm run build:static` 重新构建
3. 部署新的 `out/` 目录

## 🤝 贡献

如果您发现静态导出功能的问题或有改进建议，请提交Issue或Pull Request。
