// @ts-nocheck -- skip type checking
import * as docs_131 from "../content/docs/ten_agent/playground/use-cases/voice-assistant/run_dify.md?collection=docs&hash=1755487355000"
import * as docs_130 from "../content/docs/ten_agent/playground/use-cases/voice-assistant/run_dify.cn.md?collection=docs&hash=1755487355000"
import * as docs_129 from "../content/docs/ten_agent/playground/use-cases/voice-assistant/run_coze.md?collection=docs&hash=1755487355000"
import * as docs_128 from "../content/docs/ten_agent/playground/use-cases/voice-assistant/run_coze.cn.md?collection=docs&hash=1755487355000"
import * as docs_127 from "../content/docs/ten_agent/playground/use-cases/voice-assistant/index.md?collection=docs&hash=1755487355000"
import * as docs_126 from "../content/docs/ten_agent/playground/use-cases/voice-assistant/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_125 from "../content/docs/ten_agent/playground/use-cases/run_story_telling.md?collection=docs&hash=1755487355000"
import * as docs_124 from "../content/docs/ten_agent/playground/use-cases/run_story_telling.cn.md?collection=docs&hash=1755487355000"
import * as docs_123 from "../content/docs/ten_framework/tman_designer/overview.md?collection=docs&hash=1755487355000"
import * as docs_122 from "../content/docs/ten_framework/tman_designer/overview.cn.md?collection=docs&hash=1755487355000"
import * as docs_121 from "../content/docs/ten_framework/tman_designer/doc_window.md?collection=docs&hash=1755487355000"
import * as docs_120 from "../content/docs/ten_framework/tman_designer/doc_window.cn.md?collection=docs&hash=1755487355000"
import * as docs_119 from "../content/docs/ten_framework/standard_extension/standard_interface.md?collection=docs&hash=1755487355000"
import * as docs_118 from "../content/docs/ten_framework/standard_extension/standard_interface.cn.md?collection=docs&hash=1755487355000"
import * as docs_117 from "../content/docs/ten_framework/ten_manager/overview.mdx?collection=docs&hash=1755487355000"
import * as docs_116 from "../content/docs/ten_framework/ten_manager/overview.cn.mdx?collection=docs&hash=1755487355000"
import * as docs_115 from "../content/docs/ten_framework/ten_manager/check_graph.md?collection=docs&hash=1755487355000"
import * as docs_114 from "../content/docs/ten_framework/ten_manager/check_graph.cn.md?collection=docs&hash=1755487355000"
import * as docs_113 from "../content/docs/ten_framework/software_packages/python_async_extension.md?collection=docs&hash=1755487355000"
import * as docs_112 from "../content/docs/ten_framework/software_packages/python_async_extension.cn.md?collection=docs&hash=1755487355000"
import * as docs_111 from "../content/docs/ten_framework/software_packages/overview.md?collection=docs&hash=1755487355000"
import * as docs_110 from "../content/docs/ten_framework/software_packages/overview.cn.md?collection=docs&hash=1755487355000"
import * as docs_109 from "../content/docs/ten_framework/software_packages/extension.md?collection=docs&hash=1755487355000"
import * as docs_108 from "../content/docs/ten_framework/software_packages/extension.cn.md?collection=docs&hash=1755487355000"
import * as docs_107 from "../content/docs/ten_framework/software_packages/app.md?collection=docs&hash=1755487355000"
import * as docs_106 from "../content/docs/ten_framework/software_packages/app.cn.md?collection=docs&hash=1755487355000"
import * as docs_105 from "../content/docs/ten_framework/software_packages/addon_system.md?collection=docs&hash=1755487355000"
import * as docs_104 from "../content/docs/ten_framework/software_packages/addon_system.cn.md?collection=docs&hash=1755487355000"
import * as docs_103 from "../content/docs/ten_framework/testing_and_debugging/how_to_debug_with_logs.md?collection=docs&hash=1755487355000"
import * as docs_102 from "../content/docs/ten_framework/testing_and_debugging/how_to_debug_with_logs.cn.md?collection=docs&hash=1755487355000"
import * as docs_101 from "../content/docs/ten_framework/testing_and_debugging/for_users_of_ten_framework.md?collection=docs&hash=1755487355000"
import * as docs_100 from "../content/docs/ten_framework/testing_and_debugging/for_users_of_ten_framework.cn.md?collection=docs&hash=1755487355000"
import * as docs_99 from "../content/docs/ten_framework/testing_and_debugging/for_developers_of_ten_framework.md?collection=docs&hash=1755487355000"
import * as docs_98 from "../content/docs/ten_framework/testing_and_debugging/for_developers_of_ten_framework.cn.md?collection=docs&hash=1755487355000"
import * as docs_97 from "../content/docs/ten_framework/testing_and_debugging/debugging.md?collection=docs&hash=1755487355000"
import * as docs_96 from "../content/docs/ten_framework/testing_and_debugging/debugging.cn.md?collection=docs&hash=1755487355000"
import * as docs_95 from "../content/docs/ten_framework/metadata_system/property.md?collection=docs&hash=1755487355000"
import * as docs_94 from "../content/docs/ten_framework/metadata_system/property.cn.md?collection=docs&hash=1755487355000"
import * as docs_93 from "../content/docs/ten_framework/metadata_system/metadata_system.md?collection=docs&hash=1755487355000"
import * as docs_92 from "../content/docs/ten_framework/metadata_system/metadata_system.cn.md?collection=docs&hash=1755487355000"
import * as docs_91 from "../content/docs/ten_framework/metadata_system/manifest.md?collection=docs&hash=1755487355000"
import * as docs_90 from "../content/docs/ten_framework/metadata_system/manifest.cn.md?collection=docs&hash=1755487355000"
import * as docs_89 from "../content/docs/ten_framework/language_binding/go.md?collection=docs&hash=1755487355000"
import * as docs_88 from "../content/docs/ten_framework/language_binding/go.cn.md?collection=docs&hash=1755487355000"
import * as docs_87 from "../content/docs/ten_framework/development/how_to_develop_with_ext.md?collection=docs&hash=1755487355000"
import * as docs_86 from "../content/docs/ten_framework/development/how_to_develop_with_ext.cn.md?collection=docs&hash=1755487355000"
import * as docs_85 from "../content/docs/ten_framework/development/development_process.md?collection=docs&hash=1755487355000"
import * as docs_84 from "../content/docs/ten_framework/development/development_process.cn.md?collection=docs&hash=1755487355000"
import * as docs_83 from "../content/docs/ten_framework/graph/msg_conversion.md?collection=docs&hash=1755487355000"
import * as docs_82 from "../content/docs/ten_framework/graph/msg_conversion.cn.md?collection=docs&hash=1755487355000"
import * as docs_81 from "../content/docs/ten_framework/graph/graph.md?collection=docs&hash=1755487355000"
import * as docs_80 from "../content/docs/ten_framework/graph/graph.cn.md?collection=docs&hash=1755487355000"
import * as docs_79 from "../content/docs/ten_framework/api/type_system.md?collection=docs&hash=1755487355000"
import * as docs_78 from "../content/docs/ten_framework/api/type_system.cn.md?collection=docs&hash=1755487355000"
import * as docs_77 from "../content/docs/ten_framework/api/required.md?collection=docs&hash=1755487355000"
import * as docs_76 from "../content/docs/ten_framework/api/required.cn.md?collection=docs&hash=1755487355000"
import * as docs_75 from "../content/docs/ten_framework/api/log.md?collection=docs&hash=1755487355000"
import * as docs_74 from "../content/docs/ten_framework/api/log.cn.md?collection=docs&hash=1755487355000"
import * as docs_73 from "../content/docs/ten_framework/api/api.md?collection=docs&hash=1755487355000"
import * as docs_72 from "../content/docs/ten_framework/api/api.cn.md?collection=docs&hash=1755487355000"
import * as docs_71 from "../content/docs/ten_framework/ai_model/how_to_run_local_model_in_python_extensions.md?collection=docs&hash=1755487355000"
import * as docs_70 from "../content/docs/ten_framework/ai_model/how_to_run_local_model_in_python_extensions.cn.md?collection=docs&hash=1755487355000"
import * as docs_69 from "../content/docs/ten_agent/tutorials/how_to_run_local_model_in_ollama.md?collection=docs&hash=1755487355000"
import * as docs_68 from "../content/docs/ten_agent/setup_development_env/setting_up_vscode_for_development_inside_container.md?collection=docs&hash=1755487355000"
import * as docs_67 from "../content/docs/ten_agent/setup_development_env/setting_up_vscode_for_development_inside_container.cn.md?collection=docs&hash=1755487355000"
import * as docs_66 from "../content/docs/ten_agent/setup_development_env/setting_up_development_inside_codespace.md?collection=docs&hash=1755487355000"
import * as docs_65 from "../content/docs/ten_agent/setup_development_env/setting_up_development_inside_codespace.cn.md?collection=docs&hash=1755487355000"
import * as docs_64 from "../content/docs/ten_agent/setup_development_env/index.md?collection=docs&hash=1755487355000"
import * as docs_63 from "../content/docs/ten_agent/setup_development_env/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_62 from "../content/docs/ten_agent/project_structure/server.md?collection=docs&hash=1755487355000"
import * as docs_61 from "../content/docs/ten_agent/project_structure/server.cn.md?collection=docs&hash=1755487355000"
import * as docs_60 from "../content/docs/ten_agent/project_structure/property_json.md?collection=docs&hash=1755487355000"
import * as docs_59 from "../content/docs/ten_agent/project_structure/property_json.cn.md?collection=docs&hash=1755487355000"
import * as docs_58 from "../content/docs/ten_agent/project_structure/index.md?collection=docs&hash=1755487355000"
import * as docs_57 from "../content/docs/ten_agent/project_structure/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_56 from "../content/docs/ten_agent/project_structure/extension.md?collection=docs&hash=1755487355000"
import * as docs_55 from "../content/docs/ten_agent/project_structure/extension.cn.md?collection=docs&hash=1755487355000"
import * as docs_54 from "../content/docs/ten_agent/playground/index.md?collection=docs&hash=1755487355000"
import * as docs_53 from "../content/docs/ten_agent/playground/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_52 from "../content/docs/ten_agent/playground/configure_properties.md?collection=docs&hash=1755487355000"
import * as docs_51 from "../content/docs/ten_agent/playground/configure_properties.cn.md?collection=docs&hash=1755487355000"
import * as docs_50 from "../content/docs/ten_agent/playground/configure_modules.md?collection=docs&hash=1755487355000"
import * as docs_49 from "../content/docs/ten_agent/playground/configure_modules.cn.md?collection=docs&hash=1755487355000"
import * as docs_48 from "../content/docs/ten_agent/playground/change_language.md?collection=docs&hash=1755487355000"
import * as docs_47 from "../content/docs/ten_agent/playground/change_language.cn.md?collection=docs&hash=1755487355000"
import * as docs_46 from "../content/docs/ten_agent/demo/index.md?collection=docs&hash=1755487355000"
import * as docs_45 from "../content/docs/ten_agent/demo/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_44 from "../content/docs/ten_agent/deploy_ten_agent/deploy_agent_service.md?collection=docs&hash=1755487355000"
import * as docs_43 from "../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_Tool_extension.md?collection=docs&hash=1755487355000"
import * as docs_42 from "../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_Tool_extension.cn.md?collection=docs&hash=1755487355000"
import * as docs_41 from "../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_extension.md?collection=docs&hash=1755487355000"
import * as docs_40 from "../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_extension.cn.md?collection=docs&hash=1755487355000"
import * as docs_39 from "../content/docs/ten_agent/create_an_extension_with_predefined_type/index.md?collection=docs&hash=1755487355000"
import * as docs_38 from "../content/docs/ten_agent/create_an_extension_with_predefined_type/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_37 from "../content/docs/ten_framework/version_system.md?collection=docs&hash=1755487355000"
import * as docs_36 from "../content/docs/ten_framework/version_system.cn.md?collection=docs&hash=1755487355000"
import * as docs_35 from "../content/docs/ten_framework/profiling.md?collection=docs&hash=1755487355000"
import * as docs_34 from "../content/docs/ten_framework/profiling.cn.md?collection=docs&hash=1755487355000"
import * as docs_33 from "../content/docs/ten_framework/preparation.md?collection=docs&hash=1755487355000"
import * as docs_32 from "../content/docs/ten_framework/preparation.cn.md?collection=docs&hash=1755487355000"
import * as docs_31 from "../content/docs/ten_framework/message_system.md?collection=docs&hash=1755487355000"
import * as docs_30 from "../content/docs/ten_framework/message_system.cn.md?collection=docs&hash=1755487355000"
import * as docs_29 from "../content/docs/ten_framework/dependencies.md?collection=docs&hash=1755487355000"
import * as docs_28 from "../content/docs/ten_framework/dependencies.cn.md?collection=docs&hash=1755487355000"
import * as docs_27 from "../content/docs/ten_framework/concept_overview.md?collection=docs&hash=1755487355000"
import * as docs_26 from "../content/docs/ten_framework/concept_overview.cn.md?collection=docs&hash=1755487355000"
import * as docs_25 from "../content/docs/ten_framework/cloud_store.md?collection=docs&hash=1755487355000"
import * as docs_24 from "../content/docs/ten_framework/cloud_store.cn.md?collection=docs&hash=1755487355000"
import * as docs_23 from "../content/docs/ten_framework/build_system.md?collection=docs&hash=1755487355000"
import * as docs_22 from "../content/docs/ten_framework/build_system.cn.md?collection=docs&hash=1755487355000"
import * as docs_21 from "../content/docs/ten_agent/overview.md?collection=docs&hash=1755487355000"
import * as docs_20 from "../content/docs/ten_agent/overview.cn.md?collection=docs&hash=1755487355000"
import * as docs_19 from "../content/docs/ten_agent/how_does_interrupt_work.md?collection=docs&hash=1755487355000"
import * as docs_18 from "../content/docs/ten_agent/how_does_interrupt_work.cn.md?collection=docs&hash=1755487355000"
import * as docs_17 from "../content/docs/ten_agent/getting_started.mdx?collection=docs&hash=1755487355000"
import * as docs_16 from "../content/docs/ten_agent/getting_started.cn.mdx?collection=docs&hash=1755487355000"
import * as docs_15 from "../content/docs/ten_agent/faqs.md?collection=docs&hash=1755487355000"
import * as docs_14 from "../content/docs/ten_agent/faqs.cn.md?collection=docs&hash=1755487355000"
import * as docs_13 from "../content/docs/ten_agent/customize_your_agent.md?collection=docs&hash=1755487355000"
import * as docs_12 from "../content/docs/ten_agent/customize_your_agent.cn.md?collection=docs&hash=1755487355000"
import * as docs_11 from "../content/docs/ten_agent/create_a_hello_world_extension.mdx?collection=docs&hash=1755487355000"
import * as docs_10 from "../content/docs/ten_agent/create_a_hello_world_extension.cn.mdx?collection=docs&hash=1755487355000"
import * as docs_9 from "../content/docs/ten_agent/architecture_flow.md?collection=docs&hash=1755487355000"
import * as docs_8 from "../content/docs/ten_agent/architecture_flow.cn.md?collection=docs&hash=1755487355000"
import * as docs_7 from "../content/docs/key_concepts.md?collection=docs&hash=1755487355000"
import * as docs_6 from "../content/docs/key_concepts.cn.md?collection=docs&hash=1755487355000"
import * as docs_5 from "../content/docs/index.mdx?collection=docs&hash=1755487355000"
import * as docs_4 from "../content/docs/index.cn.md?collection=docs&hash=1755487355000"
import * as docs_3 from "../content/docs/goal.md?collection=docs&hash=1755487355000"
import * as docs_2 from "../content/docs/goal.cn.md?collection=docs&hash=1755487355000"
import * as docs_1 from "../content/docs/application_scenarios.md?collection=docs&hash=1755487355000"
import * as docs_0 from "../content/docs/application_scenarios.cn.md?collection=docs&hash=1755487355000"
import * as blogPosts_3 from "../content/blog/tman-designer-of-ten-framework.mdx?collection=blogPosts&hash=1755487355000"
import * as blogPosts_2 from "../content/blog/ten-world-first-truly-real-time-multimodal-agent-framework.mdx?collection=blogPosts&hash=1755487355000"
import * as blogPosts_1 from "../content/blog/exploring-the-ai-sales-avatar.mdx?collection=blogPosts&hash=1755487355000"
import * as blogPosts_0 from "../content/blog/deepgram-deepseek-fish-build-your-own-voice-agent.mdx?collection=blogPosts&hash=1755487355000"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const blogPosts = _runtime.doc<typeof _source.blogPosts>([{ info: {"path":"deepgram-deepseek-fish-build-your-own-voice-agent.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/blog/deepgram-deepseek-fish-build-your-own-voice-agent.mdx"}, data: blogPosts_0 }, { info: {"path":"exploring-the-ai-sales-avatar.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/blog/exploring-the-ai-sales-avatar.mdx"}, data: blogPosts_1 }, { info: {"path":"ten-world-first-truly-real-time-multimodal-agent-framework.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/blog/ten-world-first-truly-real-time-multimodal-agent-framework.mdx"}, data: blogPosts_2 }, { info: {"path":"tman-designer-of-ten-framework.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/blog/tman-designer-of-ten-framework.mdx"}, data: blogPosts_3 }]);
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"application_scenarios.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/application_scenarios.cn.md"}, data: docs_0 }, { info: {"path":"application_scenarios.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/application_scenarios.md"}, data: docs_1 }, { info: {"path":"goal.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/goal.cn.md"}, data: docs_2 }, { info: {"path":"goal.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/goal.md"}, data: docs_3 }, { info: {"path":"index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/index.cn.md"}, data: docs_4 }, { info: {"path":"index.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/index.mdx"}, data: docs_5 }, { info: {"path":"key_concepts.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/key_concepts.cn.md"}, data: docs_6 }, { info: {"path":"key_concepts.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/key_concepts.md"}, data: docs_7 }, { info: {"path":"ten_agent\\architecture_flow.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/architecture_flow.cn.md"}, data: docs_8 }, { info: {"path":"ten_agent\\architecture_flow.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/architecture_flow.md"}, data: docs_9 }, { info: {"path":"ten_agent\\create_a_hello_world_extension.cn.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_a_hello_world_extension.cn.mdx"}, data: docs_10 }, { info: {"path":"ten_agent\\create_a_hello_world_extension.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_a_hello_world_extension.mdx"}, data: docs_11 }, { info: {"path":"ten_agent\\customize_your_agent.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/customize_your_agent.cn.md"}, data: docs_12 }, { info: {"path":"ten_agent\\customize_your_agent.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/customize_your_agent.md"}, data: docs_13 }, { info: {"path":"ten_agent\\faqs.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/faqs.cn.md"}, data: docs_14 }, { info: {"path":"ten_agent\\faqs.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/faqs.md"}, data: docs_15 }, { info: {"path":"ten_agent\\getting_started.cn.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/getting_started.cn.mdx"}, data: docs_16 }, { info: {"path":"ten_agent\\getting_started.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/getting_started.mdx"}, data: docs_17 }, { info: {"path":"ten_agent\\how_does_interrupt_work.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/how_does_interrupt_work.cn.md"}, data: docs_18 }, { info: {"path":"ten_agent\\how_does_interrupt_work.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/how_does_interrupt_work.md"}, data: docs_19 }, { info: {"path":"ten_agent\\overview.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/overview.cn.md"}, data: docs_20 }, { info: {"path":"ten_agent\\overview.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/overview.md"}, data: docs_21 }, { info: {"path":"ten_framework\\build_system.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/build_system.cn.md"}, data: docs_22 }, { info: {"path":"ten_framework\\build_system.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/build_system.md"}, data: docs_23 }, { info: {"path":"ten_framework\\cloud_store.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/cloud_store.cn.md"}, data: docs_24 }, { info: {"path":"ten_framework\\cloud_store.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/cloud_store.md"}, data: docs_25 }, { info: {"path":"ten_framework\\concept_overview.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/concept_overview.cn.md"}, data: docs_26 }, { info: {"path":"ten_framework\\concept_overview.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/concept_overview.md"}, data: docs_27 }, { info: {"path":"ten_framework\\dependencies.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/dependencies.cn.md"}, data: docs_28 }, { info: {"path":"ten_framework\\dependencies.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/dependencies.md"}, data: docs_29 }, { info: {"path":"ten_framework\\message_system.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/message_system.cn.md"}, data: docs_30 }, { info: {"path":"ten_framework\\message_system.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/message_system.md"}, data: docs_31 }, { info: {"path":"ten_framework\\preparation.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/preparation.cn.md"}, data: docs_32 }, { info: {"path":"ten_framework\\preparation.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/preparation.md"}, data: docs_33 }, { info: {"path":"ten_framework\\profiling.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/profiling.cn.md"}, data: docs_34 }, { info: {"path":"ten_framework\\profiling.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/profiling.md"}, data: docs_35 }, { info: {"path":"ten_framework\\version_system.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/version_system.cn.md"}, data: docs_36 }, { info: {"path":"ten_framework\\version_system.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/version_system.md"}, data: docs_37 }, { info: {"path":"ten_agent\\create_an_extension_with_predefined_type\\index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/index.cn.md"}, data: docs_38 }, { info: {"path":"ten_agent\\create_an_extension_with_predefined_type\\index.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/index.md"}, data: docs_39 }, { info: {"path":"ten_agent\\create_an_extension_with_predefined_type\\LLM_extension.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_extension.cn.md"}, data: docs_40 }, { info: {"path":"ten_agent\\create_an_extension_with_predefined_type\\LLM_extension.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_extension.md"}, data: docs_41 }, { info: {"path":"ten_agent\\create_an_extension_with_predefined_type\\LLM_Tool_extension.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_Tool_extension.cn.md"}, data: docs_42 }, { info: {"path":"ten_agent\\create_an_extension_with_predefined_type\\LLM_Tool_extension.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_Tool_extension.md"}, data: docs_43 }, { info: {"path":"ten_agent\\deploy_ten_agent\\deploy_agent_service.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/deploy_ten_agent/deploy_agent_service.md"}, data: docs_44 }, { info: {"path":"ten_agent\\demo\\index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/demo/index.cn.md"}, data: docs_45 }, { info: {"path":"ten_agent\\demo\\index.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/demo/index.md"}, data: docs_46 }, { info: {"path":"ten_agent\\playground\\change_language.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/change_language.cn.md"}, data: docs_47 }, { info: {"path":"ten_agent\\playground\\change_language.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/change_language.md"}, data: docs_48 }, { info: {"path":"ten_agent\\playground\\configure_modules.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/configure_modules.cn.md"}, data: docs_49 }, { info: {"path":"ten_agent\\playground\\configure_modules.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/configure_modules.md"}, data: docs_50 }, { info: {"path":"ten_agent\\playground\\configure_properties.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/configure_properties.cn.md"}, data: docs_51 }, { info: {"path":"ten_agent\\playground\\configure_properties.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/configure_properties.md"}, data: docs_52 }, { info: {"path":"ten_agent\\playground\\index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/index.cn.md"}, data: docs_53 }, { info: {"path":"ten_agent\\playground\\index.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/index.md"}, data: docs_54 }, { info: {"path":"ten_agent\\project_structure\\extension.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/extension.cn.md"}, data: docs_55 }, { info: {"path":"ten_agent\\project_structure\\extension.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/extension.md"}, data: docs_56 }, { info: {"path":"ten_agent\\project_structure\\index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/index.cn.md"}, data: docs_57 }, { info: {"path":"ten_agent\\project_structure\\index.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/index.md"}, data: docs_58 }, { info: {"path":"ten_agent\\project_structure\\property_json.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/property_json.cn.md"}, data: docs_59 }, { info: {"path":"ten_agent\\project_structure\\property_json.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/property_json.md"}, data: docs_60 }, { info: {"path":"ten_agent\\project_structure\\server.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/server.cn.md"}, data: docs_61 }, { info: {"path":"ten_agent\\project_structure\\server.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/server.md"}, data: docs_62 }, { info: {"path":"ten_agent\\setup_development_env\\index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/index.cn.md"}, data: docs_63 }, { info: {"path":"ten_agent\\setup_development_env\\index.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/index.md"}, data: docs_64 }, { info: {"path":"ten_agent\\setup_development_env\\setting_up_development_inside_codespace.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/setting_up_development_inside_codespace.cn.md"}, data: docs_65 }, { info: {"path":"ten_agent\\setup_development_env\\setting_up_development_inside_codespace.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/setting_up_development_inside_codespace.md"}, data: docs_66 }, { info: {"path":"ten_agent\\setup_development_env\\setting_up_vscode_for_development_inside_container.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/setting_up_vscode_for_development_inside_container.cn.md"}, data: docs_67 }, { info: {"path":"ten_agent\\setup_development_env\\setting_up_vscode_for_development_inside_container.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/setting_up_vscode_for_development_inside_container.md"}, data: docs_68 }, { info: {"path":"ten_agent\\tutorials\\how_to_run_local_model_in_ollama.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/tutorials/how_to_run_local_model_in_ollama.md"}, data: docs_69 }, { info: {"path":"ten_framework\\ai_model\\how_to_run_local_model_in_python_extensions.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ai_model/how_to_run_local_model_in_python_extensions.cn.md"}, data: docs_70 }, { info: {"path":"ten_framework\\ai_model\\how_to_run_local_model_in_python_extensions.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ai_model/how_to_run_local_model_in_python_extensions.md"}, data: docs_71 }, { info: {"path":"ten_framework\\api\\api.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/api.cn.md"}, data: docs_72 }, { info: {"path":"ten_framework\\api\\api.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/api.md"}, data: docs_73 }, { info: {"path":"ten_framework\\api\\log.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/log.cn.md"}, data: docs_74 }, { info: {"path":"ten_framework\\api\\log.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/log.md"}, data: docs_75 }, { info: {"path":"ten_framework\\api\\required.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/required.cn.md"}, data: docs_76 }, { info: {"path":"ten_framework\\api\\required.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/required.md"}, data: docs_77 }, { info: {"path":"ten_framework\\api\\type_system.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/type_system.cn.md"}, data: docs_78 }, { info: {"path":"ten_framework\\api\\type_system.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/type_system.md"}, data: docs_79 }, { info: {"path":"ten_framework\\graph\\graph.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/graph/graph.cn.md"}, data: docs_80 }, { info: {"path":"ten_framework\\graph\\graph.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/graph/graph.md"}, data: docs_81 }, { info: {"path":"ten_framework\\graph\\msg_conversion.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/graph/msg_conversion.cn.md"}, data: docs_82 }, { info: {"path":"ten_framework\\graph\\msg_conversion.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/graph/msg_conversion.md"}, data: docs_83 }, { info: {"path":"ten_framework\\development\\development_process.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/development/development_process.cn.md"}, data: docs_84 }, { info: {"path":"ten_framework\\development\\development_process.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/development/development_process.md"}, data: docs_85 }, { info: {"path":"ten_framework\\development\\how_to_develop_with_ext.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/development/how_to_develop_with_ext.cn.md"}, data: docs_86 }, { info: {"path":"ten_framework\\development\\how_to_develop_with_ext.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/development/how_to_develop_with_ext.md"}, data: docs_87 }, { info: {"path":"ten_framework\\language_binding\\go.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/language_binding/go.cn.md"}, data: docs_88 }, { info: {"path":"ten_framework\\language_binding\\go.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/language_binding/go.md"}, data: docs_89 }, { info: {"path":"ten_framework\\metadata_system\\manifest.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/manifest.cn.md"}, data: docs_90 }, { info: {"path":"ten_framework\\metadata_system\\manifest.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/manifest.md"}, data: docs_91 }, { info: {"path":"ten_framework\\metadata_system\\metadata_system.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/metadata_system.cn.md"}, data: docs_92 }, { info: {"path":"ten_framework\\metadata_system\\metadata_system.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/metadata_system.md"}, data: docs_93 }, { info: {"path":"ten_framework\\metadata_system\\property.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/property.cn.md"}, data: docs_94 }, { info: {"path":"ten_framework\\metadata_system\\property.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/property.md"}, data: docs_95 }, { info: {"path":"ten_framework\\testing_and_debugging\\debugging.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/debugging.cn.md"}, data: docs_96 }, { info: {"path":"ten_framework\\testing_and_debugging\\debugging.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/debugging.md"}, data: docs_97 }, { info: {"path":"ten_framework\\testing_and_debugging\\for_developers_of_ten_framework.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/for_developers_of_ten_framework.cn.md"}, data: docs_98 }, { info: {"path":"ten_framework\\testing_and_debugging\\for_developers_of_ten_framework.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/for_developers_of_ten_framework.md"}, data: docs_99 }, { info: {"path":"ten_framework\\testing_and_debugging\\for_users_of_ten_framework.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/for_users_of_ten_framework.cn.md"}, data: docs_100 }, { info: {"path":"ten_framework\\testing_and_debugging\\for_users_of_ten_framework.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/for_users_of_ten_framework.md"}, data: docs_101 }, { info: {"path":"ten_framework\\testing_and_debugging\\how_to_debug_with_logs.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/how_to_debug_with_logs.cn.md"}, data: docs_102 }, { info: {"path":"ten_framework\\testing_and_debugging\\how_to_debug_with_logs.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/how_to_debug_with_logs.md"}, data: docs_103 }, { info: {"path":"ten_framework\\software_packages\\addon_system.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/addon_system.cn.md"}, data: docs_104 }, { info: {"path":"ten_framework\\software_packages\\addon_system.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/addon_system.md"}, data: docs_105 }, { info: {"path":"ten_framework\\software_packages\\app.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/app.cn.md"}, data: docs_106 }, { info: {"path":"ten_framework\\software_packages\\app.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/app.md"}, data: docs_107 }, { info: {"path":"ten_framework\\software_packages\\extension.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/extension.cn.md"}, data: docs_108 }, { info: {"path":"ten_framework\\software_packages\\extension.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/extension.md"}, data: docs_109 }, { info: {"path":"ten_framework\\software_packages\\overview.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/overview.cn.md"}, data: docs_110 }, { info: {"path":"ten_framework\\software_packages\\overview.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/overview.md"}, data: docs_111 }, { info: {"path":"ten_framework\\software_packages\\python_async_extension.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/python_async_extension.cn.md"}, data: docs_112 }, { info: {"path":"ten_framework\\software_packages\\python_async_extension.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/python_async_extension.md"}, data: docs_113 }, { info: {"path":"ten_framework\\ten_manager\\check_graph.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ten_manager/check_graph.cn.md"}, data: docs_114 }, { info: {"path":"ten_framework\\ten_manager\\check_graph.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ten_manager/check_graph.md"}, data: docs_115 }, { info: {"path":"ten_framework\\ten_manager\\overview.cn.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ten_manager/overview.cn.mdx"}, data: docs_116 }, { info: {"path":"ten_framework\\ten_manager\\overview.mdx","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ten_manager/overview.mdx"}, data: docs_117 }, { info: {"path":"ten_framework\\standard_extension\\standard_interface.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/standard_extension/standard_interface.cn.md"}, data: docs_118 }, { info: {"path":"ten_framework\\standard_extension\\standard_interface.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/standard_extension/standard_interface.md"}, data: docs_119 }, { info: {"path":"ten_framework\\tman_designer\\doc_window.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/tman_designer/doc_window.cn.md"}, data: docs_120 }, { info: {"path":"ten_framework\\tman_designer\\doc_window.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/tman_designer/doc_window.md"}, data: docs_121 }, { info: {"path":"ten_framework\\tman_designer\\overview.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/tman_designer/overview.cn.md"}, data: docs_122 }, { info: {"path":"ten_framework\\tman_designer\\overview.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/tman_designer/overview.md"}, data: docs_123 }, { info: {"path":"ten_agent\\playground\\use-cases\\run_story_telling.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/run_story_telling.cn.md"}, data: docs_124 }, { info: {"path":"ten_agent\\playground\\use-cases\\run_story_telling.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/run_story_telling.md"}, data: docs_125 }, { info: {"path":"ten_agent\\playground\\use-cases\\voice-assistant\\index.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/index.cn.md"}, data: docs_126 }, { info: {"path":"ten_agent\\playground\\use-cases\\voice-assistant\\index.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/index.md"}, data: docs_127 }, { info: {"path":"ten_agent\\playground\\use-cases\\voice-assistant\\run_coze.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/run_coze.cn.md"}, data: docs_128 }, { info: {"path":"ten_agent\\playground\\use-cases\\voice-assistant\\run_coze.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/run_coze.md"}, data: docs_129 }, { info: {"path":"ten_agent\\playground\\use-cases\\voice-assistant\\run_dify.cn.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/run_dify.cn.md"}, data: docs_130 }, { info: {"path":"ten_agent\\playground\\use-cases\\voice-assistant\\run_dify.md","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/run_dify.md"}, data: docs_131 }], [{"info":{"path":"meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/meta.json"},"data":{"pages":["---Introduction---","index","goal","key_concepts","application_scenarios","---TEN Framework---","ten_framework","---TEN Agent---","ten_agent"]}}, {"info":{"path":"ten_agent\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/meta.cn.json"},"data":{"title":"TEN Agent","pages":["overview","getting_started","architecture_flow","project_structure","setup_development_env","demo/index","playground","customize_your_agent","create_a_hello_world_extension","how_does_interrupt_work","create_an_extension_with_predefined_type","faqs"],"description":"TEN Agent","root":true}}, {"info":{"path":"ten_agent\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/meta.json"},"data":{"title":"TEN Agent","pages":["overview","getting_started","architecture_flow","project_structure","setup_development_env","demo/index","playground","customize_your_agent","deploy_ten_agent","create_a_hello_world_extension","how_does_interrupt_work","create_an_extension_with_predefined_type","tutorials/how_to_run_local_model_in_ollama","faqs"],"description":"TEN Agent","root":true}}, {"info":{"path":"ten_framework\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/meta.json"},"data":{"title":"TEN Framework","pages":["concept_overview","preparation","version_system","api","language_binding","metadata_system","message_system","build_system","graph","software_packages","standard_extension","cloud_store","profiling","dependencies","development","testing_and_debugging","ten_manager","tman_designer","ai_model"],"description":"TEN Framework","root":true}}, {"info":{"path":"ten_agent\\create_an_extension_with_predefined_type\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/create_an_extension_with_predefined_type/meta.cn.json"},"data":{"title":"使用预定义类型创建扩展"}}, {"info":{"path":"ten_agent\\deploy_ten_agent\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/deploy_ten_agent/meta.json"},"data":{"title":"Deployment","pages":["deploy_agent_service"]}}, {"info":{"path":"ten_agent\\playground\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/meta.cn.json"},"data":{"title":"运行 Playground","pages":["configure_modules","configure_properties","use-cases","change_language"]}}, {"info":{"path":"ten_agent\\playground\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/meta.json"},"data":{"pages":["configure_modules","configure_properties","use-cases","change_language"]}}, {"info":{"path":"ten_agent\\project_structure\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/meta.cn.json"},"data":{"title":"项目结构","pages":["property_json","extension","server"]}}, {"info":{"path":"ten_agent\\project_structure\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/project_structure/meta.json"},"data":{"title":"TEN Agent Project Overview","pages":["property_json","extension","server"]}}, {"info":{"path":"ten_framework\\ai_model\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ai_model/meta.cn.json"},"data":{"title":"AI Model","pages":["how_to_run_local_model_in_python_extensions"]}}, {"info":{"path":"ten_framework\\ai_model\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ai_model/meta.json"},"data":{"title":"AI Model","pages":["how_to_run_local_model_in_python_extensions"]}}, {"info":{"path":"ten_agent\\setup_development_env\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/meta.cn.json"},"data":{"title":"搭建开发环境","pages":["setting_up_development_inside_codespace","setting_up_vscode_for_development_inside_container"]}}, {"info":{"path":"ten_agent\\setup_development_env\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/setup_development_env/meta.json"},"data":{"pages":["setting_up_development_inside_codespace","setting_up_vscode_for_development_inside_container"]}}, {"info":{"path":"ten_framework\\api\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/meta.cn.json"},"data":{"title":"API","pages":["api","type_system","required","log"]}}, {"info":{"path":"ten_framework\\api\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/api/meta.json"},"data":{"title":"API","pages":["api","type_system","required","log"]}}, {"info":{"path":"ten_framework\\development\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/development/meta.cn.json"},"data":{"title":"开发","pages":["development_process","how_to_develop_with_ext"],"description":"开发"}}, {"info":{"path":"ten_framework\\development\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/development/meta.json"},"data":{"title":"Development","pages":["development_process","how_to_develop_with_ext"],"description":"Development"}}, {"info":{"path":"ten_framework\\graph\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/graph/meta.cn.json"},"data":{"title":"图","pages":["graph","msg_conversion"]}}, {"info":{"path":"ten_framework\\graph\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/graph/meta.json"},"data":{"title":"Graph","pages":["graph","msg_conversion"]}}, {"info":{"path":"ten_framework\\language_binding\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/language_binding/meta.cn.json"},"data":{"title":"图","pages":["graph"]}}, {"info":{"path":"ten_framework\\language_binding\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/language_binding/meta.json"},"data":{"title":"Language Binding","pages":["go"]}}, {"info":{"path":"ten_framework\\metadata_system\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/meta.cn.json"},"data":{"title":"元数据系统","pages":["metadata_system","manifest","property"]}}, {"info":{"path":"ten_framework\\metadata_system\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/metadata_system/meta.json"},"data":{"title":"Metadata System","pages":["metadata_system","manifest","property"]}}, {"info":{"path":"ten_framework\\software_packages\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/meta.cn.json"},"data":{"title":"软件包","pages":["overview","addon_system","app","extension","python_async_extension"]}}, {"info":{"path":"ten_framework\\software_packages\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/software_packages/meta.json"},"data":{"title":"Software Packages","pages":["overview","addon_system","app","extension","python_async_extension"]}}, {"info":{"path":"ten_framework\\standard_extension\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/standard_extension/meta.cn.json"},"data":{"title":"标准扩展","pages":["standard_interface"]}}, {"info":{"path":"ten_framework\\standard_extension\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/standard_extension/meta.json"},"data":{"title":"Standard Extension","pages":["standard_interface"]}}, {"info":{"path":"ten_framework\\ten_manager\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ten_manager/meta.cn.json"},"data":{"title":"TEN 管理器","pages":["overview","check_graph"]}}, {"info":{"path":"ten_framework\\ten_manager\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/ten_manager/meta.json"},"data":{"title":"TEN Manager","pages":["overview","check_graph"]}}, {"info":{"path":"ten_framework\\testing_and_debugging\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/meta.cn.json"},"data":{"title":"测试和调试","pages":["for_users_of_ten_framework","for_developers_of_ten_framework","how_to_debug_with_logs","debugging"],"description":"测试和调试"}}, {"info":{"path":"ten_framework\\testing_and_debugging\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/testing_and_debugging/meta.json"},"data":{"title":"Testing and Debugging","pages":["for_users_of_ten_framework","for_developers_of_ten_framework","how_to_debug_with_logs","debugging"],"description":"Testing and Debugging"}}, {"info":{"path":"ten_framework\\tman_designer\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/tman_designer/meta.cn.json"},"data":{"title":"TMAN 设计师","pages":["overview","doc_window"]}}, {"info":{"path":"ten_framework\\tman_designer\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_framework/tman_designer/meta.json"},"data":{"title":"TMAN Designer","pages":["overview","doc_window"]}}, {"info":{"path":"ten_agent\\playground\\use-cases\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/meta.cn.json"},"data":{"title":"使用案例"}}, {"info":{"path":"ten_agent\\playground\\use-cases\\meta.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/meta.json"},"data":{"title":"Use Cases"}}, {"info":{"path":"ten_agent\\playground\\use-cases\\voice-assistant\\meta.cn.json","absolutePath":"C:/Users/<USER>/Downloads/portal-main/portal-main/content/docs/ten_agent/playground/use-cases/voice-assistant/meta.cn.json"},"data":{"title":"语音助手"}}])