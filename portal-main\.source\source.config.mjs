// source.config.ts
import {
  defineDocs,
  defineConfig,
  defineCollections
} from "fumadocs-mdx/config";
import { z } from "zod";
var docs = defineDocs({
  dir: "content/docs"
});
var source_config_default = defineConfig({
  mdxOptions: {
    // MDX options
  }
});
var blogPosts = defineCollections({
  type: "doc",
  dir: "content/blog",
  schema: z.object({
    title: z.string(),
    description: z.string(),
    author: z.string(),
    date: z.coerce.date()
  })
});
export {
  blogPosts,
  source_config_default as default,
  docs
};
