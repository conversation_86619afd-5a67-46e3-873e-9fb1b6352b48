"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[381],{2085:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(2596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,s=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(n);return i[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,s,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...u}[t]):({...l,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=t);return n}},3536:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:u="",children:d,iconNode:c,...f}=e;return(0,n.createElement)("svg",{ref:t,...s,width:a,height:a,stroke:r,strokeWidth:i?24*Number(o)/Number(a):o,className:l("lucide",u),...f},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:s,...d}=r;return(0,n.createElement)(u,{ref:o,iconNode:t,className:l("lucide-".concat(a(i(e))),"lucide-".concat(e),s),...d})});return r.displayName=i(e),r}},5619:(e,t,r)=>{r.d(t,{Menu:()=>f,MenuContent:()=>m,MenuLinkItem:()=>c,MenuTrigger:()=>v});var n=r(5155),a=r(4657),o=r(9688),i=r(7474),l=r(9949),s=r(2085),u=r(7936);let d=(0,s.F)("",{variants:{variant:{main:"inline-flex items-center gap-2 py-1.5 transition-colors hover:text-fd-popover-foreground/50 data-[active=true]:font-medium data-[active=true]:text-fd-primary [&_svg]:size-4",icon:(0,u.r)({size:"icon",color:"ghost"}),button:(0,u.r)({color:"secondary",className:"gap-1.5 [&_svg]:size-4"})}},defaultVariants:{variant:"main"}});function c(e){let{item:t,...r}=e;if("custom"===t.type)return(0,n.jsx)("div",{className:(0,o.QP)("grid",r.className),children:t.children});if("menu"===t.type){let e=(0,n.jsxs)(n.Fragment,{children:[t.icon,t.text]});return(0,n.jsxs)("div",{className:(0,o.QP)("mb-4 flex flex-col",r.className),children:[(0,n.jsx)("p",{className:"mb-1 text-sm text-fd-muted-foreground",children:t.url?(0,n.jsx)(i.Ws,{asChild:!0,children:(0,n.jsx)(l.default,{href:t.url,children:e})}):e}),t.items.map((e,t)=>(0,n.jsx)(c,{item:e},t))]})}return(0,n.jsx)(i.Ws,{asChild:!0,children:(0,n.jsxs)(a.BaseLinkItem,{item:t,className:(0,o.QP)(d({variant:t.type}),r.className),"aria-label":"icon"===t.type?t.label:void 0,children:[t.icon,"icon"===t.type?void 0:t.text]})})}let f=i.JD;function v(e){let{enableHover:t=!1,...r}=e;return(0,n.jsx)(i.wd,{...r,onPointerMove:t?void 0:e=>e.preventDefault(),className:(0,o.QP)((0,u.r)({size:"icon",color:"ghost"}),r.className),children:r.children})}function m(e){return(0,n.jsx)(i.hA,{...e,className:(0,o.QP)("flex flex-col p-4",e.className),children:e.children})}},7110:(e,t,r)=>{r.d(t,{Navbar:()=>v,NavbarLink:()=>w,NavbarMenu:()=>m,NavbarMenuContent:()=>h,NavbarMenuLink:()=>g,NavbarMenuTrigger:()=>p});var n=r(5155),a=r(2115),o=r(2085),i=r(9949),l=r(9688),s=r(4657),u=r(7474),d=r(1339),c=r(7936);let f=(0,o.F)("inline-flex items-center gap-1 p-2 text-fd-muted-foreground transition-colors hover:text-fd-accent-foreground data-[active=true]:text-fd-primary [&_svg]:size-4");function v(e){let[t,r]=(0,a.useState)(""),{isTransparent:o}=(0,d.h)();return(0,n.jsx)(u.KS,{value:t,onValueChange:r,asChild:!0,children:(0,n.jsxs)("header",{id:"nd-nav",...e,className:(0,l.QP)("fixed left-1/2 top-(--fd-banner-height) z-40 box-content w-full max-w-fd-container -translate-x-1/2 border-b border-fd-foreground/10 transition-colors lg:mt-2 lg:w-[calc(100%-1rem)] lg:rounded-2xl lg:border",t.length>0?"shadow-lg":"shadow-sm",(!o||t.length>0)&&"bg-fd-background/80 backdrop-blur-lg",e.className),children:[(0,n.jsx)(u.SK,{className:"flex h-14 w-full flex-row items-center px-4 lg:h-12",asChild:!0,children:(0,n.jsx)("nav",{children:e.children})}),(0,n.jsx)(u.QW,{})]})})}let m=u.JD;function h(e){return(0,n.jsx)(u.hA,{...e,className:(0,l.QP)("grid grid-cols-1 gap-3 px-4 pb-4 md:grid-cols-2 lg:grid-cols-3",e.className),children:e.children})}function p(e){return(0,n.jsx)(u.wd,{...e,className:(0,l.QP)(f(),"rounded-md",e.className),children:e.children})}function g(e){return(0,n.jsx)(u.Ws,{asChild:!0,children:(0,n.jsx)(i.default,{...e,className:(0,l.QP)("flex flex-col gap-2 rounded-lg border bg-fd-card p-3 transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground",e.className),children:e.children})})}let x=(0,o.F)("",{variants:{variant:{main:f(),button:(0,c.r)({color:"secondary",className:"gap-1.5 [&_svg]:size-4"}),icon:(0,c.r)({color:"ghost",size:"icon"})}},defaultVariants:{variant:"main"}});function w(e){let{item:t,variant:r,...a}=e;return(0,n.jsx)(u.JD,{children:(0,n.jsx)(u.Ws,{asChild:!0,children:(0,n.jsx)(s.BaseLinkItem,{...a,item:t,className:(0,l.QP)(x({variant:r}),a.className),children:a.children})})})}},7474:(e,t,r)=>{r.d(t,{KS:()=>ev,hA:()=>eg,JD:()=>eh,Ws:()=>ex,SK:()=>em,wd:()=>ep,QW:()=>ew});var n=r(5155),a=r(2115),o=r(7650),i=r(6081),l=r(5185),s=r(3655),u=r(5845),d=r(6101),c=r(4315),f=r(8905),v=r(1285),m=r(7328),h=r(9178),p=r(2712),g=r(9033),x=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),w=a.forwardRef((e,t)=>(0,n.jsx)(s.sG.span,{...e,ref:t,style:{...x,...e.style}}));w.displayName="VisuallyHidden";var b="NavigationMenu",[N,y,j]=(0,m.N)(b),[C,R,E]=(0,m.N)(b),[M,P]=(0,i.A)(b,[j,E]),[T,k]=M(b),[L,A]=M(b),_=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:o,onValueChange:i,defaultValue:l,delayDuration:f=200,skipDelayDuration:v=300,orientation:m="horizontal",dir:h,...p}=e,[g,x]=a.useState(null),w=(0,d.s)(t,e=>x(e)),N=(0,c.jH)(h),y=a.useRef(0),j=a.useRef(0),C=a.useRef(0),[R,E]=a.useState(!0),[M,P]=(0,u.i)({prop:o,onChange:e=>{let t=v>0;""!==e?(window.clearTimeout(C.current),t&&E(!1)):(window.clearTimeout(C.current),C.current=window.setTimeout(()=>E(!0),v)),null==i||i(e)},defaultProp:null!=l?l:"",caller:b}),T=a.useCallback(()=>{window.clearTimeout(j.current),j.current=window.setTimeout(()=>P(""),150)},[P]),k=a.useCallback(e=>{window.clearTimeout(j.current),P(e)},[P]),L=a.useCallback(e=>{M===e?window.clearTimeout(j.current):y.current=window.setTimeout(()=>{window.clearTimeout(j.current),P(e)},f)},[M,P,f]);return a.useEffect(()=>()=>{window.clearTimeout(y.current),window.clearTimeout(j.current),window.clearTimeout(C.current)},[]),(0,n.jsx)(I,{scope:r,isRootMenu:!0,value:M,dir:N,orientation:m,rootNavigationMenu:g,onTriggerEnter:e=>{window.clearTimeout(y.current),R?L(e):k(e)},onTriggerLeave:()=>{window.clearTimeout(y.current),T()},onContentEnter:()=>window.clearTimeout(j.current),onContentLeave:T,onItemSelect:e=>{P(t=>t===e?"":e)},onItemDismiss:()=>P(""),children:(0,n.jsx)(s.sG.nav,{"aria-label":"Main","data-orientation":m,dir:N,...p,ref:w})})});_.displayName=b;var F="NavigationMenuSub";a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:a,onValueChange:o,defaultValue:i,orientation:l="horizontal",...d}=e,c=k(F,r),[f,v]=(0,u.i)({prop:a,onChange:o,defaultProp:null!=i?i:"",caller:F});return(0,n.jsx)(I,{scope:r,isRootMenu:!1,value:f,dir:c.dir,orientation:l,rootNavigationMenu:c.rootNavigationMenu,onTriggerEnter:e=>v(e),onItemSelect:e=>v(e),onItemDismiss:()=>v(""),children:(0,n.jsx)(s.sG.div,{"data-orientation":l,...d,ref:t})})}).displayName=F;var I=e=>{let{scope:t,isRootMenu:r,rootNavigationMenu:o,dir:i,orientation:l,children:s,value:u,onItemSelect:d,onItemDismiss:c,onTriggerEnter:f,onTriggerLeave:m,onContentEnter:h,onContentLeave:p}=e,[x,w]=a.useState(null),[b,y]=a.useState(new Map),[j,C]=a.useState(null);return(0,n.jsx)(T,{scope:t,isRootMenu:r,rootNavigationMenu:o,value:u,previousValue:function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(u),baseId:(0,v.B)(),dir:i,orientation:l,viewport:x,onViewportChange:w,indicatorTrack:j,onIndicatorTrackChange:C,onTriggerEnter:(0,g.c)(f),onTriggerLeave:(0,g.c)(m),onContentEnter:(0,g.c)(h),onContentLeave:(0,g.c)(p),onItemSelect:(0,g.c)(d),onItemDismiss:(0,g.c)(c),onViewportContentChange:a.useCallback((e,t)=>{y(r=>(r.set(e,t),new Map(r)))},[]),onViewportContentRemove:a.useCallback(e=>{y(t=>t.has(e)?(t.delete(e),new Map(t)):t)},[]),children:(0,n.jsx)(N.Provider,{scope:t,children:(0,n.jsx)(L,{scope:t,items:b,children:s})})})},D="NavigationMenuList",S=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...a}=e,o=k(D,r),i=(0,n.jsx)(s.sG.ul,{"data-orientation":o.orientation,...a,ref:t});return(0,n.jsx)(s.sG.div,{style:{position:"relative"},ref:o.onIndicatorTrackChange,children:(0,n.jsx)(N.Slot,{scope:r,children:o.isRootMenu?(0,n.jsx)(er,{asChild:!0,children:i}):i})})});S.displayName=D;var O="NavigationMenuItem",[z,K]=M(O),W=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:o,...i}=e,l=(0,v.B)(),u=a.useRef(null),d=a.useRef(null),c=a.useRef(null),f=a.useRef(()=>{}),m=a.useRef(!1),h=a.useCallback(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"start";if(u.current){f.current();let t=eo(u.current);t.length&&ei("start"===e?t:t.reverse())}},[]),p=a.useCallback(()=>{if(u.current){let e=eo(u.current);e.length&&(f.current=function(e){return e.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}),()=>{e.forEach(e=>{let t=e.dataset.tabindex;e.setAttribute("tabindex",t)})}}(e))}},[]);return(0,n.jsx)(z,{scope:r,value:o||l||"LEGACY_REACT_AUTO_VALUE",triggerRef:d,contentRef:u,focusProxyRef:c,wasEscapeCloseRef:m,onEntryKeyDown:h,onFocusProxyEnter:h,onRootContentClose:p,onContentFocusOutside:p,children:(0,n.jsx)(s.sG.li,{...i,ref:t})})});W.displayName=O;var Q="NavigationMenuTrigger",G=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,disabled:o,...i}=e,u=k(Q,e.__scopeNavigationMenu),c=K(Q,e.__scopeNavigationMenu),f=a.useRef(null),v=(0,d.s)(f,c.triggerRef,t),m=eu(u.baseId,c.value),h=ed(u.baseId,c.value),p=a.useRef(!1),g=a.useRef(!1),x=c.value===u.value;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.ItemSlot,{scope:r,value:c.value,children:(0,n.jsx)(ea,{asChild:!0,children:(0,n.jsx)(s.sG.button,{id:m,disabled:o,"data-disabled":o?"":void 0,"data-state":es(x),"aria-expanded":x,"aria-controls":h,...i,ref:v,onPointerEnter:(0,l.m)(e.onPointerEnter,()=>{g.current=!1,c.wasEscapeCloseRef.current=!1}),onPointerMove:(0,l.m)(e.onPointerMove,ec(()=>{o||g.current||c.wasEscapeCloseRef.current||p.current||(u.onTriggerEnter(c.value),p.current=!0)})),onPointerLeave:(0,l.m)(e.onPointerLeave,ec(()=>{o||(u.onTriggerLeave(),p.current=!1)})),onClick:(0,l.m)(e.onClick,()=>{u.onItemSelect(c.value),g.current=x}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{let t={horizontal:"ArrowDown",vertical:"rtl"===u.dir?"ArrowLeft":"ArrowRight"}[u.orientation];x&&e.key===t&&(c.onEntryKeyDown(),e.preventDefault())})})})}),x&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(w,{"aria-hidden":!0,tabIndex:0,ref:c.focusProxyRef,onFocus:e=>{let t=c.contentRef.current,r=e.relatedTarget,n=r===f.current,a=null==t?void 0:t.contains(r);(n||!a)&&c.onFocusProxyEnter(n?"start":"end")}}),u.viewport&&(0,n.jsx)("span",{"aria-owns":h})]})]})});G.displayName=Q;var V="navigationMenu.linkSelect",U=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,active:a,onSelect:o,...i}=e;return(0,n.jsx)(ea,{asChild:!0,children:(0,n.jsx)(s.sG.a,{"data-active":a?"":void 0,"aria-current":a?"page":void 0,...i,ref:t,onClick:(0,l.m)(e.onClick,e=>{let t=e.target,r=new CustomEvent(V,{bubbles:!0,cancelable:!0});if(t.addEventListener(V,e=>null==o?void 0:o(e),{once:!0}),(0,s.hO)(t,r),!r.defaultPrevented&&!e.metaKey){let e=new CustomEvent(Y,{bubbles:!0,cancelable:!0});(0,s.hO)(t,e)}},{checkForDefaultPrevented:!1})})})});U.displayName="NavigationMenuLink";var H="NavigationMenuIndicator";a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,i=k(H,e.__scopeNavigationMenu),l=!!i.value;return i.indicatorTrack?o.createPortal((0,n.jsx)(f.C,{present:r||l,children:(0,n.jsx)(B,{...a,ref:t})}),i.indicatorTrack):null}).displayName=H;var B=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...o}=e,i=k(H,r),l=y(r),[u,d]=a.useState(null),[c,f]=a.useState(null),v="horizontal"===i.orientation,m=!!i.value;a.useEffect(()=>{var e;let t=null==(e=l().find(e=>e.value===i.value))?void 0:e.ref.current;t&&d(t)},[l,i.value]);let h=()=>{u&&f({size:v?u.offsetWidth:u.offsetHeight,offset:v?u.offsetLeft:u.offsetTop})};return el(u,h),el(i.indicatorTrack,h),c?(0,n.jsx)(s.sG.div,{"aria-hidden":!0,"data-state":m?"visible":"hidden","data-orientation":i.orientation,...o,ref:t,style:{position:"absolute",...v?{left:0,width:c.size+"px",transform:"translateX(".concat(c.offset,"px)")}:{top:0,height:c.size+"px",transform:"translateY(".concat(c.offset,"px)")},...o.style}}):null}),$="NavigationMenuContent",J=a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,o=k($,e.__scopeNavigationMenu),i=K($,e.__scopeNavigationMenu),s=(0,d.s)(i.contentRef,t),u=i.value===o.value,c={value:i.value,triggerRef:i.triggerRef,focusProxyRef:i.focusProxyRef,wasEscapeCloseRef:i.wasEscapeCloseRef,onContentFocusOutside:i.onContentFocusOutside,onRootContentClose:i.onRootContentClose,...a};return o.viewport?(0,n.jsx)(q,{forceMount:r,...c,ref:s}):(0,n.jsx)(f.C,{present:r||u,children:(0,n.jsx)(Z,{"data-state":es(u),...c,ref:s,onPointerEnter:(0,l.m)(e.onPointerEnter,o.onContentEnter),onPointerLeave:(0,l.m)(e.onPointerLeave,ec(o.onContentLeave)),style:{pointerEvents:!u&&o.isRootMenu?"none":void 0,...c.style}})})});J.displayName=$;var q=a.forwardRef((e,t)=>{let{onViewportContentChange:r,onViewportContentRemove:n}=k($,e.__scopeNavigationMenu);return(0,p.N)(()=>{r(e.value,{ref:t,...e})},[e,t,r]),(0,p.N)(()=>()=>n(e.value),[e.value,n]),null}),Y="navigationMenu.rootContentDismiss",Z=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:o,triggerRef:i,focusProxyRef:s,wasEscapeCloseRef:u,onRootContentClose:c,onContentFocusOutside:f,...v}=e,m=k($,r),p=a.useRef(null),g=(0,d.s)(p,t),x=eu(m.baseId,o),w=ed(m.baseId,o),b=y(r),N=a.useRef(null),{onItemDismiss:j}=m;a.useEffect(()=>{let e=p.current;if(m.isRootMenu&&e){let t=()=>{var t;j(),c(),e.contains(document.activeElement)&&(null==(t=i.current)||t.focus())};return e.addEventListener(Y,t),()=>e.removeEventListener(Y,t)}},[m.isRootMenu,e.value,i,j,c]);let C=a.useMemo(()=>{let e=b().map(e=>e.value);"rtl"===m.dir&&e.reverse();let t=e.indexOf(m.value),r=e.indexOf(m.previousValue),n=o===m.value,a=r===e.indexOf(o);if(!n&&!a)return N.current;let i=(()=>{if(t!==r){if(n&&-1!==r)return t>r?"from-end":"from-start";if(a&&-1!==t)return t>r?"to-start":"to-end"}return null})();return N.current=i,i},[m.previousValue,m.value,m.dir,b,o]);return(0,n.jsx)(er,{asChild:!0,children:(0,n.jsx)(h.qW,{id:w,"aria-labelledby":x,"data-motion":C,"data-orientation":m.orientation,...v,ref:g,disableOutsidePointerEvents:!1,onDismiss:()=>{var e;let t=new Event(Y,{bubbles:!0,cancelable:!0});null==(e=p.current)||e.dispatchEvent(t)},onFocusOutside:(0,l.m)(e.onFocusOutside,e=>{var t;f();let r=e.target;(null==(t=m.rootNavigationMenu)?void 0:t.contains(r))&&e.preventDefault()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{var t;let r=e.target,n=b().some(e=>{var t;return null==(t=e.ref.current)?void 0:t.contains(r)}),a=m.isRootMenu&&(null==(t=m.viewport)?void 0:t.contains(r));(n||a||!m.isRootMenu)&&e.preventDefault()}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{let t=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!t){let t=eo(e.currentTarget),n=document.activeElement,a=t.findIndex(e=>e===n);if(ei(e.shiftKey?t.slice(0,a).reverse():t.slice(a+1,t.length)))e.preventDefault();else{var r;null==(r=s.current)||r.focus()}}}),onEscapeKeyDown:(0,l.m)(e.onEscapeKeyDown,e=>{u.current=!0})})})}),X="NavigationMenuViewport",ee=a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,o=!!k(X,e.__scopeNavigationMenu).value;return(0,n.jsx)(f.C,{present:r||o,children:(0,n.jsx)(et,{...a,ref:t})})});ee.displayName=X;var et=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,children:o,...i}=e,u=k(X,r),c=(0,d.s)(t,u.onViewportChange),v=A($,e.__scopeNavigationMenu),[m,h]=a.useState(null),[p,g]=a.useState(null),x=m?(null==m?void 0:m.width)+"px":void 0,w=m?(null==m?void 0:m.height)+"px":void 0,b=!!u.value,N=b?u.value:u.previousValue;return el(p,()=>{p&&h({width:p.offsetWidth,height:p.offsetHeight})}),(0,n.jsx)(s.sG.div,{"data-state":es(b),"data-orientation":u.orientation,...i,ref:c,style:{pointerEvents:!b&&u.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":x,"--radix-navigation-menu-viewport-height":w,...i.style},onPointerEnter:(0,l.m)(e.onPointerEnter,u.onContentEnter),onPointerLeave:(0,l.m)(e.onPointerLeave,ec(u.onContentLeave)),children:Array.from(v.items).map(e=>{let[t,{ref:r,forceMount:a,...o}]=e,i=N===t;return(0,n.jsx)(f.C,{present:a||i,children:(0,n.jsx)(Z,{...o,ref:(0,d.t)(r,e=>{i&&e&&g(e)})})},t)})})}),er=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...a}=e,o=k("FocusGroup",r);return(0,n.jsx)(C.Provider,{scope:r,children:(0,n.jsx)(C.Slot,{scope:r,children:(0,n.jsx)(s.sG.div,{dir:o.dir,...a,ref:t})})})}),en=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],ea=a.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...a}=e,o=R(r),i=k("FocusGroupItem",r);return(0,n.jsx)(C.ItemSlot,{scope:r,children:(0,n.jsx)(s.sG.button,{...a,ref:t,onKeyDown:(0,l.m)(e.onKeyDown,e=>{if(["Home","End",...en].includes(e.key)){let t=o().map(e=>e.ref.current);if(["rtl"===i.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(e.key)&&t.reverse(),en.includes(e.key)){let r=t.indexOf(e.currentTarget);t=t.slice(r+1)}setTimeout(()=>ei(t)),e.preventDefault()}})})})});function eo(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function ei(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}function el(e,t){let r=(0,g.c)(t);(0,p.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}function es(e){return e?"open":"closed"}function eu(e,t){return"".concat(e,"-trigger-").concat(t)}function ed(e,t){return"".concat(e,"-content-").concat(t)}function ec(e){return t=>"mouse"===t.pointerType?e(t):void 0}var ef=r(9688);let ev=_,em=S,eh=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,n.jsx)(W,{ref:t,className:(0,ef.QP)("list-none",r),...o,children:a})});eh.displayName=W.displayName;let ep=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,n.jsx)(G,{ref:t,className:(0,ef.QP)("data-[state=open]:bg-fd-accent/50",r),...o,children:a})});ep.displayName=G.displayName;let eg=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(J,{ref:t,className:(0,ef.QP)("absolute inset-x-0 top-0 data-[motion=from-end]:animate-fd-enterFromRight data-[motion=from-start]:animate-fd-enterFromLeft data-[motion=to-end]:animate-fd-exitToRight data-[motion=to-start]:animate-fd-exitToLeft",r),...a})});eg.displayName=J.displayName;let ex=U,ew=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:"flex w-full justify-center",children:(0,n.jsx)(ee,{...a,className:(0,ef.QP)("relative h-(--radix-navigation-menu-viewport-height) w-full origin-[top_center] overflow-hidden text-fd-popover-foreground transition-[width,height] duration-300 data-[state=closed]:animate-fd-nav-menu-out data-[state=open]:animate-fd-nav-menu-in",r)})})});ew.displayName=ee.displayName},7936:(e,t,r)=>{r.d(t,{r:()=>n});let n=(0,r(2085).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 p-1 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5"}}})},9949:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var n=r(9600),a=r(2115),o=r(5155),i=(0,a.forwardRef)(({href:e="#",external:t=!(e.startsWith("/")||e.startsWith("#")||e.startsWith(".")),prefetch:r,...a},i)=>t?(0,o.jsx)("a",{ref:i,href:e,rel:"noreferrer noopener",target:"_blank",...a,children:a.children}):(0,o.jsx)(n.N_,{ref:i,href:e,prefetch:r,...a}));i.displayName="Link",r(9189)}}]);