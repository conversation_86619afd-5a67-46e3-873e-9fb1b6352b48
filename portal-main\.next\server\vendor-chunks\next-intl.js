"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createNavigation)\n/* harmony export */ });\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getServerLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js\");\n\n\n\nfunction createNavigation(routing) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const {\n    config,\n    ...fns\n  } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], routing);\n  function notSupported(hookName) {\n    return () => {\n      throw new Error(`\\`${hookName}\\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`);\n    };\n  }\n  return {\n    ...fns,\n    usePathname: notSupported('usePathname'),\n    useRouter: notSupported('useRouter')\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStFO0FBQzVCOztBQUVuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxFQUFFLGdGQUF5QixDQUFDLDJEQUFlO0FBQy9DO0FBQ0E7QUFDQSwyQkFBMkIsU0FBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxuYXZpZ2F0aW9uXFxyZWFjdC1zZXJ2ZXJcXGNyZWF0ZU5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZVNoYXJlZE5hdmlnYXRpb25GbnMgZnJvbSAnLi4vc2hhcmVkL2NyZWF0ZVNoYXJlZE5hdmlnYXRpb25GbnMuanMnO1xuaW1wb3J0IGdldFNlcnZlckxvY2FsZSBmcm9tICcuL2dldFNlcnZlckxvY2FsZS5qcyc7XG5cbmZ1bmN0aW9uIGNyZWF0ZU5hdmlnYXRpb24ocm91dGluZykge1xuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVudXNlZC12YXJzXG4gIGNvbnN0IHtcbiAgICBjb25maWcsXG4gICAgLi4uZm5zXG4gIH0gPSBjcmVhdGVTaGFyZWROYXZpZ2F0aW9uRm5zKGdldFNlcnZlckxvY2FsZSwgcm91dGluZyk7XG4gIGZ1bmN0aW9uIG5vdFN1cHBvcnRlZChob29rTmFtZSkge1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFxcYCR7aG9va05hbWV9XFxgIGlzIG5vdCBzdXBwb3J0ZWQgaW4gU2VydmVyIENvbXBvbmVudHMuIFlvdSBjYW4gdXNlIHRoaXMgaG9vayBpZiB5b3UgY29udmVydCB0aGUgY2FsbGluZyBjb21wb25lbnQgdG8gYSBDbGllbnQgQ29tcG9uZW50LmApO1xuICAgIH07XG4gIH1cbiAgcmV0dXJuIHtcbiAgICAuLi5mbnMsXG4gICAgdXNlUGF0aG5hbWU6IG5vdFN1cHBvcnRlZCgndXNlUGF0aG5hbWUnKSxcbiAgICB1c2VSb3V0ZXI6IG5vdFN1cHBvcnRlZCgndXNlUm91dGVyJylcbiAgfTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlTmF2aWdhdGlvbiBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getServerLocale)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n/**\n * This is only moved to a separate module for easier mocking in\n * `../createNavigatoin.test.tsx` in order to avoid suspending.\n */\nasync function getServerLocale() {\n  const config = await (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n  return config.locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErRDs7QUFFL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw2RUFBUztBQUNoQztBQUNBOztBQUVzQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxuYXZpZ2F0aW9uXFxyZWFjdC1zZXJ2ZXJcXGdldFNlcnZlckxvY2FsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4uLy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnLmpzJztcblxuLyoqXG4gKiBUaGlzIGlzIG9ubHkgbW92ZWQgdG8gYSBzZXBhcmF0ZSBtb2R1bGUgZm9yIGVhc2llciBtb2NraW5nIGluXG4gKiBgLi4vY3JlYXRlTmF2aWdhdG9pbi50ZXN0LnRzeGAgaW4gb3JkZXIgdG8gYXZvaWQgc3VzcGVuZGluZy5cbiAqL1xuYXN5bmMgZnVuY3Rpb24gZ2V0U2VydmVyTG9jYWxlKCkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcoKTtcbiAgcmV0dXJuIGNvbmZpZy5sb2NhbGU7XG59XG5cbmV4cG9ydCB7IGdldFNlcnZlckxvY2FsZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\development\\\\navigation\\\\shared\\\\BaseLink.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\next-intl\\dist\\esm\\development\\navigation\\shared\\BaseLink.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createSharedNavigationFns)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/use.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_3__.receiveRoutingConfig)(routing || {});\n  {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.validateReceivedConfig)(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isLocalizableHref)(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(localePromiseOrValue) ? (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || undefined) : pathname;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_BaseLink_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.serializeSearchParams)(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compileLocalizedPathname)({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.normalizeNameOrNameWithParams)(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.applyPathnamePrefix)(pathname, locale, config, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect);\n  const permanentRedirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ applyPathnamePrefix),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ compileLocalizedPathname),\n/* harmony export */   getBasePath: () => (/* binding */ getBasePath),\n/* harmony export */   getRoute: () => (/* binding */ getRoute),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ normalizeNameOrNameWithParams),\n/* harmony export */   serializeSearchParams: () => (/* binding */ serializeSearchParams),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ validateReceivedConfig)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vc2hhcmVkL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQThLOztBQUU5SztBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsc0VBQW9CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsSUFBSTtBQUN0QztBQUNBLFVBQVU7QUFDVix5QkFBeUIsSUFBSTtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLGVBQWUsd0VBQXNCO0FBQ3JDO0FBQ0E7QUFDQSx5RkFBeUYsU0FBUyxZQUFZLHVCQUF1QjtBQUNySTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsb0VBQWtCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGlFQUFlO0FBQ3pCO0FBQ0E7QUFDQSxNQUFNO0FBQ04sVUFBVSxpRUFBZSxDQUFDLHNFQUFvQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLElBQUksU0FBUyxtRUFBaUI7QUFDOUI7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0VBQWMsQ0FBQyxpRUFBZTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXG5hdmlnYXRpb25cXHNoYXJlZFxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U29ydGVkUGF0aG5hbWVzLCBtYXRjaGVzUGF0aG5hbWUsIGlzTG9jYWxpemFibGVIcmVmLCBwcmVmaXhQYXRobmFtZSwgZ2V0TG9jYWxpemVkVGVtcGxhdGUsIG5vcm1hbGl6ZVRyYWlsaW5nU2xhc2gsIGdldExvY2FsZVByZWZpeCB9IGZyb20gJy4uLy4uL3NoYXJlZC91dGlscy5qcyc7XG5cbi8vIE1pbm9yIGZhbHNlIHBvc2l0aXZlOiBBIHJvdXRlIHRoYXQgaGFzIGJvdGggb3B0aW9uYWwgYW5kXG4vLyByZXF1aXJlZCBwYXJhbXMgd2lsbCBhbGxvdyBvcHRpb25hbCBwYXJhbXMuXG5cbi8vIEZvciBgTGlua2BcblxuLy8gRm9yIGBnZXRQYXRobmFtZWAgKGhlbmNlIGFsc28gaXRzIGNvbnN1bWVyczogYHJlZGlyZWN0YCwgYHVzZVJvdXRlcmAsIOKApilcblxuZnVuY3Rpb24gbm9ybWFsaXplTmFtZU9yTmFtZVdpdGhQYXJhbXMoaHJlZikge1xuICByZXR1cm4gdHlwZW9mIGhyZWYgPT09ICdzdHJpbmcnID8ge1xuICAgIHBhdGhuYW1lOiBocmVmXG4gIH0gOiBocmVmO1xufVxuZnVuY3Rpb24gc2VyaWFsaXplU2VhcmNoUGFyYW1zKHNlYXJjaFBhcmFtcykge1xuICBmdW5jdGlvbiBzZXJpYWxpemVWYWx1ZSh2YWx1ZSkge1xuICAgIHJldHVybiBTdHJpbmcodmFsdWUpO1xuICB9XG4gIGNvbnN0IHVybFNlYXJjaFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc2VhcmNoUGFyYW1zKSkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgdmFsdWUuZm9yRWFjaChjdXIgPT4ge1xuICAgICAgICB1cmxTZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgc2VyaWFsaXplVmFsdWUoY3VyKSk7XG4gICAgICB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgdXJsU2VhcmNoUGFyYW1zLnNldChrZXksIHNlcmlhbGl6ZVZhbHVlKHZhbHVlKSk7XG4gICAgfVxuICB9XG4gIHJldHVybiAnPycgKyB1cmxTZWFyY2hQYXJhbXMudG9TdHJpbmcoKTtcbn1cbmZ1bmN0aW9uIGNvbXBpbGVMb2NhbGl6ZWRQYXRobmFtZSh7XG4gIHBhdGhuYW1lLFxuICBsb2NhbGUsXG4gIHBhcmFtcyxcbiAgcGF0aG5hbWVzLFxuICBxdWVyeVxufSkge1xuICBmdW5jdGlvbiBnZXROYW1lZFBhdGgodmFsdWUpIHtcbiAgICBsZXQgbmFtZWRQYXRoID0gcGF0aG5hbWVzW3ZhbHVlXTtcbiAgICBpZiAoIW5hbWVkUGF0aCkge1xuICAgICAgLy8gVW5rbm93biBwYXRobmFtZXNcbiAgICAgIG5hbWVkUGF0aCA9IHZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gbmFtZWRQYXRoO1xuICB9XG4gIGZ1bmN0aW9uIGNvbXBpbGVQYXRoKG5hbWVkUGF0aCwgaW50ZXJuYWxQYXRobmFtZSkge1xuICAgIGNvbnN0IHRlbXBsYXRlID0gZ2V0TG9jYWxpemVkVGVtcGxhdGUobmFtZWRQYXRoLCBsb2NhbGUsIGludGVybmFsUGF0aG5hbWUpO1xuICAgIGxldCBjb21waWxlZCA9IHRlbXBsYXRlO1xuICAgIGlmIChwYXJhbXMpIHtcbiAgICAgIE9iamVjdC5lbnRyaWVzKHBhcmFtcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgICAgIGxldCByZWdleHAsIHJlcGxhY2VyO1xuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICByZWdleHAgPSBgKFxcXFxbKT9cXFxcWy4uLiR7a2V5fVxcXFxdKFxcXFxdKT9gO1xuICAgICAgICAgIHJlcGxhY2VyID0gdmFsdWUubWFwKHYgPT4gU3RyaW5nKHYpKS5qb2luKCcvJyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmVnZXhwID0gYFxcXFxbJHtrZXl9XFxcXF1gO1xuICAgICAgICAgIHJlcGxhY2VyID0gU3RyaW5nKHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICBjb21waWxlZCA9IGNvbXBpbGVkLnJlcGxhY2UobmV3IFJlZ0V4cChyZWdleHAsICdnJyksIHJlcGxhY2VyKTtcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENsZWFuIHVwIG9wdGlvbmFsIGNhdGNoLWFsbCBzZWdtZW50cyB0aGF0IHdlcmUgbm90IHJlcGxhY2VkXG4gICAgY29tcGlsZWQgPSBjb21waWxlZC5yZXBsYWNlKC9cXFtcXFtcXC5cXC5cXC4uK1xcXVxcXS9nLCAnJyk7XG4gICAgY29tcGlsZWQgPSBub3JtYWxpemVUcmFpbGluZ1NsYXNoKGNvbXBpbGVkKTtcbiAgICBpZiAoY29tcGlsZWQuaW5jbHVkZXMoJ1snKSkge1xuICAgICAgLy8gTmV4dC5qcyB0aHJvd3MgYW55d2F5LCB0aGVyZWZvcmUgYmV0dGVyIHByb3ZpZGUgYSBtb3JlIGhlbHBmdWwgZXJyb3IgbWVzc2FnZVxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnN1ZmZpY2llbnQgcGFyYW1zIHByb3ZpZGVkIGZvciBsb2NhbGl6ZWQgcGF0aG5hbWUuXFxuVGVtcGxhdGU6ICR7dGVtcGxhdGV9XFxuUGFyYW1zOiAke0pTT04uc3RyaW5naWZ5KHBhcmFtcyl9YCk7XG4gICAgfVxuICAgIGlmIChxdWVyeSkge1xuICAgICAgY29tcGlsZWQgKz0gc2VyaWFsaXplU2VhcmNoUGFyYW1zKHF1ZXJ5KTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbXBpbGVkO1xuICB9XG4gIGlmICh0eXBlb2YgcGF0aG5hbWUgPT09ICdzdHJpbmcnKSB7XG4gICAgY29uc3QgbmFtZWRQYXRoID0gZ2V0TmFtZWRQYXRoKHBhdGhuYW1lKTtcbiAgICBjb25zdCBjb21waWxlZCA9IGNvbXBpbGVQYXRoKG5hbWVkUGF0aCwgcGF0aG5hbWUpO1xuICAgIHJldHVybiBjb21waWxlZDtcbiAgfSBlbHNlIHtcbiAgICBjb25zdCB7XG4gICAgICBwYXRobmFtZTogaW50ZXJuYWxQYXRobmFtZSxcbiAgICAgIC4uLnJlc3RcbiAgICB9ID0gcGF0aG5hbWU7XG4gICAgY29uc3QgbmFtZWRQYXRoID0gZ2V0TmFtZWRQYXRoKGludGVybmFsUGF0aG5hbWUpO1xuICAgIGNvbnN0IGNvbXBpbGVkID0gY29tcGlsZVBhdGgobmFtZWRQYXRoLCBpbnRlcm5hbFBhdGhuYW1lKTtcbiAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAuLi5yZXN0LFxuICAgICAgcGF0aG5hbWU6IGNvbXBpbGVkXG4gICAgfTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG59XG5mdW5jdGlvbiBnZXRSb3V0ZShsb2NhbGUsIHBhdGhuYW1lLCBwYXRobmFtZXMpIHtcbiAgY29uc3Qgc29ydGVkUGF0aG5hbWVzID0gZ2V0U29ydGVkUGF0aG5hbWVzKE9iamVjdC5rZXlzKHBhdGhuYW1lcykpO1xuICBjb25zdCBkZWNvZGVkID0gZGVjb2RlVVJJKHBhdGhuYW1lKTtcbiAgZm9yIChjb25zdCBpbnRlcm5hbFBhdGhuYW1lIG9mIHNvcnRlZFBhdGhuYW1lcykge1xuICAgIGNvbnN0IGxvY2FsaXplZFBhdGhuYW1lc09yUGF0aG5hbWUgPSBwYXRobmFtZXNbaW50ZXJuYWxQYXRobmFtZV07XG4gICAgaWYgKHR5cGVvZiBsb2NhbGl6ZWRQYXRobmFtZXNPclBhdGhuYW1lID09PSAnc3RyaW5nJykge1xuICAgICAgY29uc3QgbG9jYWxpemVkUGF0aG5hbWUgPSBsb2NhbGl6ZWRQYXRobmFtZXNPclBhdGhuYW1lO1xuICAgICAgaWYgKG1hdGNoZXNQYXRobmFtZShsb2NhbGl6ZWRQYXRobmFtZSwgZGVjb2RlZCkpIHtcbiAgICAgICAgcmV0dXJuIGludGVybmFsUGF0aG5hbWU7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmIChtYXRjaGVzUGF0aG5hbWUoZ2V0TG9jYWxpemVkVGVtcGxhdGUobG9jYWxpemVkUGF0aG5hbWVzT3JQYXRobmFtZSwgbG9jYWxlLCBpbnRlcm5hbFBhdGhuYW1lKSwgZGVjb2RlZCkpIHtcbiAgICAgICAgcmV0dXJuIGludGVybmFsUGF0aG5hbWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBwYXRobmFtZTtcbn1cbmZ1bmN0aW9uIGdldEJhc2VQYXRoKHBhdGhuYW1lLCB3aW5kb3dQYXRobmFtZSA9IHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSkge1xuICBpZiAocGF0aG5hbWUgPT09ICcvJykge1xuICAgIHJldHVybiB3aW5kb3dQYXRobmFtZTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gd2luZG93UGF0aG5hbWUucmVwbGFjZShwYXRobmFtZSwgJycpO1xuICB9XG59XG5mdW5jdGlvbiBhcHBseVBhdGhuYW1lUHJlZml4KHBhdGhuYW1lLCBsb2NhbGUsIHJvdXRpbmcsIGZvcmNlKSB7XG4gIGNvbnN0IHtcbiAgICBtb2RlXG4gIH0gPSByb3V0aW5nLmxvY2FsZVByZWZpeDtcbiAgbGV0IHNob3VsZFByZWZpeDtcbiAgaWYgKGZvcmNlICE9PSB1bmRlZmluZWQpIHtcbiAgICBzaG91bGRQcmVmaXggPSBmb3JjZTtcbiAgfSBlbHNlIGlmIChpc0xvY2FsaXphYmxlSHJlZihwYXRobmFtZSkpIHtcbiAgICBpZiAobW9kZSA9PT0gJ2Fsd2F5cycpIHtcbiAgICAgIHNob3VsZFByZWZpeCA9IHRydWU7XG4gICAgfSBlbHNlIGlmIChtb2RlID09PSAnYXMtbmVlZGVkJykge1xuICAgICAgc2hvdWxkUHJlZml4ID0gcm91dGluZy5kb21haW5zID9cbiAgICAgIC8vIFNpbmNlIGxvY2FsZXMgYXJlIHVuaXF1ZSBwZXIgZG9tYWluLCBhbnkgbG9jYWxlIHRoYXQgaXMgYVxuICAgICAgLy8gZGVmYXVsdCBsb2NhbGUgb2YgYSBkb21haW4gZG9lc24ndCByZXF1aXJlIGEgcHJlZml4XG4gICAgICAhcm91dGluZy5kb21haW5zLnNvbWUoY3VyID0+IGN1ci5kZWZhdWx0TG9jYWxlID09PSBsb2NhbGUpIDogbG9jYWxlICE9PSByb3V0aW5nLmRlZmF1bHRMb2NhbGU7XG4gICAgfVxuICB9XG4gIHJldHVybiBzaG91bGRQcmVmaXggPyBwcmVmaXhQYXRobmFtZShnZXRMb2NhbGVQcmVmaXgobG9jYWxlLCByb3V0aW5nLmxvY2FsZVByZWZpeCksIHBhdGhuYW1lKSA6IHBhdGhuYW1lO1xufVxuZnVuY3Rpb24gdmFsaWRhdGVSZWNlaXZlZENvbmZpZyhjb25maWcpIHtcbiAgaWYgKGNvbmZpZy5sb2NhbGVQcmVmaXg/Lm1vZGUgPT09ICdhcy1uZWVkZWQnICYmICEoJ2RlZmF1bHRMb2NhbGUnIGluIGNvbmZpZykpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJgbG9jYWxlUHJlZml4OiAnYXMtbmVlZGVkJyByZXF1aXJlcyBhIGBkZWZhdWx0TG9jYWxlYC5cIik7XG4gIH1cbn1cblxuZXhwb3J0IHsgYXBwbHlQYXRobmFtZVByZWZpeCwgY29tcGlsZUxvY2FsaXplZFBhdGhuYW1lLCBnZXRCYXNlUGF0aCwgZ2V0Um91dGUsIG5vcm1hbGl6ZU5hbWVPck5hbWVXaXRoUGFyYW1zLCBzZXJpYWxpemVTZWFyY2hQYXJhbXMsIHZhbGlkYXRlUmVjZWl2ZWRDb25maWcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProviderServer)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getConfigNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\");\n/* harmony import */ var _server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/react-server/getFormats.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\");\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\");\n/* harmony import */ var _server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\");\n\n\n\n\n\n\n\n\nasync function NextIntlClientProviderServer({\n  formats,\n  locale,\n  messages,\n  now,\n  timeZone,\n  ...rest\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n  // We need to be careful about potentially reading from headers here.\n  // See https://github.com/amannn/next-intl/issues/631\n  , {\n    formats: formats === undefined ? await (0,_server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])() : formats,\n    locale: locale ?? (await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])()),\n    messages: messages === undefined ? await (0,_server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])() : messages\n    // Note that we don't assign a default for `now` here,\n    // we only read one from the request config - if any.\n    // Otherwise this would cause a `dynamicIO` error.\n    ,\n    now: now ?? (await (0,_server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()),\n    timeZone: timeZone ?? (await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])()),\n    ...rest\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWtFO0FBQ0o7QUFDVztBQUNqQztBQUN3QjtBQUNBO0FBQ0U7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELHNCQUFzQixzREFBRyxDQUFDLHlFQUFzQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsOEVBQVU7QUFDckQsNkJBQTZCLDZFQUFlO0FBQzVDLDZDQUE2QywrRUFBVztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBWTtBQUNuQyxpQ0FBaUMsK0VBQVc7QUFDNUM7QUFDQSxHQUFHO0FBQ0g7O0FBRW1EIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHJlYWN0LXNlcnZlclxcTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0Q29uZmlnTm93IGZyb20gJy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzJztcbmltcG9ydCBnZXRGb3JtYXRzIGZyb20gJy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyc7XG5pbXBvcnQgTmV4dEludGxDbGllbnRQcm92aWRlciBmcm9tICcuLi9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qcyc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdyZWFjdC9qc3gtcnVudGltZSc7XG5pbXBvcnQgZ2V0VGltZVpvbmUgZnJvbSAnLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qcyc7XG5pbXBvcnQgZ2V0TWVzc2FnZXMgZnJvbSAnLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRNZXNzYWdlcy5qcyc7XG5pbXBvcnQgZ2V0TG9jYWxlQ2FjaGVkIGZyb20gJy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlcih7XG4gIGZvcm1hdHMsXG4gIGxvY2FsZSxcbiAgbWVzc2FnZXMsXG4gIG5vdyxcbiAgdGltZVpvbmUsXG4gIC4uLnJlc3Rcbn0pIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9qc3goTmV4dEludGxDbGllbnRQcm92aWRlclxuICAvLyBXZSBuZWVkIHRvIGJlIGNhcmVmdWwgYWJvdXQgcG90ZW50aWFsbHkgcmVhZGluZyBmcm9tIGhlYWRlcnMgaGVyZS5cbiAgLy8gU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9hbWFubm4vbmV4dC1pbnRsL2lzc3Vlcy82MzFcbiAgLCB7XG4gICAgZm9ybWF0czogZm9ybWF0cyA9PT0gdW5kZWZpbmVkID8gYXdhaXQgZ2V0Rm9ybWF0cygpIDogZm9ybWF0cyxcbiAgICBsb2NhbGU6IGxvY2FsZSA/PyAoYXdhaXQgZ2V0TG9jYWxlQ2FjaGVkKCkpLFxuICAgIG1lc3NhZ2VzOiBtZXNzYWdlcyA9PT0gdW5kZWZpbmVkID8gYXdhaXQgZ2V0TWVzc2FnZXMoKSA6IG1lc3NhZ2VzXG4gICAgLy8gTm90ZSB0aGF0IHdlIGRvbid0IGFzc2lnbiBhIGRlZmF1bHQgZm9yIGBub3dgIGhlcmUsXG4gICAgLy8gd2Ugb25seSByZWFkIG9uZSBmcm9tIHRoZSByZXF1ZXN0IGNvbmZpZyAtIGlmIGFueS5cbiAgICAvLyBPdGhlcndpc2UgdGhpcyB3b3VsZCBjYXVzZSBhIGBkeW5hbWljSU9gIGVycm9yLlxuICAgICxcbiAgICBub3c6IG5vdyA/PyAoYXdhaXQgZ2V0Q29uZmlnTm93KCkpLFxuICAgIHRpbWVab25lOiB0aW1lWm9uZSA/PyAoYXdhaXQgZ2V0VGltZVpvbmUoKSksXG4gICAgLi4ucmVzdFxuICB9KTtcbn1cblxuZXhwb3J0IHsgTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlciBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/config.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ receiveRoutingConfig)\n/* harmony export */ });\nfunction receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxyb3V0aW5nXFxjb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVjZWl2ZVJvdXRpbmdDb25maWcoaW5wdXQpIHtcbiAgcmV0dXJuIHtcbiAgICAuLi5pbnB1dCxcbiAgICBsb2NhbGVQcmVmaXg6IHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcoaW5wdXQubG9jYWxlUHJlZml4KSxcbiAgICBsb2NhbGVDb29raWU6IHJlY2VpdmVMb2NhbGVDb29raWUoaW5wdXQubG9jYWxlQ29va2llKSxcbiAgICBsb2NhbGVEZXRlY3Rpb246IGlucHV0LmxvY2FsZURldGVjdGlvbiA/PyB0cnVlLFxuICAgIGFsdGVybmF0ZUxpbmtzOiBpbnB1dC5hbHRlcm5hdGVMaW5rcyA/PyB0cnVlXG4gIH07XG59XG5mdW5jdGlvbiByZWNlaXZlTG9jYWxlQ29va2llKGxvY2FsZUNvb2tpZSkge1xuICByZXR1cm4gbG9jYWxlQ29va2llID8/IHRydWUgPyB7XG4gICAgbmFtZTogJ05FWFRfTE9DQUxFJyxcbiAgICBzYW1lU2l0ZTogJ2xheCcsXG4gICAgLi4uKHR5cGVvZiBsb2NhbGVDb29raWUgPT09ICdvYmplY3QnICYmIGxvY2FsZUNvb2tpZSlcblxuICAgIC8vIGBwYXRoYCBuZWVkcyB0byBiZSBwcm92aWRlZCBiYXNlZCBvbiBhIGRldGVjdGVkIGJhc2UgcGF0aFxuICAgIC8vIHRoYXQgZGVwZW5kcyBvbiB0aGUgZW52aXJvbm1lbnQgd2hlbiBzZXR0aW5nIGEgY29va2llXG4gIH0gOiBmYWxzZTtcbn1cbmZ1bmN0aW9uIHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcobG9jYWxlUHJlZml4KSB7XG4gIHJldHVybiB0eXBlb2YgbG9jYWxlUHJlZml4ID09PSAnb2JqZWN0JyA/IGxvY2FsZVByZWZpeCA6IHtcbiAgICBtb2RlOiBsb2NhbGVQcmVmaXggfHwgJ2Fsd2F5cydcbiAgfTtcbn1cblxuZXhwb3J0IHsgcmVjZWl2ZVJvdXRpbmdDb25maWcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/defineRouting.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defineRouting)\n/* harmony export */ });\nfunction defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxS0FBcUssT0FBTyxnQkFBZ0IscUNBQXFDO0FBQ2pPO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxyb3V0aW5nXFxkZWZpbmVSb3V0aW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGRlZmluZVJvdXRpbmcoY29uZmlnKSB7XG4gIGlmIChjb25maWcuZG9tYWlucykge1xuICAgIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihjb25maWcuZG9tYWlucyk7XG4gIH1cbiAgcmV0dXJuIGNvbmZpZztcbn1cbmZ1bmN0aW9uIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihkb21haW5zKSB7XG4gIGNvbnN0IGRvbWFpbnNCeUxvY2FsZSA9IG5ldyBNYXAoKTtcbiAgZm9yIChjb25zdCB7XG4gICAgZG9tYWluLFxuICAgIGxvY2FsZXNcbiAgfSBvZiBkb21haW5zKSB7XG4gICAgZm9yIChjb25zdCBsb2NhbGUgb2YgbG9jYWxlcykge1xuICAgICAgY29uc3QgbG9jYWxlRG9tYWlucyA9IGRvbWFpbnNCeUxvY2FsZS5nZXQobG9jYWxlKSB8fCBuZXcgU2V0KCk7XG4gICAgICBsb2NhbGVEb21haW5zLmFkZChkb21haW4pO1xuICAgICAgZG9tYWluc0J5TG9jYWxlLnNldChsb2NhbGUsIGxvY2FsZURvbWFpbnMpO1xuICAgIH1cbiAgfVxuICBjb25zdCBkdXBsaWNhdGVMb2NhbGVNZXNzYWdlcyA9IEFycmF5LmZyb20oZG9tYWluc0J5TG9jYWxlLmVudHJpZXMoKSkuZmlsdGVyKChbLCBsb2NhbGVEb21haW5zXSkgPT4gbG9jYWxlRG9tYWlucy5zaXplID4gMSkubWFwKChbbG9jYWxlLCBsb2NhbGVEb21haW5zXSkgPT4gYC0gXCIke2xvY2FsZX1cIiBpcyB1c2VkIGJ5OiAke0FycmF5LmZyb20obG9jYWxlRG9tYWlucykuam9pbignLCAnKX1gKTtcbiAgaWYgKGR1cGxpY2F0ZUxvY2FsZU1lc3NhZ2VzLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLndhcm4oJ0xvY2FsZXMgYXJlIGV4cGVjdGVkIHRvIGJlIHVuaXF1ZSBwZXIgZG9tYWluLCBidXQgZm91bmQgb3ZlcmxhcDpcXG4nICsgZHVwbGljYXRlTG9jYWxlTWVzc2FnZXMuam9pbignXFxuJykgKyAnXFxuUGxlYXNlIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9yb3V0aW5nI2RvbWFpbnMnKTtcbiAgfVxufVxuXG5leHBvcnQgeyBkZWZpbmVSb3V0aW5nIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ getRequestLocale)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\n\nasync function getHeadersImpl() {\n  const promiseOrValue = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n\n  // Compatibility with Next.js <15\n  return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || (await getLocaleFromHeader());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ getCachedRequestLocale),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ setCachedRequestLocale)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNENBQUs7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcUmVxdWVzdExvY2FsZUNhY2hlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuXG4vLyBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzL2Rpc2N1c3Npb25zLzU4ODYyXG5mdW5jdGlvbiBnZXRDYWNoZUltcGwoKSB7XG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIGxvY2FsZTogdW5kZWZpbmVkXG4gIH07XG4gIHJldHVybiB2YWx1ZTtcbn1cbmNvbnN0IGdldENhY2hlID0gY2FjaGUoZ2V0Q2FjaGVJbXBsKTtcbmZ1bmN0aW9uIGdldENhY2hlZFJlcXVlc3RMb2NhbGUoKSB7XG4gIHJldHVybiBnZXRDYWNoZSgpLmxvY2FsZTtcbn1cbmZ1bmN0aW9uIHNldENhY2hlZFJlcXVlc3RMb2NhbGUobG9jYWxlKSB7XG4gIGdldENhY2hlKCkubG9jYWxlID0gbG9jYWxlO1xufVxuXG5leHBvcnQgeyBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlLCBzZXRDYWNoZWRSZXF1ZXN0TG9jYWxlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./lib/next-intl-requests.ts\");\n/* harmony import */ var _validateLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\");\n\n\n\n\n\n\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__.getRequestLocale)();\n    }\n  };\n  let result = getConfig(params);\n  if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    (0,_validateLocale_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(receiveRuntimeConfigImpl);\nconst getFormatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.b);\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.d);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(next_intl_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"], localeOverride);\n  return {\n    ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.i)(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfigNow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getConfigNowImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.now;\n}\nconst getConfigNow = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigNowImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EscUJBQXFCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRDb25maWdOb3cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0Q29uZmlnTm93SW1wbChsb2NhbGUpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKGxvY2FsZSk7XG4gIHJldHVybiBjb25maWcubm93O1xufVxuY29uc3QgZ2V0Q29uZmlnTm93ID0gY2FjaGUoZ2V0Q29uZmlnTm93SW1wbCk7XG5cbmV4cG9ydCB7IGdldENvbmZpZ05vdyBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getDefaultNow.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getDefaultNow.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getDefaultNow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\nfunction defaultNow() {\n  // See https://next-intl.dev/docs/usage/dates-times#relative-times-server\n  return new Date();\n}\nconst getDefaultNow = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(defaultNow);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0RGVmYXVsdE5vdy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNENBQUs7O0FBRVMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldERlZmF1bHROb3cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIGRlZmF1bHROb3coKSB7XG4gIC8vIFNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy91c2FnZS9kYXRlcy10aW1lcyNyZWxhdGl2ZS10aW1lcy1zZXJ2ZXJcbiAgcmV0dXJuIG5ldyBEYXRlKCk7XG59XG5jb25zdCBnZXREZWZhdWx0Tm93ID0gY2FjaGUoZGVmYXVsdE5vdyk7XG5cbmV4cG9ydCB7IGdldERlZmF1bHROb3cgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getDefaultNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getFormatsCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.formats;\n}\nconst getFormats = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatsCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDUzs7QUFFdkM7QUFDQSx1QkFBdUIseURBQVM7QUFDaEM7QUFDQTtBQUNBLG1CQUFtQiw0Q0FBSzs7QUFFUyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0Rm9ybWF0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRGb3JtYXRzQ2FjaGVkSW1wbCgpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKCk7XG4gIHJldHVybiBjb25maWcuZm9ybWF0cztcbn1cbmNvbnN0IGdldEZvcm1hdHMgPSBjYWNoZShnZXRGb3JtYXRzQ2FjaGVkSW1wbCk7XG5cbmV4cG9ydCB7IGdldEZvcm1hdHMgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormatter.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getFormatter.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormatter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n/* harmony import */ var _getServerFormatter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getServerFormatter.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerFormatter.js\");\n\n\n\n\nasync function getFormatterCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return (0,_getServerFormatter_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(config);\n}\nconst getFormatterCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatterCachedImpl);\n\n/**\n * Returns a formatter based on the given locale.\n *\n * The formatter automatically receives the request config, but\n * you can override it by passing in additional options.\n */\nasync function getFormatter(opts) {\n  return getFormatterCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFDUztBQUNvQjs7QUFFM0Q7QUFDQSx1QkFBdUIseURBQVM7QUFDaEMsU0FBUyxrRUFBb0I7QUFDN0I7QUFDQSwyQkFBMkIsNENBQUs7O0FBRWhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFbUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldEZvcm1hdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuaW1wb3J0IGdldEZvcm1hdHRlckNhY2hlZCQxIGZyb20gJy4vZ2V0U2VydmVyRm9ybWF0dGVyLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0Rm9ybWF0dGVyQ2FjaGVkSW1wbChsb2NhbGUpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKGxvY2FsZSk7XG4gIHJldHVybiBnZXRGb3JtYXR0ZXJDYWNoZWQkMShjb25maWcpO1xufVxuY29uc3QgZ2V0Rm9ybWF0dGVyQ2FjaGVkID0gY2FjaGUoZ2V0Rm9ybWF0dGVyQ2FjaGVkSW1wbCk7XG5cbi8qKlxuICogUmV0dXJucyBhIGZvcm1hdHRlciBiYXNlZCBvbiB0aGUgZ2l2ZW4gbG9jYWxlLlxuICpcbiAqIFRoZSBmb3JtYXR0ZXIgYXV0b21hdGljYWxseSByZWNlaXZlcyB0aGUgcmVxdWVzdCBjb25maWcsIGJ1dFxuICogeW91IGNhbiBvdmVycmlkZSBpdCBieSBwYXNzaW5nIGluIGFkZGl0aW9uYWwgb3B0aW9ucy5cbiAqL1xuYXN5bmMgZnVuY3Rpb24gZ2V0Rm9ybWF0dGVyKG9wdHMpIHtcbiAgcmV0dXJuIGdldEZvcm1hdHRlckNhY2hlZChvcHRzPy5sb2NhbGUpO1xufVxuXG5leHBvcnQgeyBnZXRGb3JtYXR0ZXIgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormatter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getLocaleCached)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getLocaleCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.locale;\n}\nconst getLocaleCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getLocaleCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0Esd0JBQXdCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRMb2NhbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0TG9jYWxlQ2FjaGVkSW1wbCgpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKCk7XG4gIHJldHVybiBjb25maWcubG9jYWxlO1xufVxuY29uc3QgZ2V0TG9jYWxlQ2FjaGVkID0gY2FjaGUoZ2V0TG9jYWxlQ2FjaGVkSW1wbCk7XG5cbmV4cG9ydCB7IGdldExvY2FsZUNhY2hlZCBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getMessages),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ getMessagesFromConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nfunction getMessagesFromConfig(config) {\n  if (!config.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');\n  }\n  return config.messages;\n}\nasync function getMessagesCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return getMessagesFromConfig(config);\n}\nconst getMessagesCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getMessagesCachedImpl);\nasync function getMessages(opts) {\n  return getMessagesCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EsMEJBQTBCLDRDQUFLO0FBQy9CO0FBQ0E7QUFDQTs7QUFFeUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldE1lc3NhZ2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmZ1bmN0aW9uIGdldE1lc3NhZ2VzRnJvbUNvbmZpZyhjb25maWcpIHtcbiAgaWYgKCFjb25maWcubWVzc2FnZXMpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIG1lc3NhZ2VzIGZvdW5kLiBIYXZlIHlvdSBjb25maWd1cmVkIHRoZW0gY29ycmVjdGx5PyBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNtZXNzYWdlcycpO1xuICB9XG4gIHJldHVybiBjb25maWcubWVzc2FnZXM7XG59XG5hc3luYyBmdW5jdGlvbiBnZXRNZXNzYWdlc0NhY2hlZEltcGwobG9jYWxlKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZyhsb2NhbGUpO1xuICByZXR1cm4gZ2V0TWVzc2FnZXNGcm9tQ29uZmlnKGNvbmZpZyk7XG59XG5jb25zdCBnZXRNZXNzYWdlc0NhY2hlZCA9IGNhY2hlKGdldE1lc3NhZ2VzQ2FjaGVkSW1wbCk7XG5hc3luYyBmdW5jdGlvbiBnZXRNZXNzYWdlcyhvcHRzKSB7XG4gIHJldHVybiBnZXRNZXNzYWdlc0NhY2hlZChvcHRzPy5sb2NhbGUpO1xufVxuXG5leHBvcnQgeyBnZXRNZXNzYWdlcyBhcyBkZWZhdWx0LCBnZXRNZXNzYWdlc0Zyb21Db25maWcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getRequestConfig)\n/* harmony export */ });\n/**\n * Should be called in `i18n/request.ts` to create the configuration for the current request.\n */\nfunction getRequestConfig(createRequestConfig) {\n  return createRequestConfig;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0UmVxdWVzdENvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFNob3VsZCBiZSBjYWxsZWQgaW4gYGkxOG4vcmVxdWVzdC50c2AgdG8gY3JlYXRlIHRoZSBjb25maWd1cmF0aW9uIGZvciB0aGUgY3VycmVudCByZXF1ZXN0LlxuICovXG5mdW5jdGlvbiBnZXRSZXF1ZXN0Q29uZmlnKGNyZWF0ZVJlcXVlc3RDb25maWcpIHtcbiAgcmV0dXJuIGNyZWF0ZVJlcXVlc3RDb25maWc7XG59XG5cbmV4cG9ydCB7IGdldFJlcXVlc3RDb25maWcgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerFormatter.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getServerFormatter.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormatterCached)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var _getDefaultNow_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getDefaultNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getDefaultNow.js\");\n\n\n\n\nfunction getFormatterCachedImpl(config) {\n  return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.c)({\n    ...config,\n    // Only init when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    get now() {\n      return config.now ?? (0,_getDefaultNow_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    }\n  });\n}\nconst getFormatterCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatterCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0U2VydmVyRm9ybWF0dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFDa0I7QUFDRDs7QUFFL0M7QUFDQSxTQUFTLGdEQUFlO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLDZEQUFhO0FBQ3hDO0FBQ0EsR0FBRztBQUNIO0FBQ0EsMkJBQTJCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRTZXJ2ZXJGb3JtYXR0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjcmVhdGVGb3JtYXR0ZXIgfSBmcm9tICd1c2UtaW50bC9jb3JlJztcbmltcG9ydCBnZXREZWZhdWx0Tm93IGZyb20gJy4vZ2V0RGVmYXVsdE5vdy5qcyc7XG5cbmZ1bmN0aW9uIGdldEZvcm1hdHRlckNhY2hlZEltcGwoY29uZmlnKSB7XG4gIHJldHVybiBjcmVhdGVGb3JtYXR0ZXIoe1xuICAgIC4uLmNvbmZpZyxcbiAgICAvLyBPbmx5IGluaXQgd2hlbiBuZWNlc3NhcnkgdG8gYXZvaWQgdHJpZ2dlcmluZyBhIGBkeW5hbWljSU9gIGVycm9yXG4gICAgLy8gdW5uZWNlc3NhcmlseSAoYG5vd2AgaXMgb25seSBuZWVkZWQgZm9yIGBmb3JtYXQucmVsYXRpdmVUaW1lYClcbiAgICBnZXQgbm93KCkge1xuICAgICAgcmV0dXJuIGNvbmZpZy5ub3cgPz8gZ2V0RGVmYXVsdE5vdygpO1xuICAgIH1cbiAgfSk7XG59XG5jb25zdCBnZXRGb3JtYXR0ZXJDYWNoZWQgPSBjYWNoZShnZXRGb3JtYXR0ZXJDYWNoZWRJbXBsKTtcblxuZXhwb3J0IHsgZ2V0Rm9ybWF0dGVyQ2FjaGVkIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerFormatter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getServerTranslator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n\n\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getServerTranslatorImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0U2VydmVyVHJhbnNsYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDbUI7O0FBRWpEO0FBQ0EsU0FBUywrREFBZ0I7QUFDekI7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLDBCQUEwQiw0Q0FBSzs7QUFFVyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0U2VydmVyVHJhbnNsYXRvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNyZWF0ZVRyYW5zbGF0b3IgfSBmcm9tICd1c2UtaW50bC9jb3JlJztcblxuZnVuY3Rpb24gZ2V0U2VydmVyVHJhbnNsYXRvckltcGwoY29uZmlnLCBuYW1lc3BhY2UpIHtcbiAgcmV0dXJuIGNyZWF0ZVRyYW5zbGF0b3Ioe1xuICAgIC4uLmNvbmZpZyxcbiAgICBuYW1lc3BhY2VcbiAgfSk7XG59XG52YXIgZ2V0U2VydmVyVHJhbnNsYXRvciA9IGNhY2hlKGdldFNlcnZlclRyYW5zbGF0b3JJbXBsKTtcblxuZXhwb3J0IHsgZ2V0U2VydmVyVHJhbnNsYXRvciBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTimeZone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getTimeZoneCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.timeZone;\n}\nconst getTimeZoneCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTimeZoneCachedImpl);\nasync function getTimeZone(opts) {\n  return getTimeZoneCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBQ1M7O0FBRXZDO0FBQ0EsdUJBQXVCLHlEQUFTO0FBQ2hDO0FBQ0E7QUFDQSwwQkFBMEIsNENBQUs7QUFDL0I7QUFDQTtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0VGltZVpvbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0VGltZVpvbmVDYWNoZWRJbXBsKGxvY2FsZSkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcobG9jYWxlKTtcbiAgcmV0dXJuIGNvbmZpZy50aW1lWm9uZTtcbn1cbmNvbnN0IGdldFRpbWVab25lQ2FjaGVkID0gY2FjaGUoZ2V0VGltZVpvbmVDYWNoZWRJbXBsKTtcbmFzeW5jIGZ1bmN0aW9uIGdldFRpbWVab25lKG9wdHMpIHtcbiAgcmV0dXJuIGdldFRpbWVab25lQ2FjaGVkKG9wdHM/LmxvY2FsZSk7XG59XG5cbmV4cG9ydCB7IGdldFRpbWVab25lIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTranslations$1)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n/* harmony import */ var _getServerTranslator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getServerTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js\");\n\n\n\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return (0,_getServerTranslator_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(config, namespace);\n}\nvar getTranslations$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTranslations);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFDUztBQUNvQjs7QUFFM0Q7QUFDQTtBQUNBOztBQUVBOztBQUVBLHVDQUF1QyxrQkFBa0I7O0FBRXpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIseURBQVM7QUFDaEMsU0FBUyxtRUFBbUI7QUFDNUI7QUFDQSx3QkFBd0IsNENBQUs7O0FBRVciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGVlcEluc2lnaHRcXERvd25sb2Fkc1xccG9ydGFsLW1haW5cXHBvcnRhbC1tYWluXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldFRyYW5zbGF0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuaW1wb3J0IGdldFNlcnZlclRyYW5zbGF0b3IgZnJvbSAnLi9nZXRTZXJ2ZXJUcmFuc2xhdG9yLmpzJztcblxuLy8gTWFpbnRhaW5lciBub3RlOiBgZ2V0VHJhbnNsYXRpb25zYCBoYXMgdHdvIGRpZmZlcmVudCBjYWxsIHNpZ25hdHVyZXMuXG4vLyBXZSBuZWVkIHRvIGRlZmluZSB0aGVzZSB3aXRoIGZ1bmN0aW9uIG92ZXJsb2Fkcywgb3RoZXJ3aXNlIFR5cGVTY3JpcHRcbi8vIG1lc3NlcyB1cCB0aGUgcmV0dXJuIHR5cGUuXG5cbi8vIENhbGwgc2lnbmF0dXJlIDE6IGBnZXRUcmFuc2xhdGlvbnMobmFtZXNwYWNlKWBcblxuLy8gQ2FsbCBzaWduYXR1cmUgMjogYGdldFRyYW5zbGF0aW9ucyh7bG9jYWxlLCBuYW1lc3BhY2V9KWBcblxuLy8gSW1wbGVtZW50YXRpb25cbmFzeW5jIGZ1bmN0aW9uIGdldFRyYW5zbGF0aW9ucyhuYW1lc3BhY2VPck9wdHMpIHtcbiAgbGV0IG5hbWVzcGFjZTtcbiAgbGV0IGxvY2FsZTtcbiAgaWYgKHR5cGVvZiBuYW1lc3BhY2VPck9wdHMgPT09ICdzdHJpbmcnKSB7XG4gICAgbmFtZXNwYWNlID0gbmFtZXNwYWNlT3JPcHRzO1xuICB9IGVsc2UgaWYgKG5hbWVzcGFjZU9yT3B0cykge1xuICAgIGxvY2FsZSA9IG5hbWVzcGFjZU9yT3B0cy5sb2NhbGU7XG4gICAgbmFtZXNwYWNlID0gbmFtZXNwYWNlT3JPcHRzLm5hbWVzcGFjZTtcbiAgfVxuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcobG9jYWxlKTtcbiAgcmV0dXJuIGdldFNlcnZlclRyYW5zbGF0b3IoY29uZmlnLCBuYW1lc3BhY2UpO1xufVxudmFyIGdldFRyYW5zbGF0aW9ucyQxID0gY2FjaGUoZ2V0VHJhbnNsYXRpb25zKTtcblxuZXhwb3J0IHsgZ2V0VHJhbnNsYXRpb25zJDEgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ validateLocale)\n/* harmony export */ });\nfunction validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvdmFsaWRhdGVMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixzREFBc0QsT0FBTztBQUM3RDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcdmFsaWRhdGVMb2NhbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gdmFsaWRhdGVMb2NhbGUobG9jYWxlKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgY29uc3RydWN0ZWQgPSBuZXcgSW50bC5Mb2NhbGUobG9jYWxlKTtcbiAgICBpZiAoIWNvbnN0cnVjdGVkLmxhbmd1YWdlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xhbmd1YWdlIGlzIHJlcXVpcmVkJyk7XG4gICAgfVxuICB9IGNhdGNoIHtcbiAgICBjb25zb2xlLmVycm9yKGBBbiBpbnZhbGlkIGxvY2FsZSB3YXMgcHJvdmlkZWQ6IFwiJHtsb2NhbGV9XCJcXG5QbGVhc2UgZW5zdXJlIHlvdSdyZSB1c2luZyBhIHZhbGlkIFVuaWNvZGUgbG9jYWxlIGlkZW50aWZpZXIgKGUuZy4gXCJlbi1VU1wiKS5gKTtcbiAgfVxufVxuXG5leHBvcnQgeyB2YWxpZGF0ZUxvY2FsZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\portal-main\\\\portal-main\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\development\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\next-intl\\dist\\esm\\development\\shared\\NextIntlClientProvider.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/constants.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ HEADER_LOCALE_NAME)\n/* harmony export */ });\n// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERlZXBJbnNpZ2h0XFxEb3dubG9hZHNcXHBvcnRhbC1tYWluXFxwb3J0YWwtbWFpblxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNoYXJlZFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFVzZWQgdG8gcmVhZCB0aGUgbG9jYWxlIGZyb20gdGhlIG1pZGRsZXdhcmVcbmNvbnN0IEhFQURFUl9MT0NBTEVfTkFNRSA9ICdYLU5FWFQtSU5UTC1MT0NBTEUnO1xuXG5leHBvcnQgeyBIRUFERVJfTE9DQUxFX05BTUUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzaGFyZWRcXHVzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyByZWFjdCBmcm9tICdyZWFjdCc7XG5cbi8vIEB0cy1leHBlY3QtZXJyb3IgLS0gT29vZiwgTmV4dC5qcyBkb2Vzbid0IG1ha2UgdGhpcyBlYXN5LlxuLy8gYHVzZWAgaXMgb25seSBhdmFpbGFibGUgaW4gUmVhY3QgMTkgY2FuYXJ5LCBidXQgd2UgY2FuXG4vLyB1c2UgaXQgaW4gTmV4dC5qcyBhbHJlYWR5IGFzIE5leHQuanMgXCJ2ZW5kb3JzXCIgYSBmaXhlZFxuLy8gdmVyc2lvbiBvZiBSZWFjdC4gSG93ZXZlciwgaWYgd2UnZCBzaW1wbHkgcHV0IGB1c2VgIGluXG4vLyBFU00gY29kZSwgdGhlbiB0aGUgYnVpbGQgZG9lc24ndCB3b3JrIHNpbmNlIFJlYWN0IGRvZXNcbi8vIG5vdCBleHBvcnQgYHVzZWAgb2ZmaWNpYWxseS4gVGhlcmVmb3JlLCB3ZSBoYXZlIHRvIHVzZVxuLy8gc29tZXRoaW5nIHRoYXQgaXMgbm90IHN0YXRpY2FsbHkgYW5hbHl6YWJsZS4gT25jZSBSZWFjdFxuLy8gMTkgaXMgb3V0LCB3ZSBjYW4gcmVtb3ZlIHRoaXMgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi5cbnZhciB1c2UgPSByZWFjdFsndXNlJy50cmltKCldO1xuXG5leHBvcnQgeyB1c2UgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createNavigation)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _shared_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var _useBasePathname_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js\");\n\n\n\n\n\n\n\n\nfunction createNavigation(routing) {\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname$1() {\n    const pathname = (0,_useBasePathname_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(config);\n    const locale = (0,use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.getRoute)(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter$1() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const curLocale = (0,use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const nextPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          (0,_shared_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname: usePathname$1,\n    useRouter: useRouter$1,\n    getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useBasePathname)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n\n\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n  const locale = (0,use_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.getLocalePrefix)(locale, config.localePrefix);\n    const isPathnamePrefixed = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.hasPathnamePrefixed)(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.unprefixPathname)(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.getLocaleAsPrefix)(locale);\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.hasPathnamePrefixed)(localeAsPrefix, pathname)) {\n        unlocalizedPathname = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.unprefixPathname)(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseLink$1)\n/* harmony export */ });\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction BaseLink({ href, locale, localeCookie, onClick, prefetch, ...rest }, ref) {\n    const curLocale = (0,use_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    function onLinkClick(event) {\n        (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== 'production') {\n            console.error('The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`');\n        }\n        prefetch = false;\n    }\n    // Somehow the types for `next/link` don't work as expected\n    // when `moduleResolution: \"nodenext\"` is used.\n    const Link = next_link__WEBPACK_IMPORTED_MODULE_0__;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Link, {\n        ref: ref,\n        href: href,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch,\n        ...rest\n    });\n}\nvar BaseLink$1 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(BaseLink);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createSharedNavigationFns)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/use.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_3__.receiveRoutingConfig)(routing || {});\n  {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.validateReceivedConfig)(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isLocalizableHref)(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(localePromiseOrValue) ? (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || undefined) : pathname;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_BaseLink_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.serializeSearchParams)(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compileLocalizedPathname)({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.normalizeNameOrNameWithParams)(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.applyPathnamePrefix)(pathname, locale, config, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect);\n  const permanentRedirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ syncLocaleCookie)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = `${name}=${nextLocale};`;\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += `${targetKey}`;\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ applyPathnamePrefix),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ compileLocalizedPathname),\n/* harmony export */   getBasePath: () => (/* binding */ getBasePath),\n/* harmony export */   getRoute: () => (/* binding */ getRoute),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ normalizeNameOrNameWithParams),\n/* harmony export */   serializeSearchParams: () => (/* binding */ serializeSearchParams),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ validateReceivedConfig)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-client/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlError),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlErrorCode),\n/* harmony export */   IntlProvider: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlProvider),\n/* harmony export */   _createCache: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createCache),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createIntlFormatters),\n/* harmony export */   createFormatter: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createFormatter),\n/* harmony export */   createTranslator: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createTranslator),\n/* harmony export */   hasLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.initializeConfig),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useLocale),\n/* harmony export */   useMessages: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useMessages),\n/* harmony export */   useNow: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useNow),\n/* harmony export */   useTimeZone: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/index.js\");\n\n\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore React attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', use_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations);\nconst useFormatter = callHook('useFormatter', use_intl__WEBPACK_IMPORTED_MODULE_1__.useFormatter);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/config.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ receiveRoutingConfig)\n/* harmony export */ });\nfunction receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxyb3V0aW5nXFxjb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVjZWl2ZVJvdXRpbmdDb25maWcoaW5wdXQpIHtcbiAgcmV0dXJuIHtcbiAgICAuLi5pbnB1dCxcbiAgICBsb2NhbGVQcmVmaXg6IHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcoaW5wdXQubG9jYWxlUHJlZml4KSxcbiAgICBsb2NhbGVDb29raWU6IHJlY2VpdmVMb2NhbGVDb29raWUoaW5wdXQubG9jYWxlQ29va2llKSxcbiAgICBsb2NhbGVEZXRlY3Rpb246IGlucHV0LmxvY2FsZURldGVjdGlvbiA/PyB0cnVlLFxuICAgIGFsdGVybmF0ZUxpbmtzOiBpbnB1dC5hbHRlcm5hdGVMaW5rcyA/PyB0cnVlXG4gIH07XG59XG5mdW5jdGlvbiByZWNlaXZlTG9jYWxlQ29va2llKGxvY2FsZUNvb2tpZSkge1xuICByZXR1cm4gbG9jYWxlQ29va2llID8/IHRydWUgPyB7XG4gICAgbmFtZTogJ05FWFRfTE9DQUxFJyxcbiAgICBzYW1lU2l0ZTogJ2xheCcsXG4gICAgLi4uKHR5cGVvZiBsb2NhbGVDb29raWUgPT09ICdvYmplY3QnICYmIGxvY2FsZUNvb2tpZSlcblxuICAgIC8vIGBwYXRoYCBuZWVkcyB0byBiZSBwcm92aWRlZCBiYXNlZCBvbiBhIGRldGVjdGVkIGJhc2UgcGF0aFxuICAgIC8vIHRoYXQgZGVwZW5kcyBvbiB0aGUgZW52aXJvbm1lbnQgd2hlbiBzZXR0aW5nIGEgY29va2llXG4gIH0gOiBmYWxzZTtcbn1cbmZ1bmN0aW9uIHJlY2VpdmVMb2NhbGVQcmVmaXhDb25maWcobG9jYWxlUHJlZml4KSB7XG4gIHJldHVybiB0eXBlb2YgbG9jYWxlUHJlZml4ID09PSAnb2JqZWN0JyA/IGxvY2FsZVByZWZpeCA6IHtcbiAgICBtb2RlOiBsb2NhbGVQcmVmaXggfHwgJ2Fsd2F5cydcbiAgfTtcbn1cblxuZXhwb3J0IHsgcmVjZWl2ZVJvdXRpbmdDb25maWcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/defineRouting.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defineRouting)\n/* harmony export */ });\nfunction defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxS0FBcUssT0FBTyxnQkFBZ0IscUNBQXFDO0FBQ2pPO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxyb3V0aW5nXFxkZWZpbmVSb3V0aW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGRlZmluZVJvdXRpbmcoY29uZmlnKSB7XG4gIGlmIChjb25maWcuZG9tYWlucykge1xuICAgIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihjb25maWcuZG9tYWlucyk7XG4gIH1cbiAgcmV0dXJuIGNvbmZpZztcbn1cbmZ1bmN0aW9uIHZhbGlkYXRlVW5pcXVlTG9jYWxlc1BlckRvbWFpbihkb21haW5zKSB7XG4gIGNvbnN0IGRvbWFpbnNCeUxvY2FsZSA9IG5ldyBNYXAoKTtcbiAgZm9yIChjb25zdCB7XG4gICAgZG9tYWluLFxuICAgIGxvY2FsZXNcbiAgfSBvZiBkb21haW5zKSB7XG4gICAgZm9yIChjb25zdCBsb2NhbGUgb2YgbG9jYWxlcykge1xuICAgICAgY29uc3QgbG9jYWxlRG9tYWlucyA9IGRvbWFpbnNCeUxvY2FsZS5nZXQobG9jYWxlKSB8fCBuZXcgU2V0KCk7XG4gICAgICBsb2NhbGVEb21haW5zLmFkZChkb21haW4pO1xuICAgICAgZG9tYWluc0J5TG9jYWxlLnNldChsb2NhbGUsIGxvY2FsZURvbWFpbnMpO1xuICAgIH1cbiAgfVxuICBjb25zdCBkdXBsaWNhdGVMb2NhbGVNZXNzYWdlcyA9IEFycmF5LmZyb20oZG9tYWluc0J5TG9jYWxlLmVudHJpZXMoKSkuZmlsdGVyKChbLCBsb2NhbGVEb21haW5zXSkgPT4gbG9jYWxlRG9tYWlucy5zaXplID4gMSkubWFwKChbbG9jYWxlLCBsb2NhbGVEb21haW5zXSkgPT4gYC0gXCIke2xvY2FsZX1cIiBpcyB1c2VkIGJ5OiAke0FycmF5LmZyb20obG9jYWxlRG9tYWlucykuam9pbignLCAnKX1gKTtcbiAgaWYgKGR1cGxpY2F0ZUxvY2FsZU1lc3NhZ2VzLmxlbmd0aCA+IDApIHtcbiAgICBjb25zb2xlLndhcm4oJ0xvY2FsZXMgYXJlIGV4cGVjdGVkIHRvIGJlIHVuaXF1ZSBwZXIgZG9tYWluLCBidXQgZm91bmQgb3ZlcmxhcDpcXG4nICsgZHVwbGljYXRlTG9jYWxlTWVzc2FnZXMuam9pbignXFxuJykgKyAnXFxuUGxlYXNlIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9yb3V0aW5nI2RvbWFpbnMnKTtcbiAgfVxufVxuXG5leHBvcnQgeyBkZWZpbmVSb3V0aW5nIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider({ locale, ...rest }) {\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs2REFDOEM7QUFDTjtBQUV4QyxTQUFTRSx1QkFBdUIsRUFDOUJDLE1BQU0sRUFDTixHQUFHQyxNQUNKO0lBQ0MsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtBQUU2QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzaGFyZWRcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgeyBJbnRsUHJvdmlkZXIgfSBmcm9tICd1c2UtaW50bC9yZWFjdCc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdyZWFjdC9qc3gtcnVudGltZSc7XG5cbmZ1bmN0aW9uIE5leHRJbnRsQ2xpZW50UHJvdmlkZXIoe1xuICBsb2NhbGUsXG4gIC4uLnJlc3Rcbn0pIHtcbiAgaWYgKCFsb2NhbGUpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBpbmZlciB0aGUgYGxvY2FsZWAgcHJvcCBpbiBgTmV4dEludGxDbGllbnRQcm92aWRlcmAsIHBsZWFzZSBwcm92aWRlIGl0IGV4cGxpY2l0bHkuXFxuXFxuU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbG9jYWxlXCIgKTtcbiAgfVxuICByZXR1cm4gLyojX19QVVJFX18qL2pzeChJbnRsUHJvdmlkZXIsIHtcbiAgICBsb2NhbGU6IGxvY2FsZSxcbiAgICAuLi5yZXN0XG4gIH0pO1xufVxuXG5leHBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6WyJJbnRsUHJvdmlkZXIiLCJqc3giLCJOZXh0SW50bENsaWVudFByb3ZpZGVyIiwibG9jYWxlIiwicmVzdCIsIkVycm9yIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzaGFyZWRcXHVzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyByZWFjdCBmcm9tICdyZWFjdCc7XG5cbi8vIEB0cy1leHBlY3QtZXJyb3IgLS0gT29vZiwgTmV4dC5qcyBkb2Vzbid0IG1ha2UgdGhpcyBlYXN5LlxuLy8gYHVzZWAgaXMgb25seSBhdmFpbGFibGUgaW4gUmVhY3QgMTkgY2FuYXJ5LCBidXQgd2UgY2FuXG4vLyB1c2UgaXQgaW4gTmV4dC5qcyBhbHJlYWR5IGFzIE5leHQuanMgXCJ2ZW5kb3JzXCIgYSBmaXhlZFxuLy8gdmVyc2lvbiBvZiBSZWFjdC4gSG93ZXZlciwgaWYgd2UnZCBzaW1wbHkgcHV0IGB1c2VgIGluXG4vLyBFU00gY29kZSwgdGhlbiB0aGUgYnVpbGQgZG9lc24ndCB3b3JrIHNpbmNlIFJlYWN0IGRvZXNcbi8vIG5vdCBleHBvcnQgYHVzZWAgb2ZmaWNpYWxseS4gVGhlcmVmb3JlLCB3ZSBoYXZlIHRvIHVzZVxuLy8gc29tZXRoaW5nIHRoYXQgaXMgbm90IHN0YXRpY2FsbHkgYW5hbHl6YWJsZS4gT25jZSBSZWFjdFxuLy8gMTkgaXMgb3V0LCB3ZSBjYW4gcmVtb3ZlIHRoaXMgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbi5cbnZhciB1c2UgPSByZWFjdFsndXNlJy50cmltKCldO1xuXG5leHBvcnQgeyB1c2UgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ })

};
;