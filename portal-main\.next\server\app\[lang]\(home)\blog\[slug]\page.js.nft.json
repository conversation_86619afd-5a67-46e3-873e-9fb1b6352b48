{"version": 1, "files": ["../../../../../webpack-runtime.js", "../../../../../chunks/447.js", "../../../../../chunks/825.js", "../../../../../chunks/189.js", "../../../../../chunks/453.js", "../../../../../chunks/155.js", "../../../../../chunks/569.js", "../../../../../chunks/510.js", "../../../../../chunks/96.js", "../../../../../chunks/897.js", "../../../../../chunks/958.js", "../../../../../chunks/236.js", "../../../../../chunks/684.js", "../../../../../chunks/296.js", "../../../../../chunks/730.js", "../../../../../chunks/851.js", "page_client-reference-manifest.js", "../../../../../../../package.json", "../../../../../../../content/docs/key_concepts.cn.md", "../../../../../../../content/docs/index.mdx", "../../../../../../../content/docs/index.cn.md", "../../../../../../../content/docs/key_concepts.md", "../../../../../../../content/docs/goal.md", "../../../../../../../content/docs/goal.cn.md", "../../../../../../../content/docs/application_scenarios.cn.md", "../../../../../../../content/docs/application_scenarios.md", "../../../../../../../content/blog/tman-designer-of-ten-framework.mdx", "../../../../../../../content/blog/ten-world-first-truly-real-time-multimodal-agent-framework.mdx", "../../../../../../../content/blog/deepgram-deepseek-fish-build-your-own-voice-agent.mdx", "../../../../../../../content/blog/exploring-the-ai-sales-avatar.mdx", "../../../../../../../content/docs/ten_framework/version_system.md", "../../../../../../../content/docs/ten_framework/version_system.cn.md", "../../../../../../../content/docs/ten_framework/profiling.md", "../../../../../../../content/docs/ten_framework/profiling.cn.md", "../../../../../../../content/docs/ten_framework/preparation.md", "../../../../../../../content/docs/ten_framework/preparation.cn.md", "../../../../../../../content/docs/ten_framework/message_system.cn.md", "../../../../../../../content/docs/ten_framework/message_system.md", "../../../../../../../content/docs/ten_framework/dependencies.cn.md", "../../../../../../../content/docs/ten_framework/dependencies.md", "../../../../../../../content/docs/ten_framework/concept_overview.cn.md", "../../../../../../../content/docs/ten_framework/concept_overview.md", "../../../../../../../content/docs/ten_framework/cloud_store.md", "../../../../../../../content/docs/ten_framework/cloud_store.cn.md", "../../../../../../../content/docs/ten_framework/build_system.cn.md", "../../../../../../../content/docs/ten_framework/build_system.md", "../../../../../../../content/docs/ten_agent/overview.md", "../../../../../../../content/docs/ten_agent/overview.cn.md", "../../../../../../../content/docs/ten_agent/getting_started.mdx", "../../../../../../../content/docs/ten_agent/getting_started.cn.mdx", "../../../../../../../content/docs/ten_agent/how_does_interrupt_work.md", "../../../../../../../content/docs/ten_agent/how_does_interrupt_work.cn.md", "../../../../../../../content/docs/ten_agent/faqs.cn.md", "../../../../../../../content/docs/ten_agent/customize_your_agent.md", "../../../../../../../content/docs/ten_agent/customize_your_agent.cn.md", "../../../../../../../content/docs/ten_agent/faqs.md", "../../../../../../../content/docs/ten_agent/create_a_hello_world_extension.mdx", "../../../../../../../content/docs/ten_agent/create_a_hello_world_extension.cn.mdx", "../../../../../../../content/docs/ten_agent/architecture_flow.md", "../../../../../../../content/docs/ten_agent/architecture_flow.cn.md", "../../../../../../../content/docs/ten_framework/tman_designer/overview.md", "../../../../../../../content/docs/ten_framework/tman_designer/overview.cn.md", "../../../../../../../content/docs/ten_framework/tman_designer/doc_window.md", "../../../../../../../content/docs/ten_framework/tman_designer/doc_window.cn.md", "../../../../../../../content/docs/ten_framework/standard_extension/standard_interface.md", "../../../../../../../content/docs/ten_framework/standard_extension/standard_interface.cn.md", "../../../../../../../content/docs/ten_framework/software_packages/python_async_extension.md", "../../../../../../../content/docs/ten_framework/software_packages/python_async_extension.cn.md", "../../../../../../../content/docs/ten_framework/software_packages/overview.cn.md", "../../../../../../../content/docs/ten_framework/software_packages/overview.md", "../../../../../../../content/docs/ten_framework/software_packages/extension.md", "../../../../../../../content/docs/ten_framework/software_packages/app.md", "../../../../../../../content/docs/ten_framework/software_packages/extension.cn.md", "../../../../../../../content/docs/ten_framework/software_packages/app.cn.md", "../../../../../../../content/docs/ten_framework/software_packages/addon_system.md", "../../../../../../../content/docs/ten_framework/software_packages/addon_system.cn.md", "../../../../../../../content/docs/ten_framework/ten_manager/overview.mdx", "../../../../../../../content/docs/ten_framework/ten_manager/overview.cn.mdx", "../../../../../../../content/docs/ten_framework/ten_manager/check_graph.md", "../../../../../../../content/docs/ten_framework/ten_manager/check_graph.cn.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/for_users_of_ten_framework.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/how_to_debug_with_logs.cn.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/how_to_debug_with_logs.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/for_users_of_ten_framework.cn.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/for_developers_of_ten_framework.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/for_developers_of_ten_framework.cn.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/debugging.cn.md", "../../../../../../../content/docs/ten_framework/testing_and_debugging/debugging.md", "../../../../../../../content/docs/ten_framework/metadata_system/property.md", "../../../../../../../content/docs/ten_framework/metadata_system/property.cn.md", "../../../../../../../content/docs/ten_framework/metadata_system/metadata_system.md", "../../../../../../../content/docs/ten_framework/metadata_system/manifest.md", "../../../../../../../content/docs/ten_framework/metadata_system/metadata_system.cn.md", "../../../../../../../content/docs/ten_framework/metadata_system/manifest.cn.md", "../../../../../../../content/docs/ten_framework/language_binding/go.md", "../../../../../../../content/docs/ten_framework/language_binding/go.cn.md", "../../../../../../../content/docs/ten_framework/development/how_to_develop_with_ext.md", "../../../../../../../content/docs/ten_framework/development/how_to_develop_with_ext.cn.md", "../../../../../../../content/docs/ten_framework/development/development_process.cn.md", "../../../../../../../content/docs/ten_framework/development/development_process.md", "../../../../../../../content/docs/ten_framework/graph/msg_conversion.md", "../../../../../../../content/docs/ten_framework/graph/msg_conversion.cn.md", "../../../../../../../content/docs/ten_framework/graph/graph.md", "../../../../../../../content/docs/ten_framework/graph/graph.cn.md", "../../../../../../../content/docs/ten_framework/api/type_system.md", "../../../../../../../content/docs/ten_framework/api/type_system.cn.md", "../../../../../../../content/docs/ten_framework/api/required.md", "../../../../../../../content/docs/ten_framework/api/required.cn.md", "../../../../../../../content/docs/ten_framework/api/log.md", "../../../../../../../content/docs/ten_framework/api/log.cn.md", "../../../../../../../content/docs/ten_framework/api/api.md", "../../../../../../../content/docs/ten_framework/api/api.cn.md", "../../../../../../../content/docs/ten_framework/ai_model/how_to_run_local_model_in_python_extensions.cn.md", "../../../../../../../content/docs/ten_framework/ai_model/how_to_run_local_model_in_python_extensions.md", "../../../../../../../content/docs/ten_agent/setup_development_env/setting_up_vscode_for_development_inside_container.md", "../../../../../../../content/docs/ten_agent/setup_development_env/setting_up_development_inside_codespace.md", "../../../../../../../content/docs/ten_agent/setup_development_env/setting_up_vscode_for_development_inside_container.cn.md", "../../../../../../../content/docs/ten_agent/setup_development_env/setting_up_development_inside_codespace.cn.md", "../../../../../../../content/docs/ten_agent/setup_development_env/index.md", "../../../../../../../content/docs/ten_agent/setup_development_env/index.cn.md", "../../../../../../../content/docs/ten_agent/tutorials/how_to_run_local_model_in_ollama.md", "../../../../../../../content/docs/ten_agent/project_structure/server.cn.md", "../../../../../../../content/docs/ten_agent/project_structure/server.md", "../../../../../../../content/docs/ten_agent/project_structure/property_json.md", "../../../../../../../content/docs/ten_agent/project_structure/property_json.cn.md", "../../../../../../../content/docs/ten_agent/project_structure/index.md", "../../../../../../../content/docs/ten_agent/project_structure/index.cn.md", "../../../../../../../content/docs/ten_agent/project_structure/extension.md", "../../../../../../../content/docs/ten_agent/project_structure/extension.cn.md", "../../../../../../../content/docs/ten_agent/deploy_ten_agent/deploy_agent_service.md", "../../../../../../../content/docs/ten_agent/demo/index.md", "../../../../../../../content/docs/ten_agent/demo/index.cn.md", "../../../../../../../content/docs/ten_agent/playground/index.md", "../../../../../../../content/docs/ten_agent/playground/index.cn.md", "../../../../../../../content/docs/ten_agent/playground/configure_properties.md", "../../../../../../../content/docs/ten_agent/playground/configure_properties.cn.md", "../../../../../../../content/docs/ten_agent/playground/configure_modules.md", "../../../../../../../content/docs/ten_agent/playground/configure_modules.cn.md", "../../../../../../../content/docs/ten_agent/playground/change_language.md", "../../../../../../../content/docs/ten_agent/playground/change_language.cn.md", "../../../../../../../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_Tool_extension.cn.md", "../../../../../../../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_Tool_extension.md", "../../../../../../../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_extension.md", "../../../../../../../content/docs/ten_agent/create_an_extension_with_predefined_type/LLM_extension.cn.md", "../../../../../../../content/docs/ten_agent/create_an_extension_with_predefined_type/index.cn.md", "../../../../../../../content/docs/ten_agent/create_an_extension_with_predefined_type/index.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/run_story_telling.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/run_story_telling.cn.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/voice-assistant/run_dify.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/voice-assistant/run_coze.cn.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/voice-assistant/run_coze.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/voice-assistant/run_dify.cn.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/voice-assistant/index.cn.md", "../../../../../../../content/docs/ten_agent/playground/use-cases/voice-assistant/index.md"]}