"use strict";exports.id=730,exports.ids=[730],exports.modules={25862:(e,t,r)=>{r.d(t,{B:()=>function e(t,r=!1){for(let o of Object.keys(t))void 0===t[o]&&delete t[o],r&&"object"==typeof t[o]&&null!==t[o]?e(t[o],r):r&&Array.isArray(t[o])&&t[o].forEach(t=>e(t,r));return t}})},34730:(e,t,r)=>{r.d(t,{searchDocs:()=>i});var o=r(25862);async function i(e,t,r){let i=[],{index:l="default",client:n,params:s={}}=r;if("crawler"===l){let r=await n.search({...s,term:e,where:{category:t?{eq:t.slice(0,1).toUpperCase()+t.slice(1)}:void 0,...s.where},limit:10});if(!r)return i;if("crawler"===l){for(let e of r.hits){let t=e.document;i.push({id:e.id,type:"page",content:t.title,url:t.path},{id:"page"+e.id,type:"text",content:t.content,url:t.path})}return i}}let u={...s,term:e,where:(0,o.B)({tag:t,...s.where}),groupBy:{properties:["page_id"],maxResult:7,...s.groupBy}},a=await n.search(u);if(!a||!a.groups)return i;for(let e of a.groups){let t=!1;for(let r of e.result){let e=r.document;t||(i.push({id:e.page_id,type:"page",content:e.title,url:e.url}),t=!0),i.push({id:e.id,content:e.content,type:e.content===e.section?"heading":"text",url:e.section_id?`${e.url}#${e.section_id}`:e.url})}}return i}r(22317)}};