exports.id=958,exports.ids=[958],exports.modules={963:(e,t,r)=>{"use strict";r.d(t,{k:()=>m});var a=r(37413),s=r(78655);let n=({height:e=32,width:t})=>(0,a.jsxs)("svg",{id:"ten-logo","data-name":"ten-logo",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 536.03 168.7",style:{height:`${e}px`,width:t?`${t}px`:"auto"},className:"w-full h-full",children:[(0,a.jsx)("defs",{children:(0,a.jsx)("style",{children:`
          @import url('https://fonts.googleapis.com/css2?family=Audiowide&display=swap');
          .cls-1 {
            font-family: 'Audiowide', cursive;
            font-size: 144px;
            fill: currentColor;
            dominant-baseline: middle;
            text-anchor: start;
          }
        `})}),(0,a.jsxs)("g",{id:"logo-container","data-name":"logo-container",children:[(0,a.jsx)("g",{id:"logo-shape",children:(0,a.jsx)("polygon",{points:"153.1 3.22 153.3 3.22 150.5 .72 147.3 .72 147.3 .72 34.2 .52 33.4 2.82 0 99.72 28 130.42 63.5 130.32 98.9 27.42 61.5 27.22 36.6 2.82 149.9 3.22 177.5 29.82 133.8 28.22 98.6 130.32 146.2 129.82 180.6 29.92 153.1 3.22",fill:"currentColor"})}),(0,a.jsx)("text",{className:"cls-1",x:"200",y:"85",children:"TEN"})]})]});var o=r(40851),i=r(63488),l=r(87368),c=r(38078);let d=e=>"cn"===e?c:l;function m(e){let t=d(e);return{i18n:i.R,nav:{title:(0,a.jsx)("div",{className:"flex items-center gap-2 mt-0.5",children:(0,a.jsx)(n,{height:33,width:66})}),url:`/${e}`},links:[{text:t.nav.docs,url:`/${e}/docs`,active:"nested-url"},{text:t.nav.blog,url:`/${e}/blog`,active:"nested-url"},{text:(0,a.jsx)(o.ClientSearch,{placeholder:t.fuma.search}),url:"#",active:"none"},{text:(0,a.jsx)(s.GitHubStarButton,{repo:"TEN-framework/ten-framework"}),url:"https://github.com/TEN-framework/ten-framework",active:"none"}],themeSwitch:{enabled:!0,mode:"light-dark-system"}}}},2635:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w,generateMetadata:()=>y,generateStaticParams:()=>v});var a=r(37413),s=r(26736),n=r(93889),o=r.n(n),i=r(14967),l=r(88946),c=r(83930),d=r(39916),m=r(63488),u=r(38078),h=r(87368),g=r(94549),x=r(36162);r(42447);let p=u.fuma,b=h.fuma,f=[{name:"English",locale:"en"},{name:"中文",locale:"cn"}];async function y({params:e}){let{lang:t}=await e;return(0,g.gr)({lang:t})}async function v(){return m.P.locales.map(e=>({lang:e}))}async function w({params:e,children:t}){let{lang:r}=await e,n=await (0,c.A)({locale:r});return(0,i.EL)(m.P.locales,r)||(0,d.notFound)(),(0,a.jsxs)("html",{lang:r,className:o().className,suppressHydrationWarning:!0,children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,a.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Audiowide&display=swap",rel:"stylesheet"})]}),(0,a.jsx)("body",{className:"flex min-h-screen flex-col",children:(0,a.jsx)(l.A,{locale:r,messages:n,children:(0,a.jsxs)(s.RootProvider,{i18n:{locale:r,locales:f,translations:{cn:p,en:b}[r]},children:[t,(0,a.jsx)(x.default,{src:"/analytics/scarf-analytics.js",strategy:"afterInteractive"})]})})})]})}},13453:(e,t,r)=>{"use strict";r.d(t,{ClientSearch:()=>m});var a=r(60687),s=r(43210),n=r(90150);r(46040);var o=r(82080),i=r(10022),l=r(99270),c=r(11860),d=r(8610);function m({placeholder:e="Search...",className:t=""}){let r=(0,d.Ym)(),[m,u]=(0,s.useState)(!1),[h,g]=(0,s.useState)(""),[x,p]=(0,s.useState)([]),[b,f]=(0,s.useState)(!1),[y,v]=(0,s.useState)(null),[w,N]=(0,s.useState)(!1);(0,s.useCallback)(async e=>{if(!y||!e.trim())return void p([]);f(!0);try{let t=(await (0,n.$P)(y,{term:e,limit:10,threshold:.6,boost:{title:2,description:1.5,content:1}})).hits.map(e=>({id:e.id,url:e.document.url,title:e.document.title,description:e.document.description,content:e.document.content,type:e.document.type,score:e.score}));p(t)}catch(e){console.error("搜索失败:",e),p([])}finally{f(!1)}},[y]);let j=e=>{u(!1),g(""),window.location.href=`/${r}${e.url}`},k=e=>"blog"===e?(0,a.jsx)(o.A,{className:"h-4 w-4"}):(0,a.jsx)(i.A,{className:"h-4 w-4"}),A=e=>"blog"===e?"cn"===r?"博客":"Blog":"cn"===r?"文档":"Docs";return w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>u(!0),className:`flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 transition-colors ${t}`,children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e}),(0,a.jsxs)("kbd",{className:"ml-auto hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 px-1.5 font-mono text-xs text-gray-600 dark:text-gray-300",children:[(0,a.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:()=>u(!1),children:(0,a.jsx)("div",{className:"fixed left-1/2 top-1/2 w-full max-w-2xl -translate-x-1/2 -translate-y-1/2 transform",children:(0,a.jsxs)("div",{className:"mx-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center border-b border-gray-200 dark:border-gray-700 px-4",children:[(0,a.jsx)(l.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",value:h,onChange:e=>g(e.target.value),placeholder:e,className:"flex-1 bg-transparent px-4 py-4 text-sm outline-none placeholder:text-gray-400",autoFocus:!0}),(0,a.jsx)("button",{onClick:()=>u(!1),className:"rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:b?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"搜索中..."})}):x.length>0?(0,a.jsx)("div",{className:"py-2",children:x.map(e=>(0,a.jsxs)("button",{onClick:()=>j(e),className:"flex w-full items-start gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50",children:[(0,a.jsx)("div",{className:"mt-1 flex-shrink-0 text-gray-400",children:k(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title}),(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded",children:A(e.type)})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:e.description})]})]},e.id))}):h?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"cn"===r?"未找到结果":"No results found"})}):(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"cn"===r?"输入关键词开始搜索":"Type to search"})})})]})})})]}):(0,a.jsxs)("button",{className:`flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 ${t}`,disabled:!0,children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Loading search..."})]})}},32859:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},36724:(e,t,r)=>{Promise.resolve().then(r.bind(r,26736)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.t.bind(r,47429,23))},38078:e=>{"use strict";e.exports=JSON.parse('{"nav":{"docs":"文档","blog":"博客"},"fuma":{"search":"搜索","searchNoResult":"未找到结果","toc":"目录","tocNoHeadings":"没有标题","lastUpdate":"最后更新","chooseLanguage":"选择语言","nextPage":"下一页","previousPage":"上一页","chooseTheme":"选择主题","editOnGithub":"在 GitHub 上编辑"},"blog":{"latestPosts":"最新博客文章","discoverLatestArticles":"发现我们关于 TEN framework、AI 开发等方面的最新文章","readMore":"阅读更多","backToBlog":"返回博客","writtenBy":"作者","publishedOn":"发布于"},"homePage":{"titlePrefix":"搭建","titleRealtime":"实时","titleMultimodal":"可定制","titleLowlantency":"低延迟","titleHighperformance":"高性能","titleEdgeCloud":"可边缘云","titleSuffix":"语音 AI Agent","readLaunchArticle":"阅读我们的博客了解更多","heroDescription":"TEN 是一个用于搭建实时多模态的对话式 AI 引擎的开源框架","heroBtnTryTenAgent":"体验 TEN Agent","heroBtnReadDoc":"文档","bannerAnnouncement":"欢迎 VAD 和 Turn Detection 加入 TEN 开源全家桶!","huggingFaceSpace":"体验语音检测和打断","supportedBy":"共同支持来自 TEN 社区"}}')},40851:(e,t,r)=>{"use strict";r.d(t,{ClientSearch:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ClientSearch() from the server but ClientSearch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\client-search.tsx","ClientSearch")},42447:()=>{},67203:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},68444:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(35471),s=r(14967),n=r(63488);let o=(0,a.A)(async({requestLocale:e})=>{let t=await e,a=(0,s.EL)(n.P.locales,t)?t:n.P.defaultLocale;return{locale:a,messages:(await r(76565)(`./${a}.json`)).default}})},76565:(e,t,r)=>{var a={"./cn.json":38078,"./en.json":87368};function s(e){return Promise.resolve().then(()=>{if(!r.o(a,e)){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}var s=a[e];return r.t(s,19)})}s.keys=()=>Object.keys(a),s.id=76565,e.exports=s},76773:(e,t,r)=>{"use strict";r.d(t,{GitHubStarButton:()=>i});var a=r(60687),s=r(43210),n=r(56085),o=r(64398);function i({repo:e,className:t=""}){let[r,i]=(0,s.useState)(null),[l,c]=(0,s.useState)(!1),d=function(e,t=8e3){let[r,a]=(0,s.useState)(0);return(0,s.useRef)(0),(0,s.useRef)(null),r}(0);return(0,a.jsxs)("div",{className:"relative",children:[!l&&(0,a.jsxs)("div",{className:"absolute -inset-2 pointer-events-none",children:[(0,a.jsx)(n.A,{className:"absolute top-0 left-0 h-3 w-3 text-yellow-400 animate-ping"}),(0,a.jsx)(n.A,{className:"absolute top-0 right-0 h-2 w-2 text-blue-400 animate-ping animation-delay-300"}),(0,a.jsx)(n.A,{className:"absolute bottom-0 left-2 h-2 w-2 text-purple-400 animate-ping animation-delay-500"}),(0,a.jsx)(n.A,{className:"absolute bottom-0 right-2 h-3 w-3 text-green-400 animate-ping animation-delay-700"})]}),(0,a.jsxs)("button",{onClick:()=>{window.open(`https://github.com/${e}`,"_blank","noopener,noreferrer")},onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),className:`group relative inline-flex items-center gap-2 rounded-md bg-gradient-to-r from-gray-900 to-black px-3 py-1.5 text-sm font-medium text-white transition-all duration-300 hover:from-gray-800 hover:to-gray-900 hover:scale-105 cursor-pointer ${t}`,children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:"h-4 w-4 relative z-10",fill:"currentColor",children:(0,a.jsx)("path",{d:"M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"})}),(0,a.jsx)("span",{className:"dark:text-gray-300 relative z-10",children:"open source"}),(0,a.jsx)(o.A,{className:`h-4 w-4 relative z-10 transition-all duration-300 ${l?"fill-transparent stroke-yellow-500 scale-110":"fill-transparent stroke-white"}`}),(0,a.jsx)("span",{className:`inline-block w-16 rounded bg-gradient-to-r from-gray-700 to-gray-600 px-2 py-0.5 text-center text-xs font-bold relative z-10 transition-all duration-300 ${l?"from-yellow-600 to-yellow-500 text-white scale-105":""}`,children:d.toLocaleString()}),l&&(0,a.jsx)("div",{className:"absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50",children:"⭐ Star this repo!"})]})]})}},78655:(e,t,r)=>{"use strict";r.d(t,{GitHubStarButton:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call GitHubStarButton() from the server but GitHubStarButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\components\\ui\\github-star-button.tsx","GitHubStarButton")},87368:e=>{"use strict";e.exports=JSON.parse('{"nav":{"docs":"Documentation","blog":"Blog"},"fuma":{"search":"Search","searchNoResult":"No results found","toc":"Table of Contents","tocNoHeadings":"No headings found","lastUpdate":"Last Updated","chooseLanguage":"Choose Language","nextPage":"Next Page","previousPage":"Previous Page","chooseTheme":"Choose Theme","editOnGithub":"Edit on GitHub"},"blog":{"latestPosts":"Latest Blog Posts","discoverLatestArticles":"Discover our latest articles about TEN Framework, TEN Agent and Conversational AI","readMore":"Read more","backToBlog":"Back to Blog","writtenBy":"Written by","publishedOn":"Published on"},"homePage":{"titlePrefix":"Build","titleLowlantency":"Real-Time","titleMultimodal":"Customizable","titleHighperformance":"High-performance","titleEdgeCloud":"Low-Latency","titleSuffix":"Voice AI Agents","readLaunchArticle":"Read more on our blog","heroDescription":"An open-source framework designed for building conversational AI","supportedBy":"supported by the TEN community and","heroBtnTryTenAgent":"Talk to TEN Agent","huggingFaceSpace":"Try VAD and Turn Detection","heroBtnReadDoc":"Documentation","bannerAnnouncement":"are now part of the TEN open-source ecosystem!"}}')},87569:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"2000x2000",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},89460:(e,t,r)=>{Promise.resolve().then(r.bind(r,8495)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.t.bind(r,79167,23))},94549:(e,t,r)=>{"use strict";r.d(t,{W6:()=>a,gr:()=>o});let a="https://theten.ai",s=["AI Framework","Conversational AI","Multimodal AI","Real-time AI","Voice AI","AI Agents"],n={twitter:"@TenFramework"};function o({title:e="Open-source framework for all AI agents.",description:t="TEN is an open-source framework designed for building multimodal conversational AI",lang:r}){return{metadataBase:new URL(a),title:{template:"%s | TEN Framework",default:e},description:t,keywords:s,authors:[{name:"TEN Framework Team"}],openGraph:{title:e,description:t,url:a,siteName:"TEN Framework",locale:r,type:"website"},twitter:{card:"summary_large_image",title:e,description:t,creator:n.twitter},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},alternates:{canonical:`${a}/${r}`,languages:{"en-US":`${a}/en`,"zh-CN":`${a}/cn`}}}}}};