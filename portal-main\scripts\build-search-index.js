#!/usr/bin/env node

/**
 * 构建时生成搜索索引的脚本
 * 为静态导出创建客户端搜索索引
 */

const fs = require('fs')
const path = require('path')
const { create, insertMultiple } = require('@orama/orama')
const { createTokenizer } = require('@orama/tokenizers/mandarin')

// 支持的语言
const LANGUAGES = ['en', 'cn']

// 读取Markdown文件并提取内容
function extractContentFromMarkdown(filePath, content) {
  // 提取frontmatter
  const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/)
  let frontmatter = {}
  let body = content
  
  if (frontmatterMatch) {
    try {
      // 简单解析YAML frontmatter
      const yamlContent = frontmatterMatch[1]
      const lines = yamlContent.split('\n')
      lines.forEach(line => {
        const match = line.match(/^(\w+):\s*(.+)$/)
        if (match) {
          frontmatter[match[1]] = match[2].replace(/^['"]|['"]$/g, '')
        }
      })
    } catch (error) {
      console.warn(`解析frontmatter失败: ${filePath}`, error.message)
    }
    body = content.replace(frontmatterMatch[0], '').trim()
  }
  
  // 清理Markdown语法
  body = body
    .replace(/```[\s\S]*?```/g, '') // 移除代码块
    .replace(/`[^`]+`/g, '') // 移除行内代码
    .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 保留链接文本
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*([^*]+)\*/g, '$1') // 移除斜体标记
    .replace(/^\s*[-*+]\s+/gm, '') // 移除列表标记
    .replace(/^\s*\d+\.\s+/gm, '') // 移除有序列表标记
    .replace(/\n{3,}/g, '\n\n') // 合并多个换行
    .trim()
  
  return {
    title: frontmatter.title || path.basename(filePath, path.extname(filePath)),
    description: frontmatter.description || '',
    content: body,
    ...frontmatter
  }
}

// 递归读取目录中的所有Markdown文件
function readMarkdownFiles(dir, basePath = '') {
  const files = []
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      files.push(...readMarkdownFiles(fullPath, path.join(basePath, item)))
    } else if (item.endsWith('.md') || item.endsWith('.mdx')) {
      const content = fs.readFileSync(fullPath, 'utf-8')
      const extracted = extractContentFromMarkdown(fullPath, content)
      
      // 确定语言
      let language = 'en'
      if (item.endsWith('.cn.md') || item.endsWith('.cn.mdx')) {
        language = 'cn'
      }
      
      // 生成URL路径
      let urlPath = path.join(basePath, item)
        .replace(/\\/g, '/') // 统一使用正斜杠
        .replace(/\.(cn\.)?mdx?$/, '') // 移除文件扩展名
        .replace(/\/index$/, '') // 移除index
      
      if (urlPath && !urlPath.startsWith('/')) {
        urlPath = '/' + urlPath
      }
      
      files.push({
        id: fullPath,
        url: urlPath,
        language,
        title: extracted.title,
        description: extracted.description,
        content: extracted.content,
        type: basePath.includes('blog') ? 'blog' : 'docs'
      })
    }
  }
  
  return files
}

// 创建搜索索引
async function createSearchIndex(documents, language) {
  const schema = {
    id: 'string',
    url: 'string',
    title: 'string',
    description: 'string',
    content: 'string',
    type: 'string'
  }
  
  const config = {
    schema,
    components: {}
  }
  
  // 为中文添加分词器
  if (language === 'cn') {
    config.components.tokenizer = createTokenizer()
  }
  
  const db = create(config)
  
  // 插入文档
  await insertMultiple(db, documents)
  
  return db
}

// 序列化搜索索引
function serializeSearchIndex(db) {
  // 这里我们需要提取Orama数据库的内部结构
  // 注意：这是一个简化的实现，实际可能需要更复杂的序列化
  return {
    schema: db.schema,
    data: db.data,
    index: db.index,
    sorting: db.sorting,
    facets: db.facets
  }
}

async function buildSearchIndex() {
  console.log('🔍 开始构建搜索索引...')
  
  const contentDir = path.join(process.cwd(), 'content')
  const publicDir = path.join(process.cwd(), 'public')
  
  // 确保public目录存在
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true })
  }
  
  // 读取所有文档
  const docsFiles = readMarkdownFiles(path.join(contentDir, 'docs'), 'docs')
  const blogFiles = readMarkdownFiles(path.join(contentDir, 'blog'), 'blog')
  const allFiles = [...docsFiles, ...blogFiles]
  
  console.log(`📄 找到 ${allFiles.length} 个文档`)
  
  // 按语言分组
  const filesByLanguage = {}
  LANGUAGES.forEach(lang => {
    filesByLanguage[lang] = allFiles.filter(file => 
      file.language === lang || (file.language === 'en' && lang === 'en')
    )
  })
  
  // 为每种语言创建搜索索引
  const searchIndexes = {}
  
  for (const language of LANGUAGES) {
    const documents = filesByLanguage[language]
    console.log(`🌐 为 ${language} 语言创建索引 (${documents.length} 个文档)`)
    
    if (documents.length > 0) {
      const db = await createSearchIndex(documents, language)
      searchIndexes[language] = serializeSearchIndex(db)
    }
  }
  
  // 保存搜索索引
  const indexPath = path.join(publicDir, 'search-index.json')
  fs.writeFileSync(indexPath, JSON.stringify(searchIndexes, null, 2))
  
  console.log(`✅ 搜索索引已保存到: ${indexPath}`)
  console.log(`📊 索引统计:`)
  LANGUAGES.forEach(lang => {
    const count = filesByLanguage[lang]?.length || 0
    console.log(`  - ${lang}: ${count} 个文档`)
  })
  
  return searchIndexes
}

// 如果直接运行此脚本
if (require.main === module) {
  buildSearchIndex()
    .then(() => {
      console.log('🎉 搜索索引构建完成!')
      process.exit(0)
    })
    .catch(error => {
      console.error('💥 构建失败:', error)
      process.exit(1)
    })
}

module.exports = { buildSearchIndex }
