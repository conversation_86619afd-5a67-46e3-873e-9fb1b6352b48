"use strict";exports.id=851,exports.ids=[851],exports.modules={17851:(e,t,r)=>{r.d(t,{createStaticClient:()=>tg});var n=r(25862);let o={arabic:"ar",armenian:"am",bulgarian:"bg",czech:"cz",danish:"dk",dutch:"nl",english:"en",finnish:"fi",french:"fr",german:"de",greek:"gr",hungarian:"hu",indian:"in",indonesian:"id",irish:"ie",italian:"it",lithuanian:"lt",nepali:"np",norwegian:"no",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"rs",slovenian:"ru",spanish:"es",swedish:"se",tamil:"ta",turkish:"tr",ukrainian:"uk",sanskrit:"sk"},i={dutch:/[^A-Za-zàèéìòóù0-9_'-]+/gim,english:/[^A-Za-zàèéìòóù0-9_'-]+/gim,french:/[^a-z0-9äâàéèëêïîöôùüûœç-]+/gim,italian:/[^A-Za-zàèéìòóù0-9_'-]+/gim,norwegian:/[^a-z0-9_æøåÆØÅäÄöÖüÜ]+/gim,portuguese:/[^a-z0-9à-úÀ-Ú]/gim,russian:/[^a-z0-9а-яА-ЯёЁ]+/gim,spanish:/[^a-z0-9A-Zá-úÁ-ÚñÑüÜ]+/gim,swedish:/[^a-z0-9_åÅäÄöÖüÜ-]+/gim,german:/[^a-z0-9A-ZäöüÄÖÜß]+/gim,finnish:/[^a-z0-9äöÄÖ]+/gim,danish:/[^a-z0-9æøåÆØÅ]+/gim,hungarian:/[^a-z0-9áéíóöőúüűÁÉÍÓÖŐÚÜŰ]+/gim,romanian:/[^a-z0-9ăâîșțĂÂÎȘȚ]+/gim,serbian:/[^a-z0-9čćžšđČĆŽŠĐ]+/gim,turkish:/[^a-z0-9çÇğĞıİöÖşŞüÜ]+/gim,lithuanian:/[^a-z0-9ąčęėįšųūžĄČĘĖĮŠŲŪŽ]+/gim,arabic:/[^a-z0-9أ-ي]+/gim,nepali:/[^a-z0-9अ-ह]+/gim,irish:/[^a-z0-9áéíóúÁÉÍÓÚ]+/gim,indian:/[^a-z0-9अ-ह]+/gim,armenian:/[^a-z0-9ա-ֆ]+/gim,greek:/[^a-z0-9α-ωά-ώ]+/gim,indonesian:/[^a-z0-9]+/gim,ukrainian:/[^a-z0-9а-яА-ЯіїєІЇЄ]+/gim,slovenian:/[^a-z0-9čžšČŽŠ]+/gim,bulgarian:/[^a-z0-9а-яА-Я]+/gim,tamil:/[^a-z0-9அ-ஹ]+/gim,sanskrit:/[^a-z0-9A-Zāīūṛḷṃṁḥśṣṭḍṇṅñḻḹṝ]+/gim,czech:/[^A-Z0-9a-zěščřžýáíéúůóťďĚŠČŘŽÝÁÍÉÓÚŮŤĎ-]+/gim},s=Object.keys(o),a=Date.now().toString().slice(5),l=0,u=BigInt(1e3),c=BigInt(1e6),f=BigInt(1e9);function d(e,t){if(t.length<65535)Array.prototype.push.apply(e,t);else{let r=t.length;for(let n=0;n<r;n+=65535)Array.prototype.push.apply(e,t.slice(n,n+65535))}}function h(){return BigInt(Math.floor(1e6*performance.now()))}function p(e){return("number"==typeof e&&(e=BigInt(e)),e<u)?`${e}ns`:e<c?`${e/u}μs`:e<f?`${e/c}ms`:`${e/f}s`}function g(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?h():"undefined"!=typeof process&&process.release&&"node"===process.release.name||"undefined"!=typeof process&&"function"==typeof process?.hrtime?.bigint?process.hrtime.bigint():"undefined"!=typeof performance?h():BigInt(0)}function m(){return`${a}-${l++}`}function y(e,t){return void 0===Object.hasOwn?Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0:Object.hasOwn(e,t)?e[t]:void 0}function b(e,t){return t[1]===e[1]?e[0]-t[0]:t[1]-e[1]}function S(e,t){let r={},n=t.length;for(let o=0;o<n;o++){let n=t[o],i=n.split("."),s=e,a=i.length;for(let e=0;e<a;e++)if("object"==typeof(s=s[i[e]])){if(null!==s&&"lat"in s&&"lon"in s&&"number"==typeof s.lat&&"number"==typeof s.lon){s=r[n]=s;break}else if(!Array.isArray(s)&&null!==s&&e===a-1){s=void 0;break}}else if((null===s||"object"!=typeof s)&&e<a-1){s=void 0;break}void 0!==s&&(r[n]=s)}return r}function I(e,t){return S(e,[t])[t]}let w={cm:.01,m:1,km:1e3,ft:.3048,yd:.9144,mi:1609.344};function O(e,t){e.hits=e.hits.map(e=>({...e,document:{...e.document,...t.reduce((e,t)=>{let r=t.split("."),n=r.pop(),o=e;for(let e of r)o[e]=o[e]??{},o=o[e];return o[n]=null,e},e.document)}}))}function T(e){return Array.isArray(e)?e.some(e=>T(e)):e?.constructor?.name==="AsyncFunction"}let N="intersection"in new Set,D="union"in new Set;function v(e,t){return D?e?e.union(t):t:new Set(e?[...e,...t]:t)}let _=s.join("\n - "),A={NO_LANGUAGE_WITH_CUSTOM_TOKENIZER:"Do not pass the language option to create when using a custom tokenizer.",LANGUAGE_NOT_SUPPORTED:`Language "%s" is not supported.
Supported languages are:
 - ${_}`,INVALID_STEMMER_FUNCTION_TYPE:"config.stemmer property must be a function.",MISSING_STEMMER:'As of version 1.0.0 @orama/orama does not ship non English stemmers by default. To solve this, please explicitly import and specify the "%s" stemmer from the package @orama/stemmers. See https://docs.orama.com/open-source/text-analysis/stemming for more information.',CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY:"Custom stop words array must only contain strings.",UNSUPPORTED_COMPONENT:'Unsupported component "%s".',COMPONENT_MUST_BE_FUNCTION:'The component "%s" must be a function.',COMPONENT_MUST_BE_FUNCTION_OR_ARRAY_FUNCTIONS:'The component "%s" must be a function or an array of functions.',INVALID_SCHEMA_TYPE:'Unsupported schema type "%s" at "%s". Expected "string", "boolean" or "number" or array of them.',DOCUMENT_ID_MUST_BE_STRING:'Document id must be of type "string". Got "%s" instead.',DOCUMENT_ALREADY_EXISTS:'A document with id "%s" already exists.',DOCUMENT_DOES_NOT_EXIST:'A document with id "%s" does not exists.',MISSING_DOCUMENT_PROPERTY:'Missing searchable property "%s".',INVALID_DOCUMENT_PROPERTY:'Invalid document property "%s": expected "%s", got "%s"',UNKNOWN_INDEX:'Invalid property name "%s". Expected a wildcard string ("*") or array containing one of the following properties: %s',INVALID_BOOST_VALUE:"Boost value must be a number greater than, or less than 0.",INVALID_FILTER_OPERATION:"You can only use one operation per filter, you requested %d.",SCHEMA_VALIDATION_FAILURE:'Cannot insert document due schema validation failure on "%s" property.',INVALID_SORT_SCHEMA_TYPE:'Unsupported sort schema type "%s" at "%s". Expected "string" or "number".',CANNOT_SORT_BY_ARRAY:'Cannot configure sort for "%s" because it is an array (%s).',UNABLE_TO_SORT_ON_UNKNOWN_FIELD:'Unable to sort on unknown field "%s". Allowed fields: %s',SORT_DISABLED:"Sort is disabled. Please read the documentation at https://docs.oramasearch for more information.",UNKNOWN_GROUP_BY_PROPERTY:'Unknown groupBy property "%s".',INVALID_GROUP_BY_PROPERTY:'Invalid groupBy property "%s". Allowed types: "%s", but given "%s".',UNKNOWN_FILTER_PROPERTY:'Unknown filter property "%s".',INVALID_VECTOR_SIZE:'Vector size must be a number greater than 0. Got "%s" instead.',INVALID_VECTOR_VALUE:'Vector value must be a number greater than 0. Got "%s" instead.',INVALID_INPUT_VECTOR:`Property "%s" was declared as a %s-dimensional vector, but got a %s-dimensional vector instead.
Input vectors must be of the size declared in the schema, as calculating similarity between vectors of different sizes can lead to unexpected results.`,WRONG_SEARCH_PROPERTY_TYPE:'Property "%s" is not searchable. Only "string" properties are searchable.',FACET_NOT_SUPPORTED:'Facet doens\'t support the type "%s".',INVALID_DISTANCE_SUFFIX:'Invalid distance suffix "%s". Valid suffixes are: cm, m, km, mi, yd, ft.',INVALID_SEARCH_MODE:'Invalid search mode "%s". Valid modes are: "fulltext", "vector", "hybrid".',MISSING_VECTOR_AND_SECURE_PROXY:"No vector was provided and no secure proxy was configured. Please provide a vector or configure an Orama Secure Proxy to perform hybrid search.",MISSING_TERM:'"term" is a required parameter when performing hybrid search. Please provide a search term.',INVALID_VECTOR_INPUT:'Invalid "vector" property. Expected an object with "value" and "property" properties, but got "%s" instead.',PLUGIN_CRASHED:"A plugin crashed during initialization. Please check the error message for more information:",PLUGIN_SECURE_PROXY_NOT_FOUND:`Could not find '@orama/secure-proxy-plugin' installed in your Orama instance.
Please install it before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,PLUGIN_SECURE_PROXY_MISSING_CHAT_MODEL:`Could not find a chat model defined in the secure proxy plugin configuration.
Please provide a chat model before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,ANSWER_SESSION_LAST_MESSAGE_IS_NOT_ASSISTANT:"The last message in the session is not an assistant message. Cannot regenerate non-assistant messages.",PLUGIN_COMPONENT_CONFLICT:'The component "%s" is already defined. The plugin "%s" is trying to redefine it.'};function E(e,...t){let r=Error(function(e,...t){return e.replace(/%(?:(?<position>\d+)\$)?(?<width>-?\d*\.?\d*)(?<type>[dfs])/g,function(...e){let{width:r,type:n,position:o}=e[e.length-1],i=o?t[Number.parseInt(o)-1]:t.shift(),s=""===r?0:Number.parseInt(r);switch(n){case"d":return i.toString().padStart(s,"0");case"f":{let e=i,[t,n]=r.split(".").map(e=>Number.parseFloat(e));return"number"==typeof n&&n>=0&&(e=e.toFixed(n)),"number"==typeof t&&t>=0?e.toString().padStart(s,"0"):e.toString()}case"s":return s<0?i.toString().padEnd(-s," "):i.toString().padStart(s," ");default:return i}})}(A[e]??`Unsupported Orama Error code: ${e}`,...t));return r.code=e,"captureStackTrace"in Error.prototype&&Error.captureStackTrace(r),r}function k(e){return{raw:Number(e),formatted:p(e)}}function P(e){if(e.id){if("string"!=typeof e.id)throw E("DOCUMENT_ID_MUST_BE_STRING",typeof e.id);return e.id}return m()}let x={string:!1,number:!1,boolean:!1,enum:!1,geopoint:!1,"string[]":!0,"number[]":!0,"boolean[]":!0,"enum[]":!0},R={"string[]":"string","number[]":"number","boolean[]":"boolean","enum[]":"enum"};function M(e){return"string"==typeof e&&/^vector\[\d+\]$/.test(e)}function z(e){return"string"==typeof e&&x[e]}function C(e){let t=Number(e.slice(7,-1));switch(!0){case isNaN(t):throw E("INVALID_VECTOR_VALUE",e);case t<=0:throw E("INVALID_VECTOR_SIZE",e);default:return t}}function U(e){return{internalIdToId:e.internalIdToId}}function L(e,t){let{internalIdToId:r}=t;e.internalDocumentIDStore.idToInternalId.clear(),e.internalDocumentIDStore.internalIdToId=[];let n=r.length;for(let t=0;t<n;t++){let n=r[t];e.internalDocumentIDStore.idToInternalId.set(n,t+1),e.internalDocumentIDStore.internalIdToId.push(n)}}function B(e,t){if("string"==typeof t){let r=e.idToInternalId.get(t);if(r)return r;let n=e.idToInternalId.size+1;return e.idToInternalId.set(t,n),e.internalIdToId.push(t),n}return t>e.internalIdToId.length?B(e,t.toString()):t}function V(e,t){if(e.internalIdToId.length<t)throw Error(`Invalid internalId ${t}`);return e.internalIdToId[t-1]}function F(e,t){return{sharedInternalDocumentStore:t,docs:{},count:0}}function W(e,t){let r=B(e.sharedInternalDocumentStore,t);return e.docs[r]}function j(e,t){let r=t.length,n=Array.from({length:r});for(let o=0;o<r;o++){let r=B(e.sharedInternalDocumentStore,t[o]);n[o]=e.docs[r]}return n}function $(e){return e.docs}function J(e,t,r,n){return void 0===e.docs[r]&&(e.docs[r]=n,e.count++,!0)}function G(e,t){let r=B(e.sharedInternalDocumentStore,t);return void 0!==e.docs[r]&&(delete e.docs[r],e.count--,!0)}function Y(e){return e.count}function H(e,t){return{docs:t.docs,count:t.count,sharedInternalDocumentStore:e}}function K(e){return{docs:e.docs,count:e.count}}let q=["beforeInsert","afterInsert","beforeRemove","afterRemove","beforeUpdate","afterUpdate","beforeSearch","afterSearch","beforeInsertMultiple","afterInsertMultiple","beforeRemoveMultiple","afterRemoveMultiple","beforeUpdateMultiple","afterUpdateMultiple","beforeLoad","afterLoad","afterCreate"],X=["tokenizer","index","documentsStore","sorter"],Z=["validateSchema","getDocumentIndexId","getDocumentProperties","formatElapsedTime"];function Q(e,t,r,n,o){if(e.some(T))return(async()=>{for(let i of e)await i(t,r,n,o)})();for(let i of e)i(t,r,n,o)}function ee(e,t,r,n){if(e.some(T))return(async()=>{for(let o of e)await o(t,r,n)})();for(let o of e)o(t,r,n)}class et{k;v;l=null;r=null;h=1;constructor(e,t){this.k=e,this.v=new Set(t)}updateHeight(){this.h=Math.max(et.getHeight(this.l),et.getHeight(this.r))+1}static getHeight(e){return e?e.h:0}getBalanceFactor(){return et.getHeight(this.l)-et.getHeight(this.r)}rotateLeft(){let e=this.r;return this.r=e.l,e.l=this,this.updateHeight(),e.updateHeight(),e}rotateRight(){let e=this.l;return this.l=e.r,e.r=this,this.updateHeight(),e.updateHeight(),e}toJSON(){return{k:this.k,v:Array.from(this.v),l:this.l?this.l.toJSON():null,r:this.r?this.r.toJSON():null,h:this.h}}static fromJSON(e){let t=new et(e.k,e.v);return t.l=e.l?et.fromJSON(e.l):null,t.r=e.r?et.fromJSON(e.r):null,t.h=e.h,t}}class er{root=null;insertCount=0;constructor(e,t){void 0!==e&&void 0!==t&&(this.root=new et(e,t))}insert(e,t,r=1e3){this.root=this.insertNode(this.root,e,t,r)}insertMultiple(e,t,r=1e3){for(let n of t)this.insert(e,n,r)}rebalance(){this.root&&(this.root=this.rebalanceNode(this.root))}toJSON(){return{root:this.root?this.root.toJSON():null,insertCount:this.insertCount}}static fromJSON(e){let t=new er;return t.root=e.root?et.fromJSON(e.root):null,t.insertCount=e.insertCount||0,t}insertNode(e,t,r,n){if(null===e)return new et(t,[r]);let o=[],i=e,s=null;for(;null!==i;)if(o.push({parent:s,node:i}),t<i.k)if(null===i.l){i.l=new et(t,[r]),o.push({parent:i,node:i.l});break}else s=i,i=i.l;else if(!(t>i.k))return i.v.add(r),e;else if(null===i.r){i.r=new et(t,[r]),o.push({parent:i,node:i.r});break}else s=i,i=i.r;let a=!1;this.insertCount++%n==0&&(a=!0);for(let t=o.length-1;t>=0;t--){let{parent:r,node:n}=o[t];if(n.updateHeight(),a){let t=this.rebalanceNode(n);r?r.l===n?r.l=t:r.r===n&&(r.r=t):e=t}}return e}rebalanceNode(e){let t=e.getBalanceFactor();if(t>1){if(e.l&&e.l.getBalanceFactor()>=0)return e.rotateRight();else if(e.l)return e.l=e.l.rotateLeft(),e.rotateRight()}if(t<-1){if(e.r&&0>=e.r.getBalanceFactor())return e.rotateLeft();else if(e.r)return e.r=e.r.rotateRight(),e.rotateLeft()}return e}find(e){let t=this.findNodeByKey(e);return t?t.v:null}contains(e){return null!==this.find(e)}getSize(){let e=0,t=[],r=this.root;for(;r||t.length>0;){for(;r;)t.push(r),r=r.l;r=t.pop(),e++,r=r.r}return e}isBalanced(){if(!this.root)return!0;let e=[this.root];for(;e.length>0;){let t=e.pop();if(Math.abs(t.getBalanceFactor())>1)return!1;t.l&&e.push(t.l),t.r&&e.push(t.r)}return!0}remove(e){this.root=this.removeNode(this.root,e)}removeDocument(e,t){let r=this.findNodeByKey(e);r&&(1===r.v.size?this.root=this.removeNode(this.root,e):r.v=new Set([...r.v.values()].filter(e=>e!==t)))}findNodeByKey(e){let t=this.root;for(;t;)if(e<t.k)t=t.l;else{if(!(e>t.k))return t;t=t.r}return null}removeNode(e,t){if(null===e)return null;let r=[],n=e;for(;null!==n&&n.k!==t;)r.push(n),n=t<n.k?n.l:n.r;if(null===n)return e;if(null===n.l||null===n.r){let t=n.l?n.l:n.r;if(0===r.length)e=t;else{let e=r[r.length-1];e.l===n?e.l=t:e.r=t}}else{let e=n,t=n.r;for(;null!==t.l;)e=t,t=t.l;n.k=t.k,n.v=t.v,e.l===t?e.l=t.r:e.r=t.r,n=e}r.push(n);for(let t=r.length-1;t>=0;t--){let n=r[t];n.updateHeight();let o=this.rebalanceNode(n);if(t>0){let e=r[t-1];e.l===n?e.l=o:e.r===n&&(e.r=o)}else e=o}return e}rangeSearch(e,t){let r=new Set,n=[],o=this.root;for(;o||n.length>0;){for(;o;)n.push(o),o=o.l;if((o=n.pop()).k>=e&&o.k<=t&&(r=v(r,o.v)),o.k>t)break;o=o.r}return r}greaterThan(e,t=!1){let r=new Set,n=[],o=this.root;for(;o||n.length>0;){for(;o;)n.push(o),o=o.r;if(o=n.pop(),t&&o.k>=e||!t&&o.k>e)r=v(r,o.v);else if(o.k<=e)break;o=o.l}return r}lessThan(e,t=!1){let r=new Set,n=[],o=this.root;for(;o||n.length>0;){for(;o;)n.push(o),o=o.l;if(o=n.pop(),t&&o.k<=e||!t&&o.k<e)r=v(r,o.v);else if(o.k>e)break;o=o.r}return r}}class en{numberToDocumentId;constructor(){this.numberToDocumentId=new Map}insert(e,t){this.numberToDocumentId.has(e)?this.numberToDocumentId.get(e).add(t):this.numberToDocumentId.set(e,new Set([t]))}find(e){let t=this.numberToDocumentId.get(e);return t?Array.from(t):null}remove(e){this.numberToDocumentId.delete(e)}removeDocument(e,t){let r=this.numberToDocumentId.get(t);r&&(r.delete(e),0===r.size&&this.numberToDocumentId.delete(t))}contains(e){return this.numberToDocumentId.has(e)}getSize(){let e=0;for(let t of this.numberToDocumentId.values())e+=t.size;return e}filter(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"eq":{let t=e[r],n=this.numberToDocumentId.get(t);return n?Array.from(n):[]}case"in":{let t=e[r],n=new Set;for(let e of t){let t=this.numberToDocumentId.get(e);if(t)for(let e of t)n.add(e)}return Array.from(n)}case"nin":{let t=new Set(e[r]),n=new Set;for(let[e,r]of this.numberToDocumentId.entries())if(!t.has(e))for(let e of r)n.add(e);return Array.from(n)}default:throw Error("Invalid operation")}}filterArr(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"containsAll":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e].filter(e=>t.has(e)))))}case"containsAny":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e,...t])))}default:throw Error("Invalid operation")}}static fromJSON(e){if(!e.numberToDocumentId)throw Error("Invalid Flat Tree JSON");let t=new en;for(let[r,n]of e.numberToDocumentId)t.numberToDocumentId.set(r,new Set(n));return t}toJSON(){return{numberToDocumentId:Array.from(this.numberToDocumentId.entries()).map(([e,t])=>[e,Array.from(t)])}}}function eo(e,t,r){let n=function(e,t,r){if(r<0)return -1;if(e===t)return 0;let n=e.length,o=t.length;if(0===n)return o<=r?o:-1;if(0===o)return n<=r?n:-1;let i=Math.abs(n-o);if(e.startsWith(t))return i<=r?i:-1;if(t.startsWith(e))return 0;if(i>r)return -1;let s=[];for(let e=0;e<=n;e++){s[e]=[e];for(let t=1;t<=o;t++)s[e][t]=0===e?t:0}for(let i=1;i<=n;i++){let n=1/0;for(let r=1;r<=o;r++)e[i-1]===t[r-1]?s[i][r]=s[i-1][r-1]:s[i][r]=Math.min(s[i-1][r]+1,s[i][r-1]+1,s[i-1][r-1]+1),n=Math.min(n,s[i][r]);if(n>r)return -1}return s[n][o]<=r?s[n][o]:-1}(e,t,r);return{distance:n,isBounded:n>=0}}class ei{k;s;c=new Map;d=new Set;e;w="";constructor(e,t,r){this.k=e,this.s=t,this.e=r}updateParent(e){this.w=e.w+this.s}addDocument(e){this.d.add(e)}removeDocument(e){return this.d.delete(e)}findAllWords(e,t,r,n){let o=[this];for(;o.length>0;){let i=o.pop();if(i.e){let{w:o,d:s}=i;if(r&&o!==t)continue;if(null!==y(e,o))if(n){if(!(Math.abs(t.length-o.length)<=n)||!eo(t,o,n).isBounded)continue;e[o]=[]}else e[o]=[];if(null!=y(e,o)&&s.size>0){let t=e[o];for(let e of s)t.includes(e)||t.push(e)}}i.c.size>0&&o.push(...i.c.values())}return e}insert(e,t){let r=this,n=0,o=e.length;for(;n<o;){let i=e[n],s=r.c.get(i);if(s){let i=s.s,a=i.length,l=0;for(;l<a&&n+l<o&&i[l]===e[n+l];)l++;if(l===a){if(r=s,(n+=l)===o){s.e||(s.e=!0),s.addDocument(t);return}continue}let u=i.slice(0,l),c=i.slice(l),f=e.slice(n+l),d=new ei(u[0],u,!1);if(r.c.set(u[0],d),d.updateParent(r),s.s=c,s.k=c[0],d.c.set(c[0],s),s.updateParent(d),f){let e=new ei(f[0],f,!0);e.addDocument(t),d.c.set(f[0],e),e.updateParent(d)}else d.e=!0,d.addDocument(t);return}{let o=new ei(i,e.slice(n),!0);o.addDocument(t),r.c.set(i,o),o.updateParent(r);return}}r.e||(r.e=!0),r.addDocument(t)}_findLevenshtein(e,t,r,n,o){let i=[{node:this,index:t,tolerance:r}];for(;i.length>0;){let{node:t,index:r,tolerance:s}=i.pop();if(t.w.startsWith(e)){t.findAllWords(o,e,!1,0);continue}if(s<0)continue;if(t.e){let{w:r,d:i}=t;if(r&&(eo(e,r,n).isBounded&&(o[r]=[]),void 0!==y(o,r)&&i.size>0)){let e=new Set(o[r]);for(let t of i)e.add(t);o[r]=Array.from(e)}}if(r>=e.length)continue;let a=e[r];if(t.c.has(a)){let e=t.c.get(a);i.push({node:e,index:r+1,tolerance:s})}for(let[e,n]of(i.push({node:t,index:r+1,tolerance:s-1}),t.c))i.push({node:n,index:r,tolerance:s-1}),e!==a&&i.push({node:n,index:r+1,tolerance:s-1})}}find(e){let{term:t,exact:r,tolerance:n}=e;if(n&&!r){let e={};return this._findLevenshtein(t,0,n,n,e),e}{let e=this,o=0,i=t.length;for(;o<i;){let s=t[o],a=e.c.get(s);if(!a)return{};{let s=a.s,l=s.length,u=0;for(;u<l&&o+u<i&&s[u]===t[o+u];)u++;if(u===l)e=a,o+=u;else{if(o+u!==i||u!==i-o||r)return{};let e={};return a.findAllWords(e,t,r,n),e}}}let s={};return e.findAllWords(s,t,r,n),s}}contains(e){let t=this,r=0,n=e.length;for(;r<n;){let o=e[r],i=t.c.get(o);if(!i)return!1;{let o=i.s,s=o.length,a=0;for(;a<s&&r+a<n&&o[a]===e[r+a];)a++;if(a<s)return!1;r+=s,t=i}}return!0}removeWord(e){if(!e)return!1;let t=this,r=e.length,n=[];for(let o=0;o<r;o++){let r=e[o];if(!t.c.has(r))return!1;{let e=t.c.get(r);n.push({parent:t,character:r}),o+=e.s.length-1,t=e}}for(t.d.clear(),t.e=!1;n.length>0&&0===t.c.size&&!t.e&&0===t.d.size;){let{parent:e,character:r}=n.pop();e.c.delete(r),t=e}return!0}removeDocumentByWord(e,t,r=!0){if(!e)return!0;let n=this,o=e.length;for(let i=0;i<o;i++){let o=e[i];if(!n.c.has(o))return!1;{let s=n.c.get(o);i+=s.s.length-1,n=s,r&&n.w!==e||n.removeDocument(t)}}return!0}static getCommonPrefix(e,t){let r=Math.min(e.length,t.length),n=0;for(;n<r&&e.charCodeAt(n)===t.charCodeAt(n);)n++;return e.slice(0,n)}toJSON(){return{w:this.w,s:this.s,e:this.e,k:this.k,d:Array.from(this.d),c:Array.from(this.c?.entries())?.map(([e,t])=>[e,t.toJSON()])}}static fromJSON(e){let t=new ei(e.k,e.s,e.e);return t.w=e.w,t.d=new Set(e.d),t.c=new Map(e?.c?.map(([e,t])=>[e,ei.fromJSON(t)])),t}}class es extends ei{constructor(){super("","",!1)}static fromJSON(e){let t=new es;return t.w=e.w,t.s=e.s,t.e=e.e,t.k=e.k,t.d=new Set(e.d),t.c=new Map(e.c?.map(([e,t])=>[e,ei.fromJSON(t)])),t}toJSON(){return super.toJSON()}}class ea{point;docIDs;left;right;parent;constructor(e,t){this.point=e,this.docIDs=new Set(t),this.left=null,this.right=null,this.parent=null}toJSON(){return{point:this.point,docIDs:Array.from(this.docIDs),left:this.left?this.left.toJSON():null,right:this.right?this.right.toJSON():null}}static fromJSON(e,t=null){let r=new ea(e.point,e.docIDs);return r.parent=t,e.left&&(r.left=ea.fromJSON(e.left,r)),e.right&&(r.right=ea.fromJSON(e.right,r)),r}}class el{root;nodeMap;constructor(){this.root=null,this.nodeMap=new Map}getPointKey(e){return`${e.lon},${e.lat}`}insert(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);if(n)return void t.forEach(e=>n.docIDs.add(e));let o=new ea(e,t);if(this.nodeMap.set(r,o),null==this.root){this.root=o;return}let i=this.root,s=0;for(;;){if(0==s%2)if(e.lon<i.point.lon){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}else if(e.lat<i.point.lat){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}s++}}contains(e){let t=this.getPointKey(e);return this.nodeMap.has(t)}getDocIDsByCoordinates(e){let t=this.getPointKey(e),r=this.nodeMap.get(t);return r?Array.from(r.docIDs):null}removeDocByID(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);n&&(n.docIDs.delete(t),0===n.docIDs.size&&(this.nodeMap.delete(r),this.deleteNode(n)))}deleteNode(e){let t=e.parent,r=e.left?e.left:e.right;r&&(r.parent=t),t?t.left===e?t.left=r:t.right===e&&(t.right=r):(this.root=r,this.root&&(this.root.parent=null))}searchByRadius(e,t,r=!0,n="asc",o=!1){let i=o?el.vincentyDistance:el.haversineDistance,s=[{node:this.root,depth:0}],a=[];for(;s.length>0;){let{node:n,depth:o}=s.pop();if(null==n)continue;let l=i(e,n.point);(r?l<=t:l>t)&&a.push({point:n.point,docIDs:Array.from(n.docIDs)}),null!=n.left&&s.push({node:n.left,depth:o+1}),null!=n.right&&s.push({node:n.right,depth:o+1})}return n&&a.sort((t,r)=>{let o=i(e,t.point),s=i(e,r.point);return"asc"===n.toLowerCase()?o-s:s-o}),a}searchByPolygon(e,t=!0,r=null,n=!1){let o=[{node:this.root,depth:0}],i=[];for(;o.length>0;){let{node:r,depth:n}=o.pop();if(null==r)continue;null!=r.left&&o.push({node:r.left,depth:n+1}),null!=r.right&&o.push({node:r.right,depth:n+1});let s=el.isPointInPolygon(e,r.point);(s&&t||!s&&!t)&&i.push({point:r.point,docIDs:Array.from(r.docIDs)})}let s=el.calculatePolygonCentroid(e);if(r){let e=n?el.vincentyDistance:el.haversineDistance;i.sort((t,n)=>{let o=e(s,t.point),i=e(s,n.point);return"asc"===r.toLowerCase()?o-i:i-o})}return i}toJSON(){return{root:this.root?this.root.toJSON():null}}static fromJSON(e){let t=new el;return e.root&&(t.root=ea.fromJSON(e.root),t.buildNodeMap(t.root)),t}buildNodeMap(e){if(null==e)return;let t=this.getPointKey(e.point);this.nodeMap.set(t,e),e.left&&this.buildNodeMap(e.left),e.right&&this.buildNodeMap(e.right)}static calculatePolygonCentroid(e){let t=0,r=0,n=0,o=e.length;for(let i=0,s=o-1;i<o;s=i++){let o=e[i].lon,a=e[i].lat,l=e[s].lon,u=e[s].lat,c=o*u-l*a;t+=c,r+=(o+l)*c,n+=(a+u)*c}let i=6*(t/=2);return{lon:r/=i,lat:n/=i}}static isPointInPolygon(e,t){let r=!1,n=t.lon,o=t.lat,i=e.length;for(let t=0,s=i-1;t<i;s=t++){let i=e[t].lon,a=e[t].lat,l=e[s].lon,u=e[s].lat;a>o!=u>o&&n<(l-i)*(o-a)/(u-a)+i&&(r=!r)}return r}static haversineDistance(e,t){let r=Math.PI/180,n=e.lat*r,o=t.lat*r,i=(t.lat-e.lat)*r,s=(t.lon-e.lon)*r,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(n)*Math.cos(o)*Math.sin(s/2)*Math.sin(s/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371e3}static vincentyDistance(e,t){let r,n,o,i,s,a,l,u=1/298.257223563,c=(1-1/298.257223563)*6378137,f=Math.PI/180,d=e.lat*f,h=t.lat*f,p=(t.lon-e.lon)*f,g=Math.atan((1-u)*Math.tan(d)),m=Math.atan((1-u)*Math.tan(h)),y=Math.sin(g),b=Math.cos(g),S=Math.sin(m),I=Math.cos(m),w=p,O=1e3;do{let e=Math.sin(w),t=Math.cos(w);if(0===(n=Math.sqrt(I*e*(I*e)+(b*S-y*I*t)*(b*S-y*I*t))))return 0;i=Math.atan2(n,o=y*S+b*I*t),isNaN(l=o-2*y*S/(a=1-(s=b*I*e/n)*s))&&(l=0);let c=u/16*a*(4+u*(4-3*a));r=w,w=p+(1-c)*u*s*(i+c*n*(l+c*o*(-1+2*l*l)))}while(Math.abs(w-r)>1e-12&&--O>0);if(0===O)return NaN;let T=a*(0x24ffb2985f71-c*c)/(c*c),N=1+T/16384*(4096+T*(-768+T*(320-175*T))),D=T/1024*(256+T*(-128+T*(74-47*T)));return c*N*(i-D*n*(l+D/4*(o*(-1+2*l*l)-D/6*l*(-3+4*n*n)*(-3+4*l*l))))}}class eu{true;false;constructor(){this.true=new Set,this.false=new Set}insert(e,t){t?this.true.add(e):this.false.add(e)}delete(e,t){t?this.true.delete(e):this.false.delete(e)}getSize(){return this.true.size+this.false.size}toJSON(){return{true:Array.from(this.true),false:Array.from(this.false)}}static fromJSON(e){let t=new eu;return t.true=new Set(e.true),t.false=new Set(e.false),t}}class ec{size;vectors=new Map;constructor(e){this.size=e}add(e,t){t instanceof Float32Array||(t=new Float32Array(t));let r=ef(t,this.size);this.vectors.set(e,[r,t])}remove(e){this.vectors.delete(e)}find(e,t,r){return e instanceof Float32Array||(e=new Float32Array(e)),function(e,t,r,n,o){let i=ef(e,n),s=[];for(let a of t||r.keys()){let t=r.get(a);if(!t)continue;let l=t[0],u=t[1],c=0;for(let t=0;t<n;t++)c+=e[t]*u[t];let f=c/(i*l);f>=o&&s.push([a,f])}return s}(e,r,this.vectors,this.size,t)}toJSON(){let e=[];for(let[t,[r,n]]of this.vectors)e.push([t,[r,Array.from(n)]]);return{size:this.size,vectors:e}}static fromJSON(e){let t=new ec(e.size);for(let[r,[n,o]]of e.vectors)t.vectors.set(r,[n,new Float32Array(o)]);return t}}function ef(e,t){let r=0;for(let n=0;n<t;n++)r+=e[n]*e[n];return Math.sqrt(r)}function ed(e,t,r,n,o){let i=B(e.sharedInternalDocumentStore,r);e.avgFieldLength[t]=((e.avgFieldLength[t]??0)*(o-1)+n.length)/o,e.fieldLengths[t][i]=n.length,e.frequencies[t][i]={}}function eh(e,t,r,n,o){let i=0;for(let e of n)e===o&&i++;let s=B(e.sharedInternalDocumentStore,r),a=i/n.length;e.frequencies[t][s][o]=a,o in e.tokenOccurrences[t]||(e.tokenOccurrences[t][o]=0),e.tokenOccurrences[t][o]=(e.tokenOccurrences[t][o]??0)+1}function ep(e,t,r,n){let o=B(e.sharedInternalDocumentStore,r);n>1?e.avgFieldLength[t]=(e.avgFieldLength[t]*n-e.fieldLengths[t][o])/(n-1):e.avgFieldLength[t]=void 0,e.fieldLengths[t][o]=void 0,e.frequencies[t][o]=void 0}function eg(e,t,r){e.tokenOccurrences[t][r]--}function em(e,t,r,n,o,i,s,a,l,u,c){if(M(s)){var f,d,h,p,g;return f=t,d=r,h=i,p=0,g=o,void f.vectorIndexes[d].node.add(g,h)}let m=n=>{let{type:i,node:s}=t.indexes[r];switch(i){case"Bool":s[n?"true":"false"].add(o);break;case"AVL":{let e=c?.avlRebalanceThreshold??1;s.insert(n,o,e);break}case"Radix":{let i=l.tokenize(n,a,r,!1);for(let n of(e.insertDocumentScoreParameters(t,r,o,i,u),i))e.insertTokenScoreParameters(t,r,o,i,n),s.insert(n,o);break}case"Flat":s.insert(n,o);break;case"BKD":s.insert(n,[o])}};if(!z(s))return m(i);let y=i.length;for(let e=0;e<y;e++)m(i[e])}function ey(e,t,r,n,o,i,s,a,l,u){if(M(s))return t.vectorIndexes[r].node.remove(o),!0;let{type:c,node:f}=t.indexes[r];switch(c){case"AVL":return f.removeDocument(i,o),!0;case"Bool":return f[i?"true":"false"].delete(o),!0;case"Radix":{let s=l.tokenize(i,a,r);for(let i of(e.removeDocumentScoreParameters(t,r,n,u),s))e.removeTokenScoreParameters(t,r,i),f.removeDocumentByWord(i,o);return!0}case"Flat":return f.removeDocument(o,i),!0;case"BKD":return f.removeDocByID(i,o),!1}}function eb(e,t,r,n,o,i,s,a,l,u){if(!z(s))return ey(e,t,r,n,o,i,s,a,l,u);let c=R[s],f=i.length;for(let s=0;s<f;s++)ey(e,t,r,n,o,i[s],c,a,l,u);return!0}function eS(e,t,r,n,o,i,s,a,l,u){let c=Array.from(n),f=e.avgFieldLength[t],d=e.fieldLengths[t],h=e.tokenOccurrences[t],p=e.frequencies[t],g="number"==typeof h[r]?h[r]??0:0,m=c.length;for(let e=0;e<m;e++){let n=c[e];if(l&&!l.has(n))continue;u.has(n)||u.set(n,new Map);let h=u.get(n);h.set(t,(h.get(t)||0)+1);let m=function(e,t,r,n,o,{k:i,b:s,d:a}){return Math.log(1+(r-t+.5)/(t+.5))*(a+e*(i+1))/(e+i*(1-s+s*n/o))}(p?.[n]?.[r]??0,g,o,d[n],f,i);s.has(n)?s.set(n,s.get(n)+m*a):s.set(n,m*a)}}function eI(e,t,r,n,o,i,s,a,l,u,c,f=0){let d=r.tokenize(t,n),h=d.length||1,p=new Map,g=new Map,m=new Map;for(let r of o){if(!(r in e.indexes))continue;let n=e.indexes[r],{type:o}=n;if("Radix"!==o)throw E("WRONG_SEARCH_PROPERTY_TYPE",r);let f=a[r]??1;if(f<=0)throw E("INVALID_BOOST_VALUE",f);0!==d.length||t||d.push("");let h=d.length;for(let t=0;t<h;t++){let o=d[t],a=n.node.find({term:o,exact:i,tolerance:s}),h=Object.keys(a);h.length>0&&g.set(o,!0);let y=h.length;for(let t=0;t<y;t++){let n=h[t],o=a[n];eS(e,r,n,o,u,l,m,f,c,p)}}}let y=Array.from(m.entries()).map(([e,t])=>[e,t]).sort((e,t)=>t[1]-e[1]);if(0===y.length)return[];if(1===f)return y;if(0===f){if(1===h)return y;for(let e of d)if(!g.get(e))return[];return y.filter(([e])=>{let t=p.get(e);return!!t&&Array.from(t.values()).some(e=>e===h)})}let b=y.filter(([e])=>{let t=p.get(e);return!!t&&Array.from(t.values()).some(e=>e===h)});if(b.length>0){let e=y.filter(([e])=>!b.some(([t])=>t===e)),t=Math.ceil(e.length*f);return[...b,...e.slice(0,t)]}return y}function ew(e,t,r,n){let o=Object.keys(r),i=o.reduce((e,t)=>({[t]:new Set,...e}),{});for(let s of o){let o=r[s];if(void 0===e.indexes[s])throw E("UNKNOWN_FILTER_PROPERTY",s);let{node:a,type:l,isArray:u}=e.indexes[s];if("Bool"===l){let e=o?a.true:a.false;i[s]=v(i[s],e);continue}if("BKD"===l){let e;if("radius"in o)e="radius";else if("polygon"in o)e="polygon";else throw Error(`Invalid operation ${o}`);if("radius"===e){let{value:t,coordinates:r,unit:n="m",inside:l=!0,highPrecision:u=!1}=o[e],c=function(e,t){let r=w[t];if(void 0===r)throw Error(E("INVALID_DISTANCE_SUFFIX",e).message);return e*r}(t,n),f=a.searchByRadius(r,c,l,void 0,u);i[s]=ev(i[s],f)}else{let{coordinates:t,inside:r=!0,highPrecision:n=!1}=o[e],l=a.searchByPolygon(t,r,void 0,n);i[s]=ev(i[s],l)}continue}if("Radix"===l&&("string"==typeof o||Array.isArray(o))){for(let e of[o].flat())for(let r of t.tokenize(e,n,s)){let e=a.find({term:r,exact:!0});i[s]=function(e,t){e||(e=new Set);let r=Object.keys(t),n=r.length;for(let o=0;o<n;o++){let n=t[r[o]],i=n.length;for(let t=0;t<i;t++)e.add(n[t])}return e}(i[s],e)}continue}let c=Object.keys(o);if(c.length>1)throw E("INVALID_FILTER_OPERATION",c.length);if("Flat"===l){let e=new Set(u?a.filterArr(o):a.filter(o));i[s]=v(i[s],e);continue}if("AVL"===l){let e,t=c[0],r=o[t];switch(t){case"gt":e=a.greaterThan(r,!1);break;case"gte":e=a.greaterThan(r,!0);break;case"lt":e=a.lessThan(r,!1);break;case"lte":e=a.lessThan(r,!0);break;case"eq":e=a.find(r)??new Set;break;case"between":{let[t,n]=r;e=a.rangeSearch(t,n);break}default:throw E("INVALID_FILTER_OPERATION",t)}i[s]=v(i[s],e)}}return function(...e){if(0===e.length)return new Set;if(1===e.length)return e[0];if(2===e.length){let t=e[0],r=e[1];if(N)return t.intersection(r);let n=new Set,o=t.size<r.size?t:r,i=o===t?r:t;for(let e of o)i.has(e)&&n.add(e);return n}let t={index:0,size:e[0].size};for(let r=1;r<e.length;r++)e[r].size<t.size&&(t.index=r,t.size=e[r].size);if(N){let r=e[t.index];for(let n=0;n<e.length;n++)n!==t.index&&(r=r.intersection(e[n]));return r}let r=e[t.index];for(let n=0;n<e.length;n++){if(n===t.index)continue;let o=e[n];for(let e of r)o.has(e)||r.delete(e)}return r}(...Object.values(i))}function eO(e){return e.searchableProperties}function eT(e){return e.searchablePropertiesWithTypes}function eN(e,t){let{indexes:r,vectorIndexes:n,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:s,tokenOccurrences:a,avgFieldLength:l,fieldLengths:u}=t,c={},f={};for(let e of Object.keys(r)){let{node:t,type:n,isArray:o}=r[e];switch(n){case"Radix":c[e]={type:"Radix",node:es.fromJSON(t),isArray:o};break;case"Flat":c[e]={type:"Flat",node:en.fromJSON(t),isArray:o};break;case"AVL":c[e]={type:"AVL",node:er.fromJSON(t),isArray:o};break;case"BKD":c[e]={type:"BKD",node:el.fromJSON(t),isArray:o};break;case"Bool":c[e]={type:"Bool",node:eu.fromJSON(t),isArray:o};break;default:c[e]=r[e]}}for(let e of Object.keys(n))f[e]={type:"Vector",isArray:!1,node:ec.fromJSON(n[e])};return{sharedInternalDocumentStore:e,indexes:c,vectorIndexes:f,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:s,tokenOccurrences:a,avgFieldLength:l,fieldLengths:u}}function eD(e){let{indexes:t,vectorIndexes:r,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:s,avgFieldLength:a,fieldLengths:l}=e,u={};for(let e of Object.keys(r))u[e]=r[e].node.toJSON();let c={};for(let e of Object.keys(t)){let{type:r,node:n,isArray:o}=t[e];"Flat"===r||"Radix"===r||"AVL"===r||"BKD"===r||"Bool"===r?c[e]={type:r,node:n.toJSON(),isArray:o}:(c[e]=t[e],c[e].node=c[e].node.toJSON())}return{indexes:c,vectorIndexes:u,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:s,avgFieldLength:a,fieldLengths:l}}function ev(e,t){e||(e=new Set);let r=t.length;for(let n=0;n<r;n++){let r=t[n].docIDs,o=r.length;for(let t=0;t<o;t++)e.add(r[t])}return e}function e_(e,t,r,n){return n?.enabled===!1?{disabled:!0}:function e(t,r,n,o,i){let s={language:t.tokenizer.language,sharedInternalDocumentStore:r,enabled:!0,isSorted:!0,sortableProperties:[],sortablePropertiesWithTypes:{},sorts:{}};for(let[a,l]of Object.entries(n)){let n=`${i}${i?".":""}${a}`;if(!o.includes(n)){if("object"==typeof l&&!Array.isArray(l)){let i=e(t,r,l,o,n);d(s.sortableProperties,i.sortableProperties),s.sorts={...s.sorts,...i.sorts},s.sortablePropertiesWithTypes={...s.sortablePropertiesWithTypes,...i.sortablePropertiesWithTypes};continue}if(!M(l))switch(l){case"boolean":case"number":case"string":s.sortableProperties.push(n),s.sortablePropertiesWithTypes[n]=l,s.sorts[n]={docs:new Map,orderedDocsToRemove:new Map,orderedDocs:[],type:l};break;case"geopoint":case"enum":case"enum[]":case"boolean[]":case"number[]":case"string[]":continue;default:throw E("INVALID_SORT_SCHEMA_TYPE",Array.isArray(l)?"array":l,n)}}}return s}(e,t,r,(n||{}).unsortableProperties||[],"")}function eA(e,t,r,n){if(!e.enabled)return;e.isSorted=!1;let o=B(e.sharedInternalDocumentStore,r),i=e.sorts[t];i.orderedDocsToRemove.has(o)&&eR(e,t),i.docs.set(o,i.orderedDocs.length),i.orderedDocs.push([o,n])}function eE(e){if(!e.isSorted&&e.enabled){for(let t of Object.keys(e.sorts))!function(e,t){let r,n=e.sorts[t];switch(n.type){case"string":r=ek.bind(null,e.language);break;case"number":r=eP.bind(null);break;case"boolean":r=ex.bind(null)}n.orderedDocs.sort(r);let o=n.orderedDocs.length;for(let e=0;e<o;e++){let t=n.orderedDocs[e][0];n.docs.set(t,e)}}(e,t);e.isSorted=!0}}function ek(e,t,r){return t[1].localeCompare(r[1],void 0!==e&&s.includes(e)?o[e]:void 0)}function eP(e,t){return e[1]-t[1]}function ex(e,t){return t[1]?-1:1}function eR(e,t){let r=e.sorts[t];r.orderedDocsToRemove.size&&(r.orderedDocs=r.orderedDocs.filter(e=>!r.orderedDocsToRemove.has(e[0])),r.orderedDocsToRemove.clear())}function eM(e,t,r){if(!e.enabled)return;let n=e.sorts[t],o=B(e.sharedInternalDocumentStore,r);n.docs.get(o)&&(n.docs.delete(o),n.orderedDocsToRemove.set(o,!0))}function ez(e,t,r){if(!e.enabled)throw E("SORT_DISABLED");let n=r.property,o="DESC"===r.order,i=e.sorts[n];if(!i)throw E("UNABLE_TO_SORT_ON_UNKNOWN_FIELD",n,e.sortableProperties.join(", "));return eR(e,n),eE(e),t.sort((t,r)=>{let n=i.docs.get(B(e.sharedInternalDocumentStore,t[0])),s=i.docs.get(B(e.sharedInternalDocumentStore,r[0])),a=void 0!==n,l=void 0!==s;return a||l?a?l?o?s-n:n-s:-1:1:0}),t}function eC(e){return e.enabled?e.sortableProperties:[]}function eU(e){return e.enabled?e.sortablePropertiesWithTypes:{}}function eL(e,t){if(!t.enabled)return{enabled:!1};let r=Object.keys(t.sorts).reduce((e,r)=>{let{docs:n,orderedDocs:o,type:i}=t.sorts[r];return e[r]={docs:new Map(Object.entries(n).map(([e,t])=>[+e,t])),orderedDocsToRemove:new Map,orderedDocs:o,type:i},e},{});return{sharedInternalDocumentStore:e,language:t.language,sortableProperties:t.sortableProperties,sortablePropertiesWithTypes:t.sortablePropertiesWithTypes,sorts:r,enabled:!0,isSorted:t.isSorted}}function eB(e){if(!e.enabled)return{enabled:!1};for(let t of Object.keys(e.sorts))eR(e,t);eE(e);let t=Object.keys(e.sorts).reduce((t,r)=>{let{docs:n,orderedDocs:o,type:i}=e.sorts[r];return t[r]={docs:Object.fromEntries(n.entries()),orderedDocs:o,type:i},t},{});return{language:e.language,sortableProperties:e.sortableProperties,sortablePropertiesWithTypes:e.sortablePropertiesWithTypes,sorts:t,enabled:e.enabled,isSorted:e.isSorted}}let eV=[65,65,65,65,65,65,65,67,69,69,69,69,73,73,73,73,69,78,79,79,79,79,79,null,79,85,85,85,85,89,80,115,97,97,97,97,97,97,97,99,101,101,101,101,105,105,105,105,101,110,111,111,111,111,111,null,111,117,117,117,117,121,112,121,65,97,65,97,65,97,67,99,67,99,67,99,67,99,68,100,68,100,69,101,69,101,69,101,69,101,69,101,71,103,71,103,71,103,71,103,72,104,72,104,73,105,73,105,73,105,73,105,73,105,73,105,74,106,75,107,107,76,108,76,108,76,108,76,108,76,108,78,110,78,110,78,110,110,78,110,79,111,79,111,79,111,79,111,82,114,82,114,82,114,83,115,83,115,83,115,83,115,84,116,84,116,84,116,85,117,85,117,85,117,85,117,85,117,85,117,87,119,89,121,89,90,122,90,122,90,122,115],eF={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},eW={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},ej="[aeiouy]",e$="[^aeiou][^aeiouy]*",eJ=ej+"[aeiou]*",eG="^("+e$+")?"+eJ+e$,eY="^("+e$+")?"+eJ+e$+"("+eJ+")?$",eH="^("+e$+")?"+eJ+e$+eJ+e$,eK="^("+e$+")?"+ej;function eq(e){let t,r,n,o,i,s;if(e.length<3)return e;let a=e.substring(0,1);if("y"==a&&(e=a.toUpperCase()+e.substring(1)),o=/^(.+?)([^s])s$/,(n=/^(.+?)(ss|i)es$/).test(e)?e=e.replace(n,"$1$2"):o.test(e)&&(e=e.replace(o,"$1$2")),o=/^(.+?)(ed|ing)$/,(n=/^(.+?)eed$/).test(e)){let t=n.exec(e);(n=new RegExp(eG)).test(t[1])&&(n=/.$/,e=e.replace(n,""))}else o.test(e)&&(t=o.exec(e)[1],(o=new RegExp(eK)).test(t)&&(e=t,o=/(at|bl|iz)$/,i=RegExp("([^aeiouylsz])\\1$"),s=RegExp("^"+e$+ej+"[^aeiouwxy]$"),o.test(e)?e+="e":i.test(e)?(n=/.$/,e=e.replace(n,"")):s.test(e)&&(e+="e")));if((n=/^(.+?)y$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(eK),t&&n.test(t)&&(e=t+"i")}if((n=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(eG),t&&n.test(t)&&(e=t+eF[r])}if((n=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(eG),t&&n.test(t)&&(e=t+eW[r])}if(o=/^(.+?)(s|t)(ion)$/,(n=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(eH),t&&n.test(t)&&(e=t)}else if(o.test(e)){let r=o.exec(e);t=r?.[1]??""+r?.[2]??"",(o=new RegExp(eH)).test(t)&&(e=t)}if((n=/^(.+?)e$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(eH),o=new RegExp(eY),i=RegExp("^"+e$+ej+"[^aeiouwxy]$"),t&&(n.test(t)||o.test(t)&&!i.test(t))&&(e=t)}return n=/ll$/,o=new RegExp(eH),n.test(e)&&o.test(e)&&(n=/.$/,e=e.replace(n,"")),"y"==a&&(e=a.toLowerCase()+e.substring(1)),e}function eX(e,t,r=!0){let n=`${this.language}:${e}:${t}`;return r&&this.normalizationCache.has(n)?this.normalizationCache.get(n):this.stopWords?.includes(t)?(r&&this.normalizationCache.set(n,""),""):(this.stemmer&&!this.stemmerSkipProperties.has(e)&&(t=this.stemmer(t)),t=function(e){let t=[];for(let n=0;n<e.length;n++){var r;t[n]=(r=e.charCodeAt(n))<192||r>383?r:eV[r-192]||r}return String.fromCharCode(...t)}(t),r&&this.normalizationCache.set(n,t),t)}function eZ(e,t,r,n=!0){let o;if(t&&t!==this.language)throw E("LANGUAGE_NOT_SUPPORTED",t);if("string"!=typeof e)return[e];let s=this.normalizeToken.bind(this,r??"");if(r&&this.tokenizeSkipProperties.has(r))o=[s(e,n)];else{let t=i[this.language];o=e.toLowerCase().split(t).map(e=>s(e,n)).filter(Boolean)}let a=function(e){for(;""===e[e.length-1];)e.pop();for(;""===e[0];)e.shift();return e}(o);return this.allowDuplicates?a:Array.from(new Set(a))}function eQ(e={}){let t,r;if(e.language){if(!s.includes(e.language))throw E("LANGUAGE_NOT_SUPPORTED",e.language)}else e.language="english";if(e.stemming||e.stemmer&&!("stemming"in e))if(e.stemmer){if("function"!=typeof e.stemmer)throw E("INVALID_STEMMER_FUNCTION_TYPE");t=e.stemmer}else if("english"===e.language)t=eq;else throw E("MISSING_STEMMER",e.language);if(!1!==e.stopWords){if(r=[],Array.isArray(e.stopWords))r=e.stopWords;else if("function"==typeof e.stopWords)r=e.stopWords(r);else if(e.stopWords)throw E("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");if(!Array.isArray(r))throw E("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");for(let e of r)if("string"!=typeof e)throw E("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY")}let n={tokenize:eZ,language:e.language,stemmer:t,stemmerSkipProperties:new Set(e.stemmerSkipProperties?[e.stemmerSkipProperties].flat():[]),tokenizeSkipProperties:new Set(e.tokenizeSkipProperties?[e.tokenizeSkipProperties].flat():[]),stopWords:r,allowDuplicates:!!e.allowDuplicates,normalizeToken:eX,normalizationCache:new Map};return n.tokenize=eZ.bind(n),n.normalizeToken=eX,n}function e0(e,t,r,n,o){let i=e.validateSchema(t,e.schema);if(i)throw createError("SCHEMA_VALIDATION_FAILURE",i);return isAsyncFunction(e.beforeInsert)||isAsyncFunction(e.afterInsert)||isAsyncFunction(e.index.beforeInsert)||isAsyncFunction(e.index.insert)||isAsyncFunction(e.index.afterInsert)?e7(e,t,r,n,o):function(e,t,r,n,o){let{index:i,docs:s}=e.data,a=e.getDocumentIndexId(t);if("string"!=typeof a)throw createError("DOCUMENT_ID_MUST_BE_STRING",typeof a);let l=getInternalDocumentId(e.internalDocumentIDStore,a);if(!e.documentsStore.store(s,a,l,t))throw createError("DOCUMENT_ALREADY_EXISTS",a);let u=e.documentsStore.count(s);n||runSingleHook(e.beforeInsert,e,a,t);let c=e.index.getSearchableProperties(i),f=e.index.getSearchablePropertiesWithTypes(i),d=e.getDocumentProperties(t,c);for(let[e,t]of Object.entries(d))void 0!==t&&e5(typeof t,f[e],e,t);return function(e,t,r,n,o,i,s,a){for(let s of r){let r=n[s];if(void 0===r)continue;let l=e.index.getSearchablePropertiesWithTypes(e.data.index)[s],u=getInternalDocumentId(e.internalDocumentIDStore,t);e.index.beforeInsert?.(e.data.index,s,t,r,l,i,e.tokenizer,o),e.index.insert(e.index,e.data.index,s,t,u,r,l,i,e.tokenizer,o,a),e.index.afterInsert?.(e.data.index,s,t,r,l,i,e.tokenizer,o)}let l=e.sorter.getSortableProperties(e.data.sorting),u=e.getDocumentProperties(s,l);for(let r of l){let n=u[r];if(void 0===n)continue;let o=e.sorter.getSortablePropertiesWithTypes(e.data.sorting)[r];e.sorter.insert(e.data.sorting,r,t,n,o,i)}}(e,a,c,d,u,r,t,o),n||runSingleHook(e.afterInsert,e,a,t),trackInsertion(e),a}(e,t,r,n,o)}Symbol("orama.insertions"),Symbol("orama.removals"),globalThis.process?.emitWarning;let e1=new Set(["enum","enum[]"]),e9=new Set(["string","number"]);async function e7(e,t,r,n,o){let{index:i,docs:s}=e.data,a=e.getDocumentIndexId(t);if("string"!=typeof a)throw createError("DOCUMENT_ID_MUST_BE_STRING",typeof a);let l=getInternalDocumentId(e.internalDocumentIDStore,a);if(!e.documentsStore.store(s,a,l,t))throw createError("DOCUMENT_ALREADY_EXISTS",a);let u=e.documentsStore.count(s);n||await runSingleHook(e.beforeInsert,e,a,t);let c=e.index.getSearchableProperties(i),f=e.index.getSearchablePropertiesWithTypes(i),d=e.getDocumentProperties(t,c);for(let[e,t]of Object.entries(d))void 0!==t&&e5(typeof t,f[e],e,t);return await e2(e,a,c,d,u,r,t,o),n||await runSingleHook(e.afterInsert,e,a,t),trackInsertion(e),a}function e5(e,t,r,n){if(!(isGeoPointType(t)&&"object"==typeof n&&"number"==typeof n.lon&&"number"==typeof n.lat||isVectorType(t)&&Array.isArray(n)||isArrayType(t)&&Array.isArray(n))&&!(e1.has(t)&&e9.has(e))&&e!==t)throw createError("INVALID_DOCUMENT_PROPERTY",r,t,e)}async function e2(e,t,r,n,o,i,s,a){for(let s of r){let r=n[s];if(void 0===r)continue;let l=e.index.getSearchablePropertiesWithTypes(e.data.index)[s];await e.index.beforeInsert?.(e.data.index,s,t,r,l,i,e.tokenizer,o);let u=e.internalDocumentIDStore.idToInternalId.get(t);await e.index.insert(e.index,e.data.index,s,t,u,r,l,i,e.tokenizer,o,a),await e.index.afterInsert?.(e.data.index,s,t,r,l,i,e.tokenizer,o)}let l=e.sorter.getSortableProperties(e.data.sorting),u=e.getDocumentProperties(s,l);for(let r of l){let n=u[r];if(void 0===n)continue;let o=e.sorter.getSortablePropertiesWithTypes(e.data.sorting)[r];e.sorter.insert(e.data.sorting,r,t,n,o,i)}}async function e8(e,t,r,n){let o=!0,{index:i,docs:s}=e.data,a=e.documentsStore.get(s,t);if(!a)return!1;let l=getInternalDocumentId(e.internalDocumentIDStore,t),u=getDocumentIdFromInternalId(e.internalDocumentIDStore,l),c=e.documentsStore.count(s);n||await runSingleHook(e.beforeRemove,e,u);let f=e.index.getSearchableProperties(i),d=e.index.getSearchablePropertiesWithTypes(i),h=e.getDocumentProperties(a,f);for(let n of f){let i=h[n];if(void 0===i)continue;let s=d[n];await e.index.beforeRemove?.(e.data.index,n,u,i,s,r,e.tokenizer,c),await e.index.remove(e.index,e.data.index,n,t,l,i,s,r,e.tokenizer,c)||(o=!1),await e.index.afterRemove?.(e.data.index,n,u,i,s,r,e.tokenizer,c)}let p=await e.sorter.getSortableProperties(e.data.sorting),g=await e.getDocumentProperties(a,p);for(let r of p)void 0!==g[r]&&e.sorter.remove(e.data.sorting,r,t);return n||await runSingleHook(e.afterRemove,e,u),e.documentsStore.remove(e.data.docs,t,l),trackRemoval(e),o}let e6="fulltext";function e3(e,t){return e[1]-t[1]}function e4(e,t){return t[1]-e[1]}function te(e,t,r){let n={},o=t.map(([e])=>e),i=e.documentsStore.getMultiple(e.data.docs,o),s=Object.keys(r),a=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e of s){let t;if("number"===a[e]){let{ranges:n}=r[e],o=n.length,i=Array.from({length:o});for(let e=0;e<o;e++){let t=n[e];i[e]=[`${t.from}-${t.to}`,0]}t=Object.fromEntries(i)}n[e]={count:0,values:t??{}}}let l=i.length;for(let e=0;e<l;e++){let t=i[e];for(let e of s){let o=e.includes(".")?I(t,e):t[e],i=a[e],s=n[e].values;switch(i){case"number":tt(r[e].ranges,s)(o);break;case"number[]":{let t=new Set,n=tt(r[e].ranges,s,t);for(let e of o)n(e);break}case"boolean":case"enum":case"string":tr(s,i)(o);break;case"boolean[]":case"enum[]":case"string[]":{let e=tr(s,"boolean[]"===i?"boolean":"string",new Set);for(let t of o)e(t);break}default:throw E("FACET_NOT_SUPPORTED",i)}}}for(let e of s){let t=n[e];if(t.count=Object.keys(t.values).length,"string"===a[e]){let n=r[e],o=function(e="desc"){return"asc"===e.toLowerCase()?e3:e4}(n.sort);t.values=Object.fromEntries(Object.entries(t.values).sort(o).slice(n.offset??0,n.limit??10))}}return n}function tt(e,t,r){return n=>{for(let o of e){let e=`${o.from}-${o.to}`;!r?.has(e)&&n>=o.from&&n<=o.to&&(void 0===t[e]?t[e]=1:(t[e]++,r?.add(e)))}}}function tr(e,t,r){let n="boolean"===t?"false":"";return t=>{let o=t?.toString()??n;r?.has(o)||(e[o]=(e[o]??0)+1,r?.add(o))}}let tn={reducer:(e,t,r,n)=>(t[n]=r,t),getInitialValue:e=>Array.from({length:e})},to=["string","number","boolean"];function ti(e,t,r){let n=r.properties,o=n.length,i=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e=0;e<o;e++){let t=n[e];if(void 0===i[t])throw E("UNKNOWN_GROUP_BY_PROPERTY",t);if(!to.includes(i[t]))throw E("INVALID_GROUP_BY_PROPERTY",t,to.join(", "),i[t])}let s=t.map(([t])=>V(e.internalDocumentIDStore,t)),a=e.documentsStore.getMultiple(e.data.docs,s),l=a.length,u=r.maxResult||Number.MAX_SAFE_INTEGER,c=[],f={};for(let e=0;e<o;e++){let t=n[e],r={property:t,perValue:{}},o=new Set;for(let e=0;e<l;e++){let n=I(a[e],t);if(void 0===n)continue;let i="boolean"!=typeof n?n:""+n,s=r.perValue[i]??{indexes:[],count:0};s.count>=u||(s.indexes.push(e),s.count++,r.perValue[i]=s,o.add(n))}c.push(Array.from(o)),f[t]=r}let h=function e(t,r=0){if(r+1===t.length)return t[r].map(e=>[e]);let n=t[r],o=e(t,r+1),i=[];for(let e of n)for(let t of o){let r=[e];d(r,t),i.push(r)}return i}(c),p=h.length,g=[];for(let e=0;e<p;e++){let t=h[e],r=t.length,o={values:[],indexes:[]},i=[];for(let e=0;e<r;e++){let r=t[e],s=n[e];i.push(f[s].perValue["boolean"!=typeof r?r:""+r].indexes),o.values.push(r)}o.indexes=(function(e){if(0===e.length)return[];if(1===e.length)return e[0];for(let t=1;t<e.length;t++)if(e[t].length<e[0].length){let r=e[0];e[0]=e[t],e[t]=r}let t=new Map;for(let r of e[0])t.set(r,1);for(let r=1;r<e.length;r++){let n=0;for(let o of e[r]){let e=t.get(o);e===r&&(t.set(o,e+1),n++)}if(0===n)return[]}return e[0].filter(r=>{let n=t.get(r);return void 0!==n&&t.set(r,0),n===e.length})})(i).sort((e,t)=>e-t),0!==o.indexes.length&&g.push(o)}let m=g.length,y=Array.from({length:m});for(let e=0;e<m;e++){let n=g[e],o=r.reduce||tn,i=n.indexes.map(e=>({id:s[e],score:t[e][1],document:a[e]})),l=o.reducer.bind(null,n.values),u=o.getInitialValue(n.indexes.length),c=i.reduce(l,u);y[e]={values:n.values,result:c}}return y}function ts(e,t,r){let n,o,{term:i,properties:s}=t,a=e.data.index,l=e.caches.propertiesToSearch;if(!l){let t=e.index.getSearchablePropertiesWithTypes(a);l=(l=e.index.getSearchableProperties(a)).filter(e=>t[e].startsWith("string")),e.caches.propertiesToSearch=l}if(s&&"*"!==s){for(let e of s)if(!l.includes(e))throw E("UNKNOWN_INDEX",e,l.join(", "));l=l.filter(e=>s.includes(e))}Object.keys(t.where??{}).length>0&&(n=e.index.searchByWhereClause(a,e.tokenizer,t.where,r));let u=void 0!==t.threshold&&null!==t.threshold?t.threshold:1;if(i||s){let s=e.documentsStore.count(e.data.docs);o=e.index.search(a,i||"",e.tokenizer,r,l,t.exact||!1,t.tolerance||0,t.boost||{},function(e){let t=e??{};return t.k=t.k??ta.k,t.b=t.b??ta.b,t.d=t.d??ta.d,t}(t.relevance),s,n,u)}else o=(n?Array.from(n):Object.keys(e.documentsStore.getAll(e.data.docs))).map(e=>[+e,0]);return o}let ta={k:1.2,b:.75,d:.5};function tl(e,t,r){let n,o=t.vector;if(o&&(!("value"in o)||!("property"in o)))throw E("INVALID_VECTOR_INPUT",Object.keys(o).join(", "));let i=e.data.index.vectorIndexes[o.property],s=i.node.size;if(o?.value.length!==s){if(o?.property===void 0||o?.value.length===void 0)throw E("INVALID_INPUT_VECTOR","undefined",s,"undefined");throw E("INVALID_INPUT_VECTOR",o.property,s,o.value.length)}let a=e.data.index;return Object.keys(t.where??{}).length>0&&(n=e.index.searchByWhereClause(a,e.tokenizer,t.where,r)),i.node.find(o.value,t.similarity??.8,n)}function tu(e){return e[1]}function tc(e,t,r){let n=t.mode??e6;if(n===e6){let n=g();function o(){let o,i=Object.keys(e.data.index.vectorIndexes),s=t.facets&&Object.keys(t.facets).length>0,{limit:a=10,offset:l=0,distinctOn:u,includeVectors:c=!1}=t,f=!0===t.preflight,d=ts(e,t,r);if(t.sortBy)if("function"==typeof t.sortBy){let r=d.map(([e])=>e),n=e.documentsStore.getMultiple(e.data.docs,r).map((e,t)=>[d[t][0],d[t][1],e]);n.sort(t.sortBy),d=n.map(([e,t])=>[e,t])}else d=e.sorter.sortBy(e.data.sorting,d,t.sortBy).map(([t,r])=>[B(e.internalDocumentIDStore,t),r]);else d=d.sort(b);f||(o=u?function(e,t,r,n,o){let i=e.data.docs,s=new Map,a=[],l=new Set,u=t.length,c=0;for(let f=0;f<u;f++){let u=t[f];if(void 0===u)continue;let[d,h]=u;if(l.has(d))continue;let p=e.documentsStore.get(i,d),g=I(p,o);if(!(void 0===g||s.has(g))&&(s.set(g,!0),!(++c<=r)&&(a.push({id:V(e.internalDocumentIDStore,d),score:h,document:p}),l.add(d),c>=r+n)))break}return a}(e,d,l,a,u):tf(e,d,l,a));let h={elapsed:{formatted:"",raw:0},hits:[],count:d.length};return void 0!==o&&(h.hits=o.filter(Boolean),c||O(h,i)),s&&(h.facets=te(e,d,t.facets)),t.groupBy&&(h.groups=ti(e,d,t.groupBy)),h.elapsed=e.formatElapsedTime(g()-n),h}async function i(){e.beforeSearch&&await ee(e.beforeSearch,e,t,r);let n=o();return e.afterSearch&&await Q(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?i():o()}if("vector"===n)return function(e,t,r="english"){let n=g();function o(){let o=tl(e,t,r).sort(b),i=[];t.facets&&Object.keys(t.facets).length>0&&(i=te(e,o,t.facets));let s=t.vector.property,a=t.includeVectors??!1,l=t.limit??10,u=t.offset??0,c=Array.from({length:l});for(let t=0;t<l;t++){let r=o[t+u];if(!r)break;let n=e.data.docs.docs[r[0]];if(n){a||(n[s]=null);let o={id:V(e.internalDocumentIDStore,r[0]),score:r[1],document:n};c[t]=o}}let f=[];t.groupBy&&(f=ti(e,o,t.groupBy));let d=g()-n;return{count:o.length,hits:c.filter(Boolean),elapsed:{raw:Number(d),formatted:p(d)},...i?{facets:i}:{},...f?{groups:f}:{}}}async function i(){e.beforeSearch&&await ee(e.beforeSearch,e,t,r);let n=o();return e.afterSearch&&await Q(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?i():o()}(e,t);if("hybrid"===n)return function(e,t,r){let n=g();function o(){let r,o,i=function(e,t,r){let n=function(e){let t=Math.max.apply(Math,e.map(tu));return e.map(([e,r])=>[e,r/t])}(ts(e,t,r)),o=tl(e,t,r),i=t.hybridWeights;return function(e,t,r,n){var o;let i=Math.max.apply(Math,e.map(tu)),s=Math.max.apply(Math,t.map(tu)),{text:a,vector:l}=n&&n.text&&n.vector?n:(o=0,{text:.5,vector:.5}),u=new Map,c=e.length,f=(e,t)=>e*a+t*l;for(let t=0;t<c;t++){let[r,n]=e[t],o=f(n/i,0);u.set(r,o)}let d=t.length;for(let e=0;e<d;e++){let[r,n]=t[e],o=n/s,i=u.get(r)??0;u.set(r,i+f(0,o))}return[...u].sort((e,t)=>t[1]-e[1])}(n,o,t.term??"",i)}(e,t,void 0);t.facets&&Object.keys(t.facets).length>0&&(r=te(e,i,t.facets)),t.groupBy&&(o=ti(e,i,t.groupBy));let s=tf(e,i,t.offset??0,t.limit??10).filter(Boolean),a=g(),l={count:i.length,elapsed:{raw:Number(a-n),formatted:p(a-n)},hits:s,...r?{facets:r}:{},...o?{groups:o}:{}};return t.includeVectors||O(l,Object.keys(e.data.index.vectorIndexes)),l}async function i(){e.beforeSearch&&await ee(e.beforeSearch,e,t,void 0);let n=o();return e.afterSearch&&await Q(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?i():o()}(e,t);throw E("INVALID_SEARCH_MODE",n)}function tf(e,t,r,n){let o=e.data.docs,i=Array.from({length:n}),s=new Set;for(let a=r;a<n+r;a++){let r=t[a];if(void 0===r)break;let[n,l]=r;if(!s.has(n)){let t=e.documentsStore.get(o,n);i[a]={id:V(e.internalDocumentIDStore,n),score:l,document:t},s.add(n)}}return i}function td(e,t){e.internalDocumentIDStore.load(e,t.internalDocumentIDStore),e.data.index=e.index.load(e.internalDocumentIDStore,t.index),e.data.docs=e.documentsStore.load(e.internalDocumentIDStore,t.docs),e.data.sorting=e.sorter.load(e.internalDocumentIDStore,t.sorting),e.tokenizer.language=t.language}async function th(e,t,r={}){return(await tc(e,{term:t,tolerance:1,...r,boost:{title:2,..."boost"in r?r.boost:void 0}})).hits.map(e=>({type:"page",content:e.document.title,id:e.document.url,url:e.document.url}))}async function tp(e,t,r,o={}){let i={where:(0,n.B)({tag:r,...o.where}),groupBy:{properties:["page_id"],maxResult:8,...o.groupBy}};t.length>0&&(i={...i,term:t,properties:["content","keywords"],...o,where:i.where,groupBy:i.groupBy});let s=await tc(e,i),a=[];for(let t of s.groups??[]){let r=t.values[0],n=await e.documentsStore.get(e.data.docs,r);if(n)for(let e of(a.push({id:r,type:"page",content:n.content,url:n.url}),t.result))"page"!==e.document.type&&a.push({id:e.document.id.toString(),content:e.document.content,type:e.document.type,url:e.document.url})}return a}function tg({from:e="/api/search",initOrama:t=e=>(function({schema:e,sort:t,language:r,components:n,id:o,plugins:i}){for(let t of(n||(n={}),i??[])){if(!("getComponents"in t)||"function"!=typeof t.getComponents)continue;let r=t.getComponents(e);for(let e of Object.keys(r))if(n[e])throw E("PLUGIN_COMPONENT_CONFLICT",e,t.name);n={...n,...r}}o||(o=m());let s=n.tokenizer,a=n.index,l=n.documentsStore,u=n.sorter;if(s=s?s.tokenize?s:eQ(s):eQ({language:r??"english"}),n.tokenizer&&r)throw E("NO_LANGUAGE_WITH_CUSTOM_TOKENIZER");let c={idToInternalId:new Map,internalIdToId:[],save:U,load:L};a||={create:function e(t,r,n,o,i=""){for(let[s,a]of(o||(o={sharedInternalDocumentStore:r,indexes:{},vectorIndexes:{},searchableProperties:[],searchablePropertiesWithTypes:{},frequencies:{},tokenOccurrences:{},avgFieldLength:{},fieldLengths:{}}),Object.entries(n))){let n=`${i}${i?".":""}${s}`;if("object"==typeof a&&!Array.isArray(a)){e(t,r,a,o,n);continue}if(M(a))o.searchableProperties.push(n),o.searchablePropertiesWithTypes[n]=a,o.vectorIndexes[n]={type:"Vector",node:new ec(C(a)),isArray:!1};else{let e=/\[/.test(a);switch(a){case"boolean":case"boolean[]":o.indexes[n]={type:"Bool",node:new eu,isArray:e};break;case"number":case"number[]":o.indexes[n]={type:"AVL",node:new er(0,[]),isArray:e};break;case"string":case"string[]":o.indexes[n]={type:"Radix",node:new es,isArray:e},o.avgFieldLength[n]=0,o.frequencies[n]={},o.tokenOccurrences[n]={},o.fieldLengths[n]={};break;case"enum":case"enum[]":o.indexes[n]={type:"Flat",node:new en,isArray:e};break;case"geopoint":o.indexes[n]={type:"BKD",node:new el,isArray:e};break;default:throw E("INVALID_SCHEMA_TYPE",Array.isArray(a)?"array":a,n)}o.searchableProperties.push(n),o.searchablePropertiesWithTypes[n]=a}}return o},insert:em,remove:eb,insertDocumentScoreParameters:ed,insertTokenScoreParameters:eh,removeDocumentScoreParameters:ep,removeTokenScoreParameters:eg,calculateResultScores:eS,search:eI,searchByWhereClause:ew,getSearchableProperties:eO,getSearchablePropertiesWithTypes:eT,load:eN,save:eD},u||={create:e_,insert:eA,remove:eM,save:eB,load:eL,sortBy:ez,getSortableProperties:eC,getSortablePropertiesWithTypes:eU},l||={create:F,get:W,getMultiple:j,getAll:$,store:J,remove:G,count:Y,load:H,save:K};var f=n;let d={formatElapsedTime:k,getDocumentIndexId:P,getDocumentProperties:S,validateSchema:function e(t,r){for(let[n,o]of Object.entries(r)){let r=t[n];if(void 0!==r&&("geopoint"!==o||"object"!=typeof r||"number"!=typeof r.lon||"number"!=typeof r.lat)&&("enum"!==o||"string"!=typeof r&&"number"!=typeof r)){if("enum[]"===o&&Array.isArray(r)){let e=r.length;for(let t=0;t<e;t++)if("string"!=typeof r[t]&&"number"!=typeof r[t])return n+"."+t;continue}if(M(o)){let e=C(o);if(!Array.isArray(r)||r.length!==e)throw E("INVALID_INPUT_VECTOR",n,e,r.length);continue}if(z(o)){if(!Array.isArray(r))return n;let e=R[o],t=r.length;for(let o=0;o<t;o++)if(typeof r[o]!==e)return n+"."+o;continue}if("object"==typeof o){if(!r||"object"!=typeof r)return n;let t=e(r,o);if(t)return n+"."+t;continue}if(typeof r!==o)return n}}}};for(let e of Z)if(f[e]){if("function"!=typeof f[e])throw E("COMPONENT_MUST_BE_FUNCTION",e)}else f[e]=d[e];for(let e of Object.keys(f))if(!X.includes(e)&&!Z.includes(e))throw E("UNSUPPORTED_COMPONENT",e);let{getDocumentProperties:h,getDocumentIndexId:p,validateSchema:g,formatElapsedTime:y}=n,b={data:{},caches:{},schema:e,tokenizer:s,index:a,sorter:u,documentsStore:l,internalDocumentIDStore:c,getDocumentProperties:h,getDocumentIndexId:p,validateSchema:g,beforeInsert:[],afterInsert:[],beforeRemove:[],afterRemove:[],beforeUpdate:[],afterUpdate:[],beforeSearch:[],afterSearch:[],beforeInsertMultiple:[],afterInsertMultiple:[],beforeRemoveMultiple:[],afterRemoveMultiple:[],afterUpdateMultiple:[],beforeUpdateMultiple:[],afterCreate:[],formatElapsedTime:y,id:o,plugins:i,version:"{{VERSION}}"};for(let r of(b.data={index:b.index.create(b,c,e),docs:b.documentsStore.create(b,c),sorting:b.sorter.create(b,c,e,t)},q))b[r]=(b[r]??[]).concat(function(e,t){let r=[],n=e.plugins?.length;if(!n)return r;for(let o=0;o<n;o++)try{let n=e.plugins[o];"function"==typeof n[t]&&r.push(n[t])}catch(e){throw console.error("Caught error in getAllPluginsByHook:",e),E("PLUGIN_CRASHED")}return r}(b,r));let I=b.afterCreate;return I&&function(e,t){if(e.some(T))return(async()=>{for(let r of e)await r(t)})();for(let r of e)r(t)}(I,b),b})({schema:{_:"string"},language:e})}){let r=new Map,n=async function(){let n=await fetch(e);if(!n.ok)throw Error(`failed to fetch exported search indexes from ${e}, make sure the search database is exported and available for client.`);let o=await n.json();if("i18n"===o.type)for(let[e,n]of Object.entries(o.data)){let o=await t(e);td(o,n),r.set(e,{type:n.type,db:o})}else{let e=await t();td(e,o),r.set("",{type:o.type,db:e})}}();return{async search(e,t,o){await n;let i=r.get(t??"");return i?"simple"===i.type?th(i,e):tp(i.db,e,o):[]}}}r(22317)},25862:(e,t,r)=>{r.d(t,{B:()=>function e(t,r=!1){for(let n of Object.keys(t))void 0===t[n]&&delete t[n],r&&"object"==typeof t[n]&&null!==t[n]?e(t[n],r):r&&Array.isArray(t[n])&&t[n].forEach(t=>e(t,r));return t}})}};