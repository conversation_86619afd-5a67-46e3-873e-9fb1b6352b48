'use client'

import { useState, useEffect, useCallback } from 'react'
import { create, search } from '@orama/orama'
import { createTokenizer } from '@orama/tokenizers/mandarin'
import { Search, X, FileText, BookOpen } from 'lucide-react'
import { useLocale } from 'next-intl'

interface SearchResult {
  id: string
  url: string
  title: string
  description: string
  content: string
  type: 'docs' | 'blog'
  score: number
}

interface ClientSearchProps {
  placeholder?: string
  className?: string
}

export function ClientSearch({ placeholder = 'Search...', className = '' }: ClientSearchProps) {
  const locale = useLocale()
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [searchIndex, setSearchIndex] = useState<any>(null)
  const [indexLoaded, setIndexLoaded] = useState(false)

  // 加载搜索索引
  useEffect(() => {
    const loadSearchIndex = async () => {
      try {
        const response = await fetch('/search-index.json')
        if (response.ok) {
          const indexes = await response.json()
          const currentIndex = indexes[locale] || indexes['en']
          
          if (currentIndex) {
            // 重建Orama数据库
            const config = {
              schema: currentIndex.schema,
              components: {} as Record<string, unknown>
            }

            // 为中文添加分词器
            if (locale === 'cn') {
              config.components.tokenizer = createTokenizer()
            }

            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const db = create(config as any)
            
            // 恢复数据
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ;(db as any).data = currentIndex.data
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ;(db as any).index = currentIndex.index
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ;(db as any).sorting = currentIndex.sorting
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ;(db as any).facets = currentIndex.facets
            
            setSearchIndex(db)
            setIndexLoaded(true)
            console.log(`✅ 搜索索引加载完成 (${locale})`)
          } else {
            console.warn(`未找到 ${locale} 语言的搜索索引`)
          }
        } else {
          console.error('无法加载搜索索引文件')
        }
      } catch (error) {
        console.error('加载搜索索引失败:', error)
      }
    }

    loadSearchIndex()
  }, [locale])

  // 执行搜索
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchIndex || !searchQuery.trim()) {
      setResults([])
      return
    }

    setIsLoading(true)
    
    try {
      const searchResults = await search(searchIndex, {
        term: searchQuery,
        limit: 10,
        threshold: 0.6,
        boost: {
          title: 2,
          description: 1.5,
          content: 1
        }
      })

      const formattedResults: SearchResult[] = searchResults.hits.map((hit: { id: string; document: Record<string, unknown>; score: number }) => ({
        id: hit.id,
        url: hit.document.url as string,
        title: hit.document.title as string,
        description: hit.document.description as string,
        content: hit.document.content as string,
        type: hit.document.type as 'docs' | 'blog',
        score: hit.score
      }))

      setResults(formattedResults)
    } catch (error) {
      console.error('搜索失败:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }, [searchIndex])

  // 防抖搜索
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query) {
        performSearch(query)
      } else {
        setResults([])
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query, performSearch])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsOpen(true)
      } else if (e.key === 'Escape') {
        setIsOpen(false)
        setQuery('')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleResultClick = (result: SearchResult) => {
    setIsOpen(false)
    setQuery('')
    // 导航到结果页面
    window.location.href = `/${locale}${result.url}`
  }

  const getTypeIcon = (type: string) => {
    return type === 'blog' ? <BookOpen className="h-4 w-4" /> : <FileText className="h-4 w-4" />
  }

  const getTypeLabel = (type: string) => {
    return type === 'blog' ? (locale === 'cn' ? '博客' : 'Blog') : (locale === 'cn' ? '文档' : 'Docs')
  }

  if (!indexLoaded) {
    return (
      <button
        className={`flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 ${className}`}
        disabled
      >
        <Search className="h-4 w-4" />
        <span>Loading search...</span>
      </button>
    )
  }

  return (
    <>
      {/* 搜索触发按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className={`flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 transition-colors ${className}`}
      >
        <Search className="h-4 w-4" />
        <span>{placeholder}</span>
        <kbd className="ml-auto hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 px-1.5 font-mono text-xs text-gray-600 dark:text-gray-300">
          <span className="text-xs">⌘</span>K
        </kbd>
      </button>

      {/* 搜索模态框 */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm" onClick={() => setIsOpen(false)}>
          <div className="fixed left-1/2 top-1/2 w-full max-w-2xl -translate-x-1/2 -translate-y-1/2 transform">
            <div className="mx-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-2xl">
              {/* 搜索输入框 */}
              <div className="flex items-center border-b border-gray-200 dark:border-gray-700 px-4">
                <Search className="h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder={placeholder}
                  className="flex-1 bg-transparent px-4 py-4 text-sm outline-none placeholder:text-gray-400"
                  autoFocus
                />
                <button
                  onClick={() => setIsOpen(false)}
                  className="rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {/* 搜索结果 */}
              <div className="max-h-96 overflow-y-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-sm text-gray-500">搜索中...</div>
                  </div>
                ) : results.length > 0 ? (
                  <div className="py-2">
                    {results.map((result) => (
                      <button
                        key={result.id}
                        onClick={() => handleResultClick(result)}
                        className="flex w-full items-start gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50"
                      >
                        <div className="mt-1 flex-shrink-0 text-gray-400">
                          {getTypeIcon(result.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                              {result.title}
                            </h3>
                            <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded">
                              {getTypeLabel(result.type)}
                            </span>
                          </div>
                          {result.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                              {result.description}
                            </p>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                ) : query ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-sm text-gray-500">
                      {locale === 'cn' ? '未找到结果' : 'No results found'}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-sm text-gray-500">
                      {locale === 'cn' ? '输入关键词开始搜索' : 'Type to search'}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
