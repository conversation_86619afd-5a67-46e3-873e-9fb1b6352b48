(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[256],{244:(e,t,r)=>{"use strict";function n(e,t,r){let{includePage:n=!0,includeSeparator:i=!1,includeRoot:o}=r,l=[];return t.forEach((e,r)=>{if("separator"===e.type&&i&&l.push({name:e.name}),"folder"===e.type){let n=t.at(r+1);if(n&&e.index===n)return;if(e.root){l=[];return}l.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&l.push({name:e.name,url:e.url})}),o&&l.unshift({name:e.name,url:"object"==typeof o?o.url:void 0}),l}r.d(t,{Pp:()=>n,oe:()=>function e(t,r){let n;for(let i of(r.endsWith("/")&&(r=r.slice(0,-1)),t)){if("separator"===i.type&&(n=i),"folder"===i.type){if(i.index?.url===r){let e=[];return n&&e.push(n),e.push(i,i.index),e}let t=e(i.children,r);if(t)return t.unshift(i),n&&t.unshift(n),t}if("page"===i.type&&i.url===r){let e=[];return n&&e.push(n),e.push(i),e}}return null}}),r(9189),r(2115)},263:(e,t,r)=>{"use strict";r.d(t,{G:()=>d,c:()=>u});var n=r(5155),i=r(2115),o=r(344),l=r(5547),a=r(3259);let s=(0,o.q6)("SidebarContext");function u(){return s.use()}function d(e){let{children:t}=e,r=(0,i.useRef)(!0),[u,d]=(0,i.useState)(!1),[c,f]=(0,i.useState)(!1),p=(0,o.a8)();return(0,a.T)(p,()=>{r.current&&d(!1),r.current=!0}),(0,n.jsx)(s.Provider,{value:(0,i.useMemo)(()=>({open:u,setOpen:d,collapsed:c,setCollapsed:f,closeOnRedirect:r}),[u,c]),children:(0,n.jsx)(l.GB,{open:u,onOpenChange:d,children:t})})}},901:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext(null)},1193:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:i,quality:o}=e,l=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+l+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>u,N:()=>d});var n=r(2115),i=(e,t,r,n,i,o,l,a)=>{let s=document.documentElement,u=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&o?i.map(e=>o[e]||e):i;r?(s.classList.remove(...n),s.classList.add(o&&o[t]?o[t]:t)):s.setAttribute(e,t)}),r=t,a&&u.includes(r)&&(s.style.colorScheme=r)}if(n)d(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},o=["light","dark"],l="(prefers-color-scheme: dark)",a=n.createContext(void 0),s={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(a))?e:s},d=e=>n.useContext(a)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),c=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:i=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:d=c,defaultTheme:f=i?"system":"light",attribute:y="data-theme",value:v,children:b,nonce:_,scriptProps:w}=e,[x,j]=n.useState(()=>m(u,f)),[S,C]=n.useState(()=>"system"===x?g():x),O=v?Object.values(v):d,P=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&i&&(t=g());let n=v?v[t]:t,l=r?h(_):null,a=document.documentElement,u=e=>{"class"===e?(a.classList.remove(...O),n&&a.classList.add(n)):e.startsWith("data-")&&(n?a.setAttribute(e,n):a.removeAttribute(e))};if(Array.isArray(y)?y.forEach(u):u(y),s){let e=o.includes(f)?f:null,r=o.includes(t)?t:e;a.style.colorScheme=r}null==l||l()},[_]),E=n.useCallback(e=>{let t="function"==typeof e?e(x):e;j(t);try{localStorage.setItem(u,t)}catch(e){}},[x]),M=n.useCallback(e=>{C(g(e)),"system"===x&&i&&!t&&P("system")},[x,t]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?j(e.newValue):E(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[E]),n.useEffect(()=>{P(null!=t?t:x)},[t,x]);let k=n.useMemo(()=>({theme:x,setTheme:E,forcedTheme:t,resolvedTheme:"system"===x?S:x,themes:i?[...d,"system"]:d,systemTheme:i?S:void 0}),[x,E,t,S,i,d]);return n.createElement(a.Provider,{value:k},n.createElement(p,{forcedTheme:t,storageKey:u,attribute:y,enableSystem:i,enableColorScheme:s,defaultTheme:f,value:v,themes:d,nonce:_,scriptProps:w}),b)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:o,enableSystem:l,enableColorScheme:a,defaultTheme:s,value:u,themes:d,nonce:c,scriptProps:f}=e,p=JSON.stringify([o,r,s,t,d,u,l,a]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(i.toString(),")(").concat(p,")")}})}),m=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return a}});let n=r(8229),i=r(8883),o=r(3063),l=n._(r(1193));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=o.Image},1871:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},2374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext({})},2714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return o}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function i(e){return["async","defer","noModule"].includes(e)}function o(e,t){for(let[o,l]of Object.entries(t)){if(!t.hasOwnProperty(o)||n.includes(o)||void 0===l)continue;let a=r[o]||o.toLowerCase();"SCRIPT"===e.tagName&&i(a)?e[a]=!!l:e.setAttribute(a,String(l)),(!1===l||"SCRIPT"===e.tagName&&i(a)&&(!l||"false"===l))&&(e.setAttribute(a,""),e.removeAttribute(a))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let n=r(8229),i=r(6966),o=r(5155),l=i._(r(2115)),a=n._(r(7650)),s=n._(r(5564)),u=r(8883),d=r(5840),c=r(6752);r(3230);let f=r(901),p=n._(r(1193)),m=r(6654),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,r,n,i,o,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}let v=(0,l.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:a,width:s,decoding:u,className:d,style:c,fetchPriority:f,placeholder:p,loading:h,unoptimized:v,fill:b,onLoadRef:_,onLoadingCompleteRef:w,setBlurComplete:x,setShowAltText:j,sizesInput:S,onLoad:C,onError:O,...P}=e,E=(0,l.useCallback)(e=>{e&&(O&&(e.src=e.src),e.complete&&g(e,p,_,w,x,v,S))},[r,p,_,w,x,O,v,S]),M=(0,m.useMergedRef)(t,E);return(0,o.jsx)("img",{...P,...y(f),loading:h,width:s,height:a,decoding:u,"data-nimg":b?"fill":"1",className:d,style:c,sizes:i,srcSet:n,src:r,ref:M,onLoad:e=>{g(e.currentTarget,p,_,w,x,v,S)},onError:e=>{j(!0),"empty"!==p&&x(!0),O&&O(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):(0,o.jsx)(s.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let _=(0,l.forwardRef)((e,t)=>{let r=(0,l.useContext)(f.RouterContext),n=(0,l.useContext)(c.ImageConfigContext),i=(0,l.useMemo)(()=>{var e;let t=h||n||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:o}},[n]),{onLoad:a,onLoadingComplete:s}=e,m=(0,l.useRef)(a);(0,l.useEffect)(()=>{m.current=a},[a]);let g=(0,l.useRef)(s);(0,l.useEffect)(()=>{g.current=s},[s]);let[y,_]=(0,l.useState)(!1),[w,x]=(0,l.useState)(!1),{props:j,meta:S}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:y,showAltText:w});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v,{...j,unoptimized:S.unoptimized,placeholder:S.placeholder,fill:S.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:_,setShowAltText:x,sizesInput:e.sizes,ref:t}),S.priority?(0,o.jsx)(b,{isAppRouter:!r,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3259:(e,t,r)=>{"use strict";r.d(t,{T:()=>n.T});var n=r(5936);r(9189)},4315:(e,t,r)=>{"use strict";r.d(t,{FX:()=>l,jH:()=>a});var n=r(2115),i=r(5155),o=n.createContext(void 0),l=e=>{let{dir:t,children:r}=e;return(0,i.jsx)(o.Provider,{value:t,children:r})};function a(e){let t=n.useContext(o);return e||t||"ltr"}},5029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(2115),i=n.useLayoutEffect,o=n.useEffect;function l(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let i=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(i,e))}}return i(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:l}=e,a=n?40*n:t,s=i?40*i:r,u=a&&s?"viewBox='0 0 "+a+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5547:(e,t,r)=>{"use strict";r.d(t,{GB:()=>s,Pg:()=>d,x2:()=>u}),r(9189);var n=r(2115),i=r(1114),o=r(5155),l=(0,n.createContext)(null);function a(){let e=(0,n.useContext)(l);if(!e)throw Error("Missing sidebar provider");return e}function s(e){let[t,r]=void 0===e.open?(0,n.useState)(!1):[e.open,e.onOpenChange];return(0,o.jsx)(l.Provider,{value:(0,n.useMemo)(()=>({open:t,setOpen:null!=r?r:()=>void 0}),[t,r]),children:e.children})}function u(e){let{as:t,...r}=e,{open:n,setOpen:i}=a();return(0,o.jsx)(null!=t?t:"button",{"aria-label":"Toggle Sidebar","data-open":n,onClick:()=>{i(!n)},...r})}function d(e){let{as:t,blockScrollingWidth:r,removeScrollOn:l=r?"(width < ".concat(r,"px)"):void 0,...s}=e,{open:u}=a(),[d,c]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{if(!l)return;let e=window.matchMedia(l),t=()=>{c(e.matches)};return t(),e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}},[l]),(0,o.jsx)(i.A,{as:null!=t?t:"aside","data-open":u,enabled:!!(d&&u),...s,children:s.children})}},5564:(e,t,r)=>{"use strict";var n=r(9509);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return f}});let i=r(8229),o=r(6966),l=r(5155),a=o._(r(2115)),s=i._(r(5029)),u=r(2464),d=r(2830),c=r(7544);function f(e){void 0===e&&(e=!1);let t=[(0,l.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,l.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(3230);let m=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let o=!0,l=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){l=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!l)&&r.has(e)?o=!1:(r.add(e),n[t]=r)}}}return o}}()).reverse().map((e,t)=>{let i=e.key||t;if(n.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:i})})}let g=function(e){let{children:t}=e,r=(0,a.useContext)(u.AmpStateContext),n=(0,a.useContext)(d.HeadManagerContext);return(0,l.jsx)(s.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5644:(e,t,r)=>{"use strict";r.d(t,{RootProvider:()=>w});var n=r(5155),i=r(1362),o=r(2115),l=r(4315),a=r(263),s=r(8169),u=r(8265),d=r(9697),c=r(344);let f=(0,o.lazy)(()=>Promise.all([r.e(93),r.e(920),r.e(53)]).then(r.bind(r,477)));function p(e){let{children:t,dir:r="ltr",theme:o={},search:u,i18n:d}=e,c=t;return(null==u?void 0:u.enabled)!==!1&&(c=(0,n.jsx)(s.YL,{SearchDialog:f,...u,children:c})),(null==o?void 0:o.enabled)!==!1&&(c=(0,n.jsx)(i.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,...o,children:c})),d&&(c=(0,n.jsx)(m,{...d,children:c})),(0,n.jsx)(l.FX,{dir:r,children:(0,n.jsx)(a.G,{children:c})})}function m(e){let{locales:t=[],locale:r,onLocaleChange:i,...l}=e,a=(0,c.rd)(),s=(0,c.a8)(),f=(0,u.J)(e=>{if(i)return i(e);let t=s.split("/").filter(e=>e.length>0);t[0]!==r?t.unshift(e):t[0]=e,a.push("/".concat(t.join("/"))),a.refresh()});return(0,n.jsx)(d.gJ.Provider,{value:(0,o.useMemo)(()=>({locale:r,locales:t,text:{...d.Cr,...l.translations},onChange:f}),[r,t,f,l.translations]),children:l.children})}var h=r(9600);r(9189);var g=r(5695),y=r(6874),v=r(1469),b=r.n(v);function _(e){let{children:t}=e;return(0,n.jsx)(h.Uy,{usePathname:g.usePathname,useRouter:g.useRouter,useParams:g.useParams,Link:y,Image:b(),children:t})}function w(e){return(0,n.jsx)(_,{children:(0,n.jsx)(p,{...e,children:e.children})})}r(8693),r(1339)},5840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},5936:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n=r(2115);function i(e,t,r=function e(t,r){if(Array.isArray(t)&&Array.isArray(r))return r.length!==t.length||t.some((t,n)=>e(t,r[n]));if("object"==typeof t&&t&&"object"==typeof r&&r){let n=Object.keys(t),i=Object.keys(r);return n.length!==i.length||n.some(n=>e(t[n],r[n]))}return t!==r}){let[o,l]=(0,n.useState)(e);r(o,e)&&(t(e,o),l(e))}},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(6453),i=r(5155);function o(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,i.jsx)(n.Dk,{locale:t,...r})}},6752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let n=r(8229)._(r(2115)),i=r(5840),o=n.default.createContext(i.imageConfigDefault)},7544:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},8169:(e,t,r)=>{"use strict";r.d(t,{$A:()=>l,YL:()=>s});var n=r(5155),i=r(2115);let o=(0,r(344).q6)("SearchContext",{enabled:!1,hotKey:[],setOpenSearch:()=>void 0});function l(){return o.use()}function a(){let[e,t]=(0,i.useState)("⌘");return(0,i.useEffect)(()=>{window.navigator.userAgent.includes("Windows")&&t("Ctrl")},[]),e}function s(e){let{SearchDialog:t,children:r,preload:l=!0,options:s,hotKey:u=[{key:e=>e.metaKey||e.ctrlKey,display:(0,n.jsx)(a,{})},{key:"k",display:"K"}],links:d}=e,[c,f]=(0,i.useState)(!l&&void 0);return(0,i.useEffect)(()=>{let e=e=>{u.every(t=>"string"==typeof t.key?e.key===t.key:t.key(e))&&(f(!0),e.preventDefault())};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}},[u]),(0,n.jsxs)(o.Provider,{value:(0,i.useMemo)(()=>({enabled:!0,hotKey:u,setOpenSearch:f}),[u]),children:[void 0!==c&&(0,n.jsx)(t,{open:c,onOpenChange:f,links:d,...s}),r]})}},8265:(e,t,r)=>{"use strict";r.d(t,{J:()=>i}),r(9189);var n=r(2115);function i(e){let t=(0,n.useRef)(e);return t.current=e,(0,n.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return t.current(...r)},[])}},8693:(e,t,r)=>{"use strict";r.d(t,{L:()=>d,TreeContextProvider:()=>u,t:()=>c});var n=r(5155),i=r(344),o=r(2115),l=r(244);let a=(0,i.q6)("TreeContext"),s=(0,i.q6)("PathContext",[]);function u(e){var t,r,u;let d=(0,o.useRef)(0),c=(0,i.a8)(),f=(0,o.useMemo)(()=>e.tree,[null!=(t=e.tree.$id)?t:e.tree]),p=(0,o.useMemo)(()=>{var e;return null!=(e=(0,l.oe)(f.children,c))?e:[]},[f,c]),m=null!=(r=p.findLast(e=>"folder"===e.type&&e.root))?r:f;return null!=m.$id||(m.$id=String(d.current++)),(0,n.jsx)(a.Provider,{value:(0,o.useMemo)(()=>({root:m}),[m]),children:(0,n.jsx)(s.Provider,{value:p,children:e.children})})}function d(){return s.use()}function c(){return a.use("You must wrap this component under <DocsLayout />")}},8883:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(3230);let n=r(5100),i=r(5840),o=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:y,quality:v,width:b,height:_,fill:w=!1,style:x,overrideSrc:j,onLoad:S,onLoadingComplete:C,placeholder:O="empty",blurDataURL:P,fetchPriority:E,decoding:M="async",layout:k,objectFit:I,objectPosition:T,lazyBoundary:A,lazyRoot:R,...L}=e,{imgConf:z,showAltText:N,blurComplete:D,defaultLoader:q}=t,F=z||i.imageConfigDefault;if("allSizes"in F)u=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);u={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===q)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let H=L.loader||q;delete L.loader,delete L.srcSet;let G="__next_img_default"in H;if(G){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=H;H=t=>{let{config:r,...n}=t;return e(n)}}if(k){"fill"===k&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!p&&(p=t)}let U="",W=a(b),B=a(_);if((s=f)&&"object"==typeof s&&(l(s)||void 0!==s.src)){let e=l(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,P=P||e.blurDataURL,U=e.src,!w)if(W||B){if(W&&!B){let t=W/e.width;B=Math.round(e.height*t)}else if(!W&&B){let t=B/e.height;W=Math.round(e.width*t)}}else W=e.width,B=e.height}let J=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:U)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,J=!1),u.unoptimized&&(m=!0),G&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let K=a(v),V=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:I,objectPosition:T}:{},N?{}:{color:"transparent"},x),X=D||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:W,heightInt:B,blurWidth:d,blurHeight:c,blurDataURL:P||"",objectFit:V.objectFit})+'")':'url("'+O+'")',$=o.includes(V.objectFit)?"fill"===V.objectFit?"100% 100%":"cover":V.objectFit,Y=X?{backgroundSize:$,backgroundPosition:V.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},Z=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:l,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,l),d=s.length-1;return{sizes:l||"w"!==u?l:"100vw",srcSet:s.map((e,n)=>a({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:a({config:t,src:r,quality:o,width:s[d]})}}({config:u,src:f,unoptimized:m,width:W,quality:K,sizes:p,loader:H});return{props:{...L,loading:J?"lazy":g,fetchPriority:E,width:W,height:B,decoding:M,className:y,style:{...V,...Y},sizes:Z.sizes,srcSet:Z.srcSet,src:j||Z.src},meta:{unoptimized:m,priority:h,placeholder:O,fill:w}}}},9189:(e,t,r)=>{"use strict"},9243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return v},handleClientScriptLoad:function(){return h},initScriptLoader:function(){return g}});let n=r(8229),i=r(6966),o=r(5155),l=n._(r(7650)),a=i._(r(2115)),s=r(2830),u=r(2714),d=r(2374),c=new Map,f=new Set,p=e=>{if(l.default.preinit)return void e.forEach(e=>{l.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},m=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:o,children:l="",strategy:a="afterInteractive",onError:s,stylesheets:d}=e,m=r||t;if(m&&f.has(m))return;if(c.has(t)){f.add(m),c.get(t).then(n,s);return}let h=()=>{i&&i(),f.add(m)},g=document.createElement("script"),y=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),n&&n.call(this,t),h()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});o?(g.innerHTML=o.__html||"",h()):l?(g.textContent="string"==typeof l?l:Array.isArray(l)?l.join(""):"",h()):t&&(g.src=t,c.set(t,y)),(0,u.setAttributesFromProps)(g,e),"worker"===a&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",a),d&&p(d),document.body.appendChild(g)};function h(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>m(e))}):m(e)}function g(e){e.forEach(h),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function y(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:u="afterInteractive",onError:c,stylesheets:p,...h}=e,{updateScripts:g,scripts:y,getIsSsr:v,appDir:b,nonce:_}=(0,a.useContext)(s.HeadManagerContext),w=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=t||r;w.current||(i&&e&&f.has(e)&&i(),w.current=!0)},[i,t,r]);let x=(0,a.useRef)(!1);if((0,a.useEffect)(()=>{if(!x.current){if("afterInteractive"===u)m(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>m(e))}));x.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(y[u]=(y[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:c,...h}]),g(y)):v&&v()?f.add(t||r):v&&!v()&&m(e)),b){if(p&&p.forEach(e=>{l.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...h,id:t}])+")"}});else return l.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin}),(0,o.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...h,id:t}])+")"}});"afterInteractive"===u&&r&&l.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let v=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);