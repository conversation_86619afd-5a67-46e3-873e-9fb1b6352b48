(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[462],{7971:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>T});var s=a(5155),r=a(1362),n=a(2115),i=a(3237),l=a(3786),o=a(7652),d=a(5585),c=a(9708),u=a(2085),x=a(2596),h=a(9688);function m(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,h.QP)((0,x.$)(t))}let g=(0,u.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function f(e){let{className:t,variant:a,size:r,asChild:n=!1,...i}=e,l=n?c.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:m(g({variant:a,size:r,className:t})),...i})}var p=a(981);let v=(0,a(9984).A)({locales:["en","cn"],defaultLocale:"en"}),{Link:b,redirect:y,usePathname:k,useRouter:j,getPathname:w}=(0,p.A)(v),N=["titleLowlantency","titleMultimodal","titleEdgeCloud"],z={visible:{y:0,opacity:1},hidden:e=>({y:e>0?-150:150,opacity:0})},_=e=>{let t={origin:{x:e.clientX/window.innerWidth,y:(e.clientY+50)/window.innerHeight},scalar:.6};function a(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,d.A)({...t,...a,particleCount:Math.floor(88*e)})}a(.25,{spread:20,startVelocity:20}),a(.2,{spread:35,startVelocity:15}),a(.35,{spread:30,decay:.91,scalar:.4,startVelocity:15}),a(.1,{spread:40,startVelocity:10,decay:.92,scalar:.8}),a(.1,{spread:40,startVelocity:10})};function E(e){let{className:t}=e,a=(0,o.c3)("homePage"),[r,d]=n.useState(0);return n.useEffect(()=>{let e=setTimeout(()=>{r===N.length-1?d(0):d(r+1)},2e3);return()=>clearTimeout(e)},[r]),(0,s.jsx)("div",{className:m("text-foreground w-full",t),children:(0,s.jsx)("div",{className:"container mx-auto",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center gap-8 pt-4 pb-20 lg:pt-8 lg:pb-60",children:[(0,s.jsx)("div",{children:(0,s.jsx)(f,{variant:"secondary",size:"sm",className:"gap-2 bg-blue-600/[0.05] text-blue-600 transition-all duration-600 hover:scale-105 hover:bg-blue-600/[0.08] hover:text-blue-500 py-7 sm:py-0",asChild:!0,onClick:e=>_(e),children:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:["\uD83C\uDF89"," ",(0,s.jsx)(b,{href:"https://github.com/ten-framework/ten-vad",className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2",children:"TEN VAD"}),(0,s.jsx)("span",{className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base",children:"and"}),(0,s.jsx)(b,{href:"https://github.com/ten-framework/ten-turn-detection",className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base hover:underline underline-offset-2",children:"TEN Turn Detection"}),(0,s.jsx)("span",{className:"font-medium text-blue-500 dark:text-blue-300 text-sm sm:text-base",children:"are now part of the TEN open-source ecosystem!"})]})})}),(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsxs)("h1",{className:"font-regular text-center text-5xl tracking-tighter md:text-7xl",children:[(0,s.jsx)("span",{className:"text-spektr-cyan-50 font-medium",children:a("titlePrefix")}),(0,s.jsxs)("span",{className:"relative flex w-full justify-center overflow-hidden text-center leading-tight md:leading-normal",children:["\xa0",N.map((e,t)=>(0,s.jsx)(i.P.span,{className:"absolute font-bold",initial:"hidden",animate:r===t?"visible":"hidden",variants:z,custom:r>t?1:-1,transition:{type:"spring",stiffness:35,duration:.5},children:a(e)},t))]}),(0,s.jsx)("span",{className:"text-spektr-cyan-50 font-medium",children:a("titleSuffix")})]}),(0,s.jsx)("p",{className:"text-muted-foreground max-w-2xl text-center text-lg leading-relaxed font-medium tracking-tight md:text-xl dark:text-gray-300",children:a("heroDescription")})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsx)(f,{size:"lg",className:"gap-4",asChild:!0,children:(0,s.jsxs)(b,{href:"https://agent.theten.ai",target:"_blank",children:[a("heroBtnTryTenAgent"),(0,s.jsx)(l.A,{className:"size-4"})]})}),(0,s.jsx)(f,{size:"lg",className:"gap-4",variant:"outline",asChild:!0,children:(0,s.jsxs)(b,{href:"https://huggingface.co/spaces/TEN-framework/ten-agent-demo",target:"_blank",children:[a("huggingFaceSpace"),(0,s.jsx)(l.A,{className:"size-4"})]})})]}),(0,s.jsxs)("p",{className:"text-muted-foreground/100 max-w-2xl text-center text-sm leading-relaxed font-normal tracking-tight md:text-base dark:text-gray-300",children:[a("supportedBy")," ",(0,s.jsx)(b,{href:"https://www.agora.io/en/",target:"_blank",className:"text-spektr-cyan-100 underline decoration-gray-300 underline-offset-5 hover:text-[] hover:decoration-[#13C2FF]",children:"Agora"})]})]})})})}let C=()=>{let{resolvedTheme:e}=(0,r.D)(),[t,a]=(0,n.useState)(!1),[i,l]=(0,n.useState)(!1),o=(0,n.useRef)(null);return((0,n.useEffect)(()=>{a(!0)},[]),(0,n.useEffect)(()=>{o.current&&(l(!1),o.current.currentTime=0,o.current.load(),o.current.play())},[e]),t)?(0,s.jsxs)("video",{ref:o,autoPlay:!0,loop:!0,muted:!0,playsInline:!0,onLoadedData:()=>l(!0),className:"absolute inset-0 z-0 h-full w-full object-cover transition-opacity duration-700 ".concat(i?"opacity-37 dark:opacity-57":"opacity-0"),children:[(0,s.jsx)("source",{src:"dark"===e?"https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg-dark.mp4":"https://ten-framework-assets.s3.us-east-1.amazonaws.com/bg2.mp4",type:"video/mp4"}),"Your browser does not support the video tag."]}):null};function T(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"relative flex h-[calc(100dvh-56px)] flex-1 flex-col justify-center overflow-hidden text-center",children:[(0,s.jsx)(C,{}),(0,s.jsx)(E,{className:"relative z-10"})]})})}},8984:(e,t,a)=>{Promise.resolve().then(a.bind(a,7971))}},e=>{var t=t=>e(e.s=t);e.O(0,[93,453,244,828,441,684,358],()=>t(8984)),_N_E=e.O()}]);