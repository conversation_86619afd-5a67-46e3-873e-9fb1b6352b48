"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[53],{477:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ev});var r,o=n(5155),a=n(5936);n(9189);var l=n(2115),i=n(3259),s=n(9697),u=n(3536);let d=(0,u.A)("text",[["path",{d:"M15 18H3",key:"olowqp"}],["path",{d:"M17 6H3",key:"16j9eg"}],["path",{d:"M21 12H3",key:"2avoz0"}]]),c=(0,u.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),f=(0,u.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),p=(0,u.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var m=n(7543),v=n(9688),g=n(263),h=n(7936),y=n(5185),x=n(6101),b=n(6081),N=n(1285),w=n(5845),j=n(9178),C=n(7900),k=(n(4378),n(8905)),A=n(3655),E=n(2293),O=n(1114),R=n(8168),D=n(9708),M="Dialog",[I,_]=(0,b.A)(M),[P,T]=I(M),S=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:i,modal:s=!0}=e,u=l.useRef(null),d=l.useRef(null),[c,f]=(0,w.i)({prop:r,defaultProp:null!=a&&a,onChange:i,caller:M});return(0,o.jsx)(P,{scope:t,triggerRef:u,contentRef:d,contentId:(0,N.B)(),titleId:(0,N.B)(),descriptionId:(0,N.B)(),open:c,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(e=>!e),[f]),modal:s,children:n})};S.displayName=M;var F="DialogTrigger";l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=T(F,n),l=(0,x.s)(t,a.triggerRef);return(0,o.jsx)(A.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Y(a.open),...r,ref:l,onClick:(0,y.m)(e.onClick,a.onOpenToggle)})}).displayName=F;var[L,$]=I("DialogPortal",{forceMount:void 0}),z="DialogOverlay",U=l.forwardRef((e,t)=>{let n=$(z,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,l=T(z,e.__scopeDialog);return l.modal?(0,o.jsx)(k.C,{present:r||l.open,children:(0,o.jsx)(q,{...a,ref:t})}):null});U.displayName=z;var W=(0,D.TL)("DialogOverlay.RemoveScroll"),q=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=T(z,n);return(0,o.jsx)(O.A,{as:W,allowPinchZoom:!0,shards:[a.contentRef],children:(0,o.jsx)(A.sG.div,{"data-state":Y(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),H="DialogContent",B=l.forwardRef((e,t)=>{let n=$(H,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,l=T(H,e.__scopeDialog);return(0,o.jsx)(k.C,{present:r||l.open,children:l.modal?(0,o.jsx)(Q,{...a,ref:t}):(0,o.jsx)(G,{...a,ref:t})})});B.displayName=H;var Q=l.forwardRef((e,t)=>{let n=T(H,e.__scopeDialog),r=l.useRef(null),a=(0,x.s)(t,n.contentRef,r);return l.useEffect(()=>{let e=r.current;if(e)return(0,R.Eq)(e)},[]),(0,o.jsx)(V,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,y.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,y.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,y.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=l.forwardRef((e,t)=>{let n=T(H,e.__scopeDialog),r=l.useRef(!1),a=l.useRef(!1);return(0,o.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:t=>{var o,l;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),V=l.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,u=T(H,n),d=l.useRef(null),c=(0,x.s)(t,d);return(0,E.Oh)(),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(C.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,o.jsx)(j.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Y(u.open),...s,ref:c,onDismiss:()=>u.onOpenChange(!1)})}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(er,{titleId:u.titleId}),(0,o.jsx)(eo,{contentRef:d,descriptionId:u.descriptionId})]})]})}),Z="DialogTitle",J=l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=T(Z,n);return(0,o.jsx)(A.sG.h2,{id:a.titleId,...r,ref:t})});J.displayName=Z;var K="DialogDescription";l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=T(K,n);return(0,o.jsx)(A.sG.p,{id:a.descriptionId,...r,ref:t})}).displayName=K;var X="DialogClose";function Y(e){return e?"open":"closed"}l.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=T(X,n);return(0,o.jsx)(A.sG.button,{type:"button",...r,ref:t,onClick:(0,y.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=X;var ee="DialogTitleWarning",[et,en]=(0,b.q)(ee,{contentName:H,titleName:Z,docsSlug:"dialog"}),er=e=>{let{titleId:t}=e,n=en(ee),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return l.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},eo=e=>{let{contentRef:t,descriptionId:n}=e,r=en("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return l.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},ea=n(2085),el=n(8265),ei=n(344);function es(e){let{open:t,onOpenChange:n,footer:r,links:a=[],search:i,onSearchChange:u,isLoading:d,...c}=e,{text:f}=(0,s.s9)(),p=(0,l.useMemo)(()=>a.map(e=>{let[t,n]=e;return{type:"page",id:t,content:t,url:n}}),[a]);return(0,o.jsxs)(S,{open:t,onOpenChange:n,children:[(0,o.jsx)(U,{className:"fixed inset-0 z-50 bg-black/30 backdrop-blur-sm data-[state=closed]:animate-fd-fade-out data-[state=open]:animate-fd-fade-in"}),(0,o.jsxs)(B,{"aria-describedby":void 0,className:"fixed left-1/2 top-[10vh] z-50 w-[98vw] max-w-screen-sm -translate-x-1/2 rounded-lg border bg-fd-popover text-fd-popover-foreground shadow-lg data-[state=closed]:animate-fd-dialog-out data-[state=open]:animate-fd-dialog-in",children:[(0,o.jsx)(J,{className:"hidden",children:f.search}),(0,o.jsxs)("div",{className:"flex flex-row items-center gap-2 px-3",children:[(0,o.jsx)(ec,{isLoading:null!=d&&d}),(0,o.jsx)("input",{value:i,onChange:e=>{u(e.target.value)},placeholder:f.search,className:"w-0 flex-1 bg-transparent py-3 text-base placeholder:text-fd-muted-foreground focus-visible:outline-none"}),(0,o.jsx)("button",{type:"button","aria-label":"Close Search",onClick:()=>n(!1),className:(0,v.QP)((0,h.r)({color:"outline",className:"text-xs p-1.5"})),children:"Esc"})]}),"empty"!==c.results||p.length>0?(0,o.jsx)(ed,{items:"empty"===c.results?p:c.results,onSelect:()=>n(!1)}):null,r?(0,o.jsx)("div",{className:"mt-auto flex flex-col border-t p-3",children:r}):null]})]})}let eu={text:(0,o.jsx)(d,{className:"size-4 text-fd-muted-foreground"}),heading:(0,o.jsx)(c,{className:"size-4 text-fd-muted-foreground"}),page:(0,o.jsx)(f,{className:"size-4 text-fd-muted-foreground"})};function ed(e){let{items:t,onSelect:n,...r}=e,[a,i]=(0,l.useState)(),{text:u}=(0,s.s9)(),d=(0,ei.rd)(),c=(0,g.c)();t.length>0&&(!a||t.every(e=>e.id!==a))&&i(t[0].id);let f=e=>{var t;let{external:r,url:o}=e;r?null==(t=window.open(o,"_blank"))||t.focus():d.push(o),null==n||n(o),c.setOpen(!1)},p=(0,el.J)(e=>{if(("ArrowDown"===e.key||"ArrowUp"==e.key)&&(i(n=>{var r,o;let a=t.findIndex(e=>e.id===n);return -1===a?null==(r=t.at(0))?void 0:r.id:null==(o=t.at(("ArrowDown"===e.key?a+1:a-1)%t.length))?void 0:o.id}),e.preventDefault()),"Enter"===e.key){let n=t.find(e=>e.id===a);n&&f(n),e.preventDefault()}});return(0,l.useEffect)(()=>(window.addEventListener("keydown",p),()=>{window.removeEventListener("keydown",p)}),[p]),(0,o.jsxs)("div",{...r,className:(0,v.QP)("flex max-h-[460px] flex-col overflow-y-auto border-t p-2",r.className),children:[0===t.length?(0,o.jsx)("div",{className:"py-12 text-center text-sm",children:u.searchNoResult}):null,t.map(e=>(0,o.jsxs)(ef,{value:e.id,active:a,onActiveChange:i,onClick:()=>{f(e)},children:["page"!==e.type?(0,o.jsx)("div",{role:"none",className:"ms-2 h-full min-h-10 w-px bg-fd-border"}):null,eu[e.type],(0,o.jsx)("p",{className:"w-0 flex-1 truncate",children:e.content})]},e.id))]})}function ec(e){let{isLoading:t}=e;return(0,o.jsxs)("div",{className:"relative size-4",children:[(0,o.jsx)(p,{className:(0,v.QP)("absolute size-full animate-spin text-fd-primary transition-opacity",!t&&"opacity-0")}),(0,o.jsx)(m.A,{className:(0,v.QP)("absolute size-full text-fd-muted-foreground transition-opacity",t&&"opacity-0")})]})}function ef(e){let{active:t,onActiveChange:n,value:r,...a}=e;return(0,o.jsx)("button",{ref:(0,l.useCallback)(e=>{t===r&&e&&e.scrollIntoView({block:"nearest"})},[t,r]),type:"button","aria-selected":t===r,onPointerMove:()=>n(r),...a,className:(0,v.QP)("flex min-h-10 select-none flex-row items-center gap-2.5 rounded-lg px-2 text-start text-sm",t===r&&"bg-fd-accent text-fd-accent-foreground",a.className),children:a.children})}let ep=(0,ea.F)("rounded-md border px-2 py-0.5 text-xs font-medium text-fd-muted-foreground transition-colors",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground"}}});function em(e){let{tag:t,onTagChange:n,items:r,allowClear:a,...l}=e;return(0,o.jsxs)("div",{...l,className:(0,v.QP)("flex flex-row items-center gap-1 flex-wrap",l.className),children:[r.map(e=>{var r;return(0,o.jsx)("button",{type:"button","data-active":t===e.value,className:(0,v.QP)(ep({active:t===e.value}),null==(r=e.props)?void 0:r.className),onClick:()=>{t===e.value&&a?n(void 0):n(e.value)},tabIndex:-1,...e.props,children:e.name},e.value)}),l.children]})}function ev(e){var t;let{defaultTag:u,tags:d,api:c,delayMs:f,type:p="fetch",allowClear:m=!1,...v}=e,{locale:g}=(0,s.s9)(),[h,y]=(0,l.useState)(u),{search:x,setSearch:b,query:N}=function(e,t,o,i=100,s=!1,u){let[d,c]=(0,l.useState)(""),[f,p]=(0,l.useState)("empty"),[m,v]=(0,l.useState)(),[g,h]=(0,l.useState)(!1),y=function(e,t=1e3){let[n,r]=(0,l.useState)(e),o=(0,l.useRef)(void 0);if(0===t)return e;if(e!==n&&o.current?.value!==e){o.current&&clearTimeout(o.current.handler);let n=window.setTimeout(()=>{r(e)},t);o.current={value:e,handler:n}}return n}(d,i),x=(0,l.useRef)(void 0);return(0,a.T)((void 0)??[e,y,t,o],()=>{x.current&&(x.current(),x.current=void 0),h(!0);let a=!1;x.current=()=>{a=!0},(async function(){if(0===y.length&&!s)return"empty";if("fetch"===e.type){let{fetchDocs:r}=await n.e(996).then(n.bind(n,1615));return r(y,t,o,e)}if("algolia"===e.type){let{index:t,type:r,...a}=e,{searchDocs:l}=await n.e(728).then(n.bind(n,6728));return l(t,y,o,a)}if("orama-cloud"===e.type){let{searchDocs:t}=await n.e(886).then(n.bind(n,8886));return t(y,o,e)}let{createStaticClient:a}=await n.e(872).then(n.bind(n,6872));return r||(r=a(e)),r.search(y,t,o)})().then(e=>{a||(v(void 0),p(e))}).catch(e=>{v(e)}).finally(()=>{h(!1)})}),{search:d,setSearch:c,query:{isLoading:g,data:f,error:m}}}("fetch"===p?{type:"fetch",api:c}:{type:"static",from:c},g,h,f);return(0,i.T)(u,e=>{y(e)}),(0,o.jsx)(es,{search:x,onSearchChange:b,isLoading:N.isLoading,results:null!=(t=N.data)?t:[],...v,footer:d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(em,{tag:h,onTagChange:y,items:d,allowClear:m}),v.footer]}):v.footer})}},1285:(e,t,n)=>{n.d(t,{B:()=>s});var r,o=n(2115),a=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function s(e){let[t,n]=o.useState(l());return(0,a.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},2085:(e,t,n)=>{n.d(t,{F:()=>l});var r=n(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:i}=t,s=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let a=o(t)||o(r);return l[e][a]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,s,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...u}[t]):({...i,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2596:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(n=0;n<a;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3536:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:c,...f}=e;return(0,r.createElement)("svg",{ref:t,...s,width:o,height:o,stroke:n,strokeWidth:l?24*Number(a)/Number(o):a,className:i("lucide",u),...f},[...c.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:s,...d}=n;return(0,r.createElement)(u,{ref:a,iconNode:t,className:i("lucide-".concat(o(l(e))),"lucide-".concat(e),s),...d})});return n.displayName=l(e),n}},3655:(e,t,n)=>{n.d(t,{hO:()=>s,sG:()=>i});var r=n(2115),o=n(7650),a=n(9708),l=n(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>i});var r,o=n(2115),a=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[a,i,s]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),a=o.useRef(n),i=o.useRef(t);return l(()=>{i.current=t},[t]),o.useEffect(()=>{a.current!==n&&(i.current?.(n),a.current=n)},[n,a]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,d=u?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[d,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else i(t)},[u,e,i,s])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>a});var r=n(2115),o=n(5155);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,l=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:l,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let l=r.createContext(a),i=n.length;n=[...n,a];let s=t=>{let{scope:n,children:a,...s}=t,u=n?.[e]?.[i]||l,d=r.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:d,children:a})};return s.displayName=t+"Provider",[s,function(n,o){let s=o?.[e]?.[i]||l,u=r.useContext(s);if(u)return u;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},7936:(e,t,n)=>{n.d(t,{r:()=>r});let r=(0,n(2085).F)("inline-flex items-center justify-center rounded-md p-2 text-sm font-medium transition-colors duration-100 disabled:pointer-events-none disabled:opacity-50",{variants:{color:{primary:"bg-fd-primary text-fd-primary-foreground hover:bg-fd-primary/80",outline:"border hover:bg-fd-accent hover:text-fd-accent-foreground",ghost:"hover:bg-fd-accent hover:text-fd-accent-foreground",secondary:"border bg-fd-secondary text-fd-secondary-foreground hover:bg-fd-accent hover:text-fd-accent-foreground"},size:{sm:"gap-1 p-1 text-xs",icon:"p-1.5 [&_svg]:size-5","icon-sm":"p-1.5 [&_svg]:size-4.5"}}})},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),o=n(6101),a=n(2712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),s=r.useRef({}),u=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=d.current,o=i(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=i(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),u=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}}}]);