"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[264],{344:(e,t,n)=>{n.d(t,{Image:()=>r._V,a8:()=>r.a8,q6:()=>r.q6,rd:()=>r.rd});var r=n(9600);n(9189)},1114:(e,t,n)=>{n.d(t,{A:()=>O});var r,o,a=n(9249),i=n(2115),c="right-scroll-bar-position",u="width-before-scroll-bar";function l(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,d=new WeakMap;function f(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=f),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=(0,a.Cl)({async:!0,ssr:!1},e),i}(),v=function(){},m=i.forwardRef(function(e,t){var n,r,o,c,u=i.useRef(null),f=i.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=f[0],p=f[1],g=e.forwardProps,w=e.children,b=e.className,y=e.removeScrollBar,E=e.enabled,C=e.shards,x=e.sideCar,P=e.noIsolation,S=e.inert,k=e.allowPinchZoom,R=e.as,L=e.gapMode,N=(0,a.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(n=[u,t],r=function(e){return n.forEach(function(t){return l(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,c=o.facade,s(function(){var e=d.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||l(e,null)}),r.forEach(function(e){t.has(e)||l(e,o)})}d.set(c,n)},[n]),c),T=(0,a.Cl)((0,a.Cl)({},N),m);return i.createElement(i.Fragment,null,E&&i.createElement(x,{sideCar:h,removeScrollBar:y,shards:C,noIsolation:P,inert:S,setCallbacks:p,allowPinchZoom:!!k,lockRef:u,gapMode:L}),g?i.cloneElement(i.Children.only(w),(0,a.Cl)((0,a.Cl)({},T),{ref:M})):i.createElement(void 0===R?"div":R,(0,a.Cl)({},T,{className:b,ref:M}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:u,zeroRight:c};var p=function(e){var t=e.sideCar,n=(0,a.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,(0,a.Cl)({},n))};p.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},y={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},x=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return y;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},P=b(),S="data-scroll-locked",k=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},R=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},L=function(){i.useEffect(function(){return document.body.setAttribute(S,(R()+1).toString()),function(){var e=R()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},N=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var a=i.useMemo(function(){return x(o)},[o]);return i.createElement(P,{styles:k(a,!t,o,n?"":"!important")})},M=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return M=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){M=!1}var I=!!M&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},B=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Y(e,r)){var o=A(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Y=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},A=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,d=c>0,f=0,h=0;do{var v=A(e,u),m=v[0],p=v[1]-v[2]-i*m;(m||p)&&Y(e,u)&&(f+=p,h+=m),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&c>f)?s=!0:!d&&(o&&1>Math.abs(h)||!o&&-c>h)&&(s=!0),s},X=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},q=function(e){return e&&"current"in e?e.current:e},F=0,H=[];let Z=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(F++)[0],c=i.useState(b)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,a.fX)([e.lockRef.current],(e.shards||[]).map(q),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=X(e),i=n.current,c="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=B(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=B(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var h=r.current||o;return W(h,t,e,"h"===h?c:l,!0)},[]),s=i.useCallback(function(e){if(H.length&&H[H.length-1]===c){var n="deltaY"in e?_(e):X(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(q).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=i.useCallback(function(e){n.current=X(e),r.current=void 0},[]),h=i.useCallback(function(t){d(t.type,_(t),t.target,l(t,e.lockRef.current))},[]),v=i.useCallback(function(t){d(t.type,X(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return H.push(c),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:v}),document.addEventListener("wheel",s,I),document.addEventListener("touchmove",s,I),document.addEventListener("touchstart",f,I),function(){H=H.filter(function(e){return e!==c}),document.removeEventListener("wheel",s,I),document.removeEventListener("touchmove",s,I),document.removeEventListener("touchstart",f,I)}},[]);var m=e.removeScrollBar,p=e.inert;return i.createElement(i.Fragment,null,p?i.createElement(c,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?i.createElement(N,{gapMode:e.gapMode}):null)},h.useMedium(r),p);var D=i.forwardRef(function(e,t){return i.createElement(m,(0,a.Cl)({},e,{ref:t,sideCar:Z}))});D.classNames=m.classNames;let O=D},1339:(e,t,n)=>{n.d(t,{NavProvider:()=>s,StylesProvider:()=>u,h:()=>d,v:()=>c});var r=n(5155),o=n(2115),a=n(344);let i=(0,a.q6)("StylesContext",{tocNav:"xl:hidden",toc:"max-xl:hidden"});function c(){return i.use()}function u(e){let{children:t,...n}=e;return(0,r.jsx)(i.Provider,{value:n,children:t})}let l=(0,a.q6)("NavContext",{isTransparent:!1});function s(e){let{transparentMode:t="none",children:n}=e,[a,i]=(0,o.useState)("none"!==t);return(0,o.useEffect)(()=>{if("top"!==t)return;let e=()=>{i(window.scrollY<10)};return e(),window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[t]),(0,r.jsx)(l.Provider,{value:(0,o.useMemo)(()=>({isTransparent:a}),[a]),children:n})}function d(){return l.use()}},9600:(e,t,n)=>{n.d(t,{N_:()=>d,Uy:()=>c,_V:()=>s,a8:()=>u,q6:()=>f,rd:()=>l});var r=n(2115),o=n(5155),a=()=>{throw Error("You need to wrap your application inside `FrameworkProvider`.")},i=f("FrameworkContext",{useParams:a,useRouter:a,usePathname:a});function c({children:e,...t}){let n=r.useMemo(()=>({usePathname:t.usePathname,useRouter:t.useRouter,Link:t.Link,Image:t.Image,useParams:t.useParams}),[t.Link,t.usePathname,t.useRouter,t.useParams,t.Image]);return(0,o.jsx)(i.Provider,{value:n,children:e})}function u(){return i.use().usePathname()}function l(){return i.use().useRouter()}function s(e){let{Image:t}=i.use();if(!t){let{src:t,alt:n,priority:r,...a}=e;return(0,o.jsx)("img",{alt:n,src:t,fetchPriority:r?"high":"auto",...a})}return(0,o.jsx)(t,{...e})}function d(e){let{Link:t}=i.use();if(!t){let{href:t,prefetch:n,...r}=e;return(0,o.jsx)("a",{href:t,...r})}return(0,o.jsx)(t,{...e})}function f(e,t){let n=r.createContext(t);return{Provider:e=>(0,o.jsx)(n.Provider,{value:e.value,children:e.children}),use:t=>{let o=r.useContext(n);if(!o)throw Error(t??`Provider of ${e} is required but missing.`);return o}}}},9697:(e,t,n)=>{n.d(t,{Cr:()=>o,I18nLabel:()=>i,gJ:()=>a,s9:()=>c});var r=n(2115);let o={search:"Search",searchNoResult:"No results found",toc:"On this page",tocNoHeadings:"No Headings",lastUpdate:"Last updated on",chooseLanguage:"Choose a language",nextPage:"Next Page",previousPage:"Previous Page",chooseTheme:"Theme",editOnGithub:"Edit on GitHub"},a=(0,r.createContext)({text:o});function i(e){let{text:t}=c();return t[e.label]}function c(){return(0,r.useContext)(a)}}}]);