"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[887],{408:(e,t,r)=>{r.d(t,{Popover:()=>tb,PopoverContent:()=>tw,PopoverTrigger:()=>tv});var n=r(5155),o=r(2115),i=r(5185),l=r(6101),a=r(6081),s=r(9178),u=r(2293),c=r(7900),f=r(1285);let d=["top","right","bottom","left"],h=Math.min,p=Math.max,m=Math.round,g=Math.floor,y=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},v={start:"end",end:"start"};function w(e,t){return"function"==typeof e?e(t):e}function S(e){return e.split("-")[0]}function x(e){return e.split("-")[1]}function I(e){return"x"===e?"y":"x"}function A(e){return"y"===e?"height":"width"}function T(e){return["top","bottom"].includes(S(e))?"y":"x"}function O(e){return e.replace(/start|end/g,e=>v[e])}function k(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function N(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function D(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function E(e,t,r){let n,{reference:o,floating:i}=e,l=T(t),a=I(T(t)),s=A(a),u=S(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,h=o[s]/2-i[s]/2;switch(u){case"top":n={x:f,y:o.y-i.height};break;case"bottom":n={x:f,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:d};break;case"left":n={x:o.x-i.width,y:d};break;default:n={x:o.x,y:o.y}}switch(x(t)){case"start":n[a]-=h*(r&&c?-1:1);break;case"end":n[a]+=h*(r&&c?-1:1)}return n}let P=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=E(u,n,s),d=n,h={},p=0;for(let r=0;r<a.length;r++){let{name:i,fn:m}=a[r],{x:g,y:y,data:b,reset:v}=await m({x:c,y:f,initialPlacement:n,placement:d,strategy:o,middlewareData:h,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=y?y:f,h={...h,[i]:{...h[i],...b}},v&&p<=50&&(p++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(u=!0===v.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):v.rects),{x:c,y:f}=E(u,d,s)),r=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:h}};async function _(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:h=0}=w(t,e),p=N(h),m=a[d?"floating"===f?"reference":"floating":f],g=D(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(m)))||r?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===f?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),v=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},S=D(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:b,strategy:s}):y);return{top:(g.top-S.top+p.top)/v.y,bottom:(S.bottom-g.bottom+p.bottom)/v.y,left:(g.left-S.left+p.left)/v.x,right:(S.right-g.right+p.right)/v.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function C(e){return d.some(t=>e[t]>=0)}async function M(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=S(r),a=x(r),s="y"===T(r),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,f=w(t,e),{mainAxis:d,crossAxis:h,alignmentAxis:p}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof p&&(h="end"===a?-1*p:p),s?{x:h*c,y:d*u}:{x:d*u,y:h*c}}function L(){return"undefined"!=typeof window}function z(e){return $(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function U(e){var t;return null==(t=($(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function $(e){return!!L()&&(e instanceof Node||e instanceof j(e).Node)}function F(e){return!!L()&&(e instanceof Element||e instanceof j(e).Element)}function B(e){return!!L()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function W(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}function V(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=K(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function H(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function J(e){let t=G(),r=F(e)?K(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function G(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Y(e){return["html","body","#document"].includes(z(e))}function K(e){return j(e).getComputedStyle(e)}function q(e){return F(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Z(e){if("html"===z(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||U(e);return W(t)?t.host:t}function X(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=Z(t);return Y(r)?t.ownerDocument?t.ownerDocument.body:t.body:B(r)&&V(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=j(o);if(i){let e=Q(l);return t.concat(l,l.visualViewport||[],V(o)?o:[],e&&r?X(e):[])}return t.concat(o,X(o,[],r))}function Q(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ee(e){let t=K(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=B(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,a=m(r)!==i||m(n)!==l;return a&&(r=i,n=l),{width:r,height:n,$:a}}function et(e){return F(e)?e:e.contextElement}function er(e){let t=et(e);if(!B(t))return y(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=ee(t),l=(i?m(r.width):r.width)/n,a=(i?m(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let en=y(0);function eo(e){let t=j(e);return G()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:en}function ei(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=et(e),a=y(1);t&&(n?F(n)&&(a=er(n)):a=er(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===j(l))&&o)?eo(l):y(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=j(l),t=n&&F(n)?j(n):n,r=e,o=Q(r);for(;o&&n&&t!==r;){let e=er(o),t=o.getBoundingClientRect(),n=K(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=l,o=Q(r=j(o))}}return D({width:f,height:d,x:u,y:c})}function el(e,t){let r=q(e).scrollLeft;return t?t.left+r:ei(U(e)).left+r}function ea(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:el(e,n)),y:n.top+t.scrollTop}}function es(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=j(e),n=U(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=G();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=U(e),r=q(e),n=e.ownerDocument.body,o=p(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=p(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+el(e),a=-r.scrollTop;return"rtl"===K(n).direction&&(l+=p(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:l,y:a}}(U(e));else if(F(t))n=function(e,t){let r=ei(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=B(e)?er(e):y(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=eo(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return D(n)}function eu(e){return"static"===K(e).position}function ec(e,t){if(!B(e)||"fixed"===K(e).position)return null;if(t)return t(e);let r=e.offsetParent;return U(e)===r&&(r=r.ownerDocument.body),r}function ef(e,t){let r=j(e);if(H(e))return r;if(!B(e)){let t=Z(e);for(;t&&!Y(t);){if(F(t)&&!eu(t))return t;t=Z(t)}return r}let n=ec(e,t);for(;n&&["table","td","th"].includes(z(n))&&eu(n);)n=ec(n,t);return n&&Y(n)&&eu(n)&&!J(n)?r:n||function(e){let t=Z(e);for(;B(t)&&!Y(t);){if(J(t))return t;if(H(t))break;t=Z(t)}return null}(e)||r}let ed=async function(e){let t=this.getOffsetParent||ef,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=B(t),o=U(t),i="fixed"===r,l=ei(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=y(0);if(n||!n&&!i)if(("body"!==z(t)||V(o))&&(a=q(t)),n){let e=ei(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=el(o));let u=!o||n||i?y(0):ea(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eh={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=U(n),a=!!t&&H(t.floating);if(n===l||a&&i)return r;let s={scrollLeft:0,scrollTop:0},u=y(1),c=y(0),f=B(n);if((f||!f&&!i)&&(("body"!==z(n)||V(l))&&(s=q(n)),B(n))){let e=ei(n);u=er(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let d=!l||f||i?y(0):ea(l,s,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:r.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:U,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?H(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=X(e,[],!1).filter(e=>F(e)&&"body"!==z(e)),o=null,i="fixed"===K(e).position,l=i?Z(e):e;for(;F(l)&&!Y(l);){let t=K(l),r=J(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||V(l)&&!r&&function e(t,r){let n=Z(t);return!(n===r||!F(n)||Y(n))&&("fixed"===K(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=Z(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=i[0],a=i.reduce((e,r)=>{let n=es(t,r,o);return e.top=p(n.top,e.top),e.right=h(n.right,e.right),e.bottom=h(n.bottom,e.bottom),e.left=p(n.left,e.left),e},es(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ef,getElementRects:ed,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ee(e);return{width:t,height:r}},getScale:er,isElement:F,isRTL:function(e){return"rtl"===K(e).direction}};function ep(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let em=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=w(e,t)||{};if(null==u)return{};let f=N(c),d={x:r,y:n},m=I(T(o)),g=A(m),y=await l.getDimensions(u),b="y"===m,v=b?"clientHeight":"clientWidth",S=i.reference[g]+i.reference[m]-d[m]-i.floating[g],O=d[m]-i.reference[m],k=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),D=k?k[v]:0;D&&await (null==l.isElement?void 0:l.isElement(k))||(D=a.floating[v]||i.floating[g]);let E=D/2-y[g]/2-1,P=h(f[b?"top":"left"],E),_=h(f[b?"bottom":"right"],E),R=D-y[g]-_,C=D/2-y[g]/2+(S/2-O/2),M=p(P,h(C,R)),L=!s.arrow&&null!=x(o)&&C!==M&&i.reference[g]/2-(C<P?P:_)-y[g]/2<0,z=L?C<P?C-P:C-R:0;return{[m]:d[m]+z,data:{[m]:M,centerOffset:C-M-z,...L&&{alignmentOffset:z}},reset:L}}}),eg=(e,t,r)=>{let n=new Map,o={platform:eh,...r},i={...o.platform,_c:n};return P(e,t,{...o,platform:i})};var ey=r(7650),eb="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function ev(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ev(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ev(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ew(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eS(e,t){let r=ew(e);return Math.round(t*r)/r}function ex(e){let t=o.useRef(e);return eb(()=>{t.current=e}),t}let eI=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?em({element:r.current,padding:n}).fn(t):{}:r?em({element:r,padding:n}).fn(t):{}}}),eA=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await M(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=w(e,t),u={x:r,y:n},c=await _(t,s),f=T(S(o)),d=I(f),m=u[d],g=u[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=m+c[e],n=m-c[t];m=p(r,h(m,n))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=g+c[e],n=g-c[t];g=p(r,h(g,n))}let y=a.fn({...t,[d]:m,[f]:g});return{...y,data:{x:y.x-r,y:y.y-n,enabled:{[d]:i,[f]:l}}}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=w(e,t),c={x:r,y:n},f=T(o),d=I(f),h=c[d],p=c[f],m=w(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,r=i.reference[d]+i.reference[e]-g.mainAxis;h<t?h=t:h>r&&(h=r)}if(u){var y,b;let e="y"===d?"width":"height",t=["top","left"].includes(S(o)),r=i.reference[f]-i.floating[e]+(t&&(null==(y=l.offset)?void 0:y[f])||0)+(t?0:g.crossAxis),n=i.reference[f]+i.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[f])||0)-(t?g.crossAxis:0);p<r?p=r:p>n&&(p=n)}return{[d]:h,[f]:p}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:h=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:b=!0,...v}=w(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let N=S(a),D=T(c),E=S(c)===c,P=await (null==f.isRTL?void 0:f.isRTL(d.floating)),R=m||(E||!b?[k(c)]:function(e){let t=k(e);return[O(e),t,O(t)]}(c)),C="none"!==y;!m&&C&&R.push(...function(e,t,r,n){let o=x(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(S(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(O)))),i}(c,b,y,P));let M=[c,...R],L=await _(t,v),z=[],j=(null==(n=s.flip)?void 0:n.overflows)||[];if(h&&z.push(L[N]),p){let e=function(e,t,r){void 0===r&&(r=!1);let n=x(e),o=I(T(e)),i=A(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=k(l)),[l,k(l)]}(a,u,P);z.push(L[e[0]],L[e[1]])}if(j=[...j,{placement:a,overflows:z}],!z.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=M[e];if(t)return{data:{index:e,overflows:j},reset:{placement:t}};let r=null==(i=j.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(g){case"bestFit":{let e=null==(l=j.filter(e=>{if(C){let t=T(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=c}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i,{placement:l,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...f}=w(e,t),d=await _(t,f),m=S(l),g=x(l),y="y"===T(l),{width:b,height:v}=a.floating;"top"===m||"bottom"===m?(o=m,i=g===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=m,o="end"===g?"top":"bottom");let I=v-d.top-d.bottom,A=b-d.left-d.right,O=h(v-d[o],I),k=h(b-d[i],A),N=!t.middlewareData.shift,D=O,E=k;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(E=A),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(D=I),N&&!g){let e=p(d.left,0),t=p(d.right,0),r=p(d.top,0),n=p(d.bottom,0);y?E=b-2*(0!==e||0!==t?e+t:p(d.left,d.right)):D=v-2*(0!==r||0!==n?r+n:p(d.top,d.bottom))}await c({...t,availableWidth:E,availableHeight:D});let P=await s.getDimensions(u.floating);return b!==P.width||v!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=w(e,t);switch(n){case"referenceHidden":{let e=R(await _(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:C(e)}}}case"escaped":{let e=R(await _(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:C(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eI(e),options:[e,t]});var eP=r(3655),e_=o.forwardRef((e,t)=>{let{children:r,width:o=10,height:i=5,...l}=e;return(0,n.jsx)(eP.sG.svg,{...l,ref:t,width:o,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,n.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e_.displayName="Arrow";var eR=r(9033),eC=r(2712),eM="Popper",[eL,ez]=(0,a.A)(eM),[ej,eU]=eL(eM),e$=e=>{let{__scopePopper:t,children:r}=e,[i,l]=o.useState(null);return(0,n.jsx)(ej,{scope:t,anchor:i,onAnchorChange:l,children:r})};e$.displayName=eM;var eF="PopperAnchor",eB=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...a}=e,s=eU(eF,r),u=o.useRef(null),c=(0,l.s)(t,u);return o.useEffect(()=>{s.onAnchorChange((null==i?void 0:i.current)||u.current)}),i?null:(0,n.jsx)(eP.sG.div,{...a,ref:c})});eB.displayName=eF;var eW="PopperContent",[eV,eH]=eL(eW),eJ=o.forwardRef((e,t)=>{var r,i,a,s,u,c,f,d;let{__scopePopper:m,side:y="bottom",sideOffset:b=0,align:v="center",alignOffset:w=0,arrowPadding:S=0,avoidCollisions:x=!0,collisionBoundary:I=[],collisionPadding:A=0,sticky:T="partial",hideWhenDetached:O=!1,updatePositionStrategy:k="optimized",onPlaced:N,...D}=e,E=eU(eW,m),[P,_]=o.useState(null),R=(0,l.s)(t,e=>_(e)),[C,M]=o.useState(null),L=function(e){let[t,r]=o.useState(void 0);return(0,eC.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(C),z=null!=(f=null==L?void 0:L.width)?f:0,j=null!=(d=null==L?void 0:L.height)?d:0,$="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},F=Array.isArray(I)?I:[I],B=F.length>0,W={padding:$,boundary:F.filter(eq),altBoundary:B},{refs:V,floatingStyles:H,placement:J,isPositioned:G,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[f,d]=o.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=o.useState(n);ev(h,n)||p(n);let[m,g]=o.useState(null),[y,b]=o.useState(null),v=o.useCallback(e=>{e!==I.current&&(I.current=e,g(e))},[]),w=o.useCallback(e=>{e!==A.current&&(A.current=e,b(e))},[]),S=l||m,x=a||y,I=o.useRef(null),A=o.useRef(null),T=o.useRef(f),O=null!=u,k=ex(u),N=ex(i),D=ex(c),E=o.useCallback(()=>{if(!I.current||!A.current)return;let e={placement:t,strategy:r,middleware:h};N.current&&(e.platform=N.current),eg(I.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};P.current&&!ev(T.current,t)&&(T.current=t,ey.flushSync(()=>{d(t)}))})},[h,t,r,N,D]);eb(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let P=o.useRef(!1);eb(()=>(P.current=!0,()=>{P.current=!1}),[]),eb(()=>{if(S&&(I.current=S),x&&(A.current=x),S&&x){if(k.current)return k.current(S,x,E);E()}},[S,x,E,k,O]);let _=o.useMemo(()=>({reference:I,floating:A,setReference:v,setFloating:w}),[v,w]),R=o.useMemo(()=>({reference:S,floating:x}),[S,x]),C=o.useMemo(()=>{let e={position:r,left:0,top:0};if(!R.floating)return e;let t=eS(R.floating,f.x),n=eS(R.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...ew(R.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,R.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:E,refs:_,elements:R,floatingStyles:C}),[f,E,_,R,C])}({strategy:"fixed",placement:y+("center"!==v?"-"+v:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,c=et(e),f=i||l?[...c?X(c):[],...X(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),l&&e.addEventListener("resize",r)});let d=c&&s?function(e,t){let r,n=null,o=U(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:m}=u;if(a||t(),!d||!m)return;let y=g(f),b=g(o.clientWidth-(c+d)),v={rootMargin:-y+"px "+-b+"px "+-g(o.clientHeight-(f+m))+"px "+-g(c)+"px",threshold:p(0,h(1,s))||1},w=!0;function S(t){let n=t[0].intersectionRatio;if(n!==s){if(!w)return l();n?l(!1,n):r=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==n||ep(u,e.getBoundingClientRect())||l(),w=!1}try{n=new IntersectionObserver(S,{...v,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(S,v)}n.observe(e)}(!0),i}(c,r):null,m=-1,y=null;a&&(y=new ResizeObserver(e=>{let[n]=e;n&&n.target===c&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),r()}),c&&!u&&y.observe(c),y.observe(t));let b=u?ei(e):null;return u&&function t(){let n=ei(e);b&&!ep(b,n)&&r(),b=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",r),l&&e.removeEventListener("resize",r)}),null==d||d(),null==(e=y)||e.disconnect(),y=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===k})},elements:{reference:E.anchor},middleware:[eA({mainAxis:b+j,alignmentAxis:w}),x&&eT({mainAxis:!0,crossAxis:!1,limiter:"partial"===T?eO():void 0,...W}),x&&ek({...W}),eN({...W,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),C&&eE({element:C,padding:S}),eZ({arrowWidth:z,arrowHeight:j}),O&&eD({strategy:"referenceHidden",...W})]}),[K,q]=eX(J),Z=(0,eR.c)(N);(0,eC.N)(()=>{G&&(null==Z||Z())},[G,Z]);let Q=null==(r=Y.arrow)?void 0:r.x,ee=null==(i=Y.arrow)?void 0:i.y,er=(null==(a=Y.arrow)?void 0:a.centerOffset)!==0,[en,eo]=o.useState();return(0,eC.N)(()=>{P&&eo(window.getComputedStyle(P).zIndex)},[P]),(0,n.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:G?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(s=Y.transformOrigin)?void 0:s.x,null==(u=Y.transformOrigin)?void 0:u.y].join(" "),...(null==(c=Y.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,n.jsx)(eV,{scope:m,placedSide:K,onArrowChange:M,arrowX:Q,arrowY:ee,shouldHideArrow:er,children:(0,n.jsx)(eP.sG.div,{"data-side":K,"data-align":q,...D,ref:R,style:{...D.style,animation:G?void 0:"none"}})})})});eJ.displayName=eW;var eG="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eK=o.forwardRef(function(e,t){let{__scopePopper:r,...o}=e,i=eH(eG,r),l=eY[i.placedSide];return(0,n.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,n.jsx)(e_,{...o,ref:t,style:{...o.style,display:"block"}})})});function eq(e){return null!==e}eK.displayName=eG;var eZ=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(r=u.arrow)?void 0:r.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[h,p]=eX(a),m={start:"0%",center:"50%",end:"100%"}[p],g=(null!=(i=null==(n=u.arrow)?void 0:n.x)?i:0)+f/2,y=(null!=(l=null==(o=u.arrow)?void 0:o.y)?l:0)+d/2,b="",v="";return"bottom"===h?(b=c?m:"".concat(g,"px"),v="".concat(-d,"px")):"top"===h?(b=c?m:"".concat(g,"px"),v="".concat(s.floating.height+d,"px")):"right"===h?(b="".concat(-d,"px"),v=c?m:"".concat(y,"px")):"left"===h&&(b="".concat(s.floating.width+d,"px"),v=c?m:"".concat(y,"px")),{data:{x:b,y:v}}}});function eX(e){let[t,r="center"]=e.split("-");return[t,r]}var eQ=r(4378),e0=r(8905),e1=r(9708),e2=r(5845),e5=r(8168),e9=r(1114),e3="Popover",[e6,e7]=(0,a.A)(e3,[ez]),e4=ez(),[e8,te]=e6(e3),tt=e=>{let{__scopePopover:t,children:r,open:i,defaultOpen:l,onOpenChange:a,modal:s=!1}=e,u=e4(t),c=o.useRef(null),[d,h]=o.useState(!1),[p,m]=(0,e2.i)({prop:i,defaultProp:null!=l&&l,onChange:a,caller:e3});return(0,n.jsx)(e$,{...u,children:(0,n.jsx)(e8,{scope:t,contentId:(0,f.B)(),triggerRef:c,open:p,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:d,onCustomAnchorAdd:o.useCallback(()=>h(!0),[]),onCustomAnchorRemove:o.useCallback(()=>h(!1),[]),modal:s,children:r})})};tt.displayName=e3;var tr="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:r,...i}=e,l=te(tr,r),a=e4(r),{onCustomAnchorAdd:s,onCustomAnchorRemove:u}=l;return o.useEffect(()=>(s(),()=>u()),[s,u]),(0,n.jsx)(eB,{...a,...i,ref:t})}).displayName=tr;var tn="PopoverTrigger",to=o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=te(tn,r),s=e4(r),u=(0,l.s)(t,a.triggerRef),c=(0,n.jsx)(eP.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":tg(a.open),...o,ref:u,onClick:(0,i.m)(e.onClick,a.onOpenToggle)});return a.hasCustomAnchor?c:(0,n.jsx)(eB,{asChild:!0,...s,children:c})});to.displayName=tn;var ti="PopoverPortal",[tl,ta]=e6(ti,{forceMount:void 0}),ts=e=>{let{__scopePopover:t,forceMount:r,children:o,container:i}=e,l=te(ti,t);return(0,n.jsx)(tl,{scope:t,forceMount:r,children:(0,n.jsx)(e0.C,{present:r||l.open,children:(0,n.jsx)(eQ.Z,{asChild:!0,container:i,children:o})})})};ts.displayName=ti;var tu="PopoverContent",tc=o.forwardRef((e,t)=>{let r=ta(tu,e.__scopePopover),{forceMount:o=r.forceMount,...i}=e,l=te(tu,e.__scopePopover);return(0,n.jsx)(e0.C,{present:o||l.open,children:l.modal?(0,n.jsx)(td,{...i,ref:t}):(0,n.jsx)(th,{...i,ref:t})})});tc.displayName=tu;var tf=(0,e1.TL)("PopoverContent.RemoveScroll"),td=o.forwardRef((e,t)=>{let r=te(tu,e.__scopePopover),a=o.useRef(null),s=(0,l.s)(t,a),u=o.useRef(!1);return o.useEffect(()=>{let e=a.current;if(e)return(0,e5.Eq)(e)},[]),(0,n.jsx)(e9.A,{as:tf,allowPinchZoom:!0,children:(0,n.jsx)(tp,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),u.current||null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;u.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),th=o.forwardRef((e,t)=>{let r=te(tu,e.__scopePopover),i=o.useRef(!1),l=o.useRef(!1);return(0,n.jsx)(tp,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(i.current||null==(o=r.triggerRef.current)||o.focus(),t.preventDefault()),i.current=!1,l.current=!1},onInteractOutside:t=>{var n,o;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let a=t.target;(null==(o=r.triggerRef.current)?void 0:o.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),tp=o.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:a,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:p,...m}=e,g=te(tu,r),y=e4(r);return(0,u.Oh)(),(0,n.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,n.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:p,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:h,onDismiss:()=>g.onOpenChange(!1),children:(0,n.jsx)(eJ,{"data-state":tg(g.open),role:"dialog",id:g.contentId,...y,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),tm="PopoverClose";function tg(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,l=te(tm,r);return(0,n.jsx)(eP.sG.button,{type:"button",...o,ref:t,onClick:(0,i.m)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=tm,o.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,i=e4(r);return(0,n.jsx)(eK,{...i,...o,ref:t})}).displayName="PopoverArrow";var ty=r(9688);let tb=tt,tv=to,tw=o.forwardRef((e,t)=>{let{className:r,align:o="center",sideOffset:i=4,...l}=e;return(0,n.jsx)(ts,{children:(0,n.jsx)(tc,{ref:t,align:o,sideOffset:i,side:"bottom",className:(0,ty.QP)("z-50 min-w-[220px] max-w-[98vw] rounded-lg border bg-fd-popover p-2 text-sm text-fd-popover-foreground shadow-lg focus-visible:outline-none data-[state=closed]:animate-fd-popover-out data-[state=open]:animate-fd-popover-in",r),...l})})});tw.displayName=tc.displayName},910:(e,t,r)=>{r.d(t,{ThemeToggle:()=>p});var n=r(5155),o=r(2085),i=r(3536);let l=(0,i.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),a=(0,i.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),s=(0,i.A)("airplay",[["path",{d:"M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1",key:"ns4c3b"}],["path",{d:"m12 15 5 6H7Z",key:"14qnn2"}]]);var u=r(1362),c=r(2115),f=r(9688);let d=(0,o.F)("size-6.5 rounded-full p-1.5 text-fd-muted-foreground",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground",false:"text-fd-muted-foreground"}}}),h=[["light",l],["dark",a],["system",s]];function p(e){let{className:t,mode:r="light-dark",...o}=e,{setTheme:i,theme:l,resolvedTheme:a}=(0,u.D)(),[s,p]=(0,c.useState)(!1);(0,c.useLayoutEffect)(()=>{p(!0)},[]);let m=(0,f.QP)("inline-flex items-center rounded-full border p-1",t);if("light-dark"===r){let e=s?a:null;return(0,n.jsx)("button",{className:m,"aria-label":"Toggle Theme",onClick:()=>i("light"===e?"dark":"light"),"data-theme-toggle":"",...o,children:h.map(t=>{let[r,o]=t;if("system"!==r)return(0,n.jsx)(o,{fill:"currentColor",className:(0,f.QP)(d({active:e===r}))},r)})})}let g=s?l:null;return(0,n.jsx)("div",{className:m,"data-theme-toggle":"",...o,children:h.map(e=>{let[t,r]=e;return(0,n.jsx)("button",{"aria-label":t,className:(0,f.QP)(d({active:g===t})),onClick:()=>i(t),children:(0,n.jsx)(r,{className:"size-full",fill:"currentColor"})},t)})})}},1053:(e,t,r)=>{r.d(t,{LanguageToggle:()=>s,LanguageToggleText:()=>u});var n=r(5155),o=r(9697),i=r(408),l=r(9688),a=r(7936);function s(e){let t=(0,o.s9)();if(!t.locales)throw Error("Missing `<I18nProvider />`");return(0,n.jsxs)(i.Popover,{children:[(0,n.jsx)(i.PopoverTrigger,{"aria-label":t.text.chooseLanguage,...e,className:(0,l.QP)((0,a.r)({color:"ghost",className:"gap-1.5 p-1.5"}),e.className),children:e.children}),(0,n.jsxs)(i.PopoverContent,{className:"flex flex-col overflow-hidden p-0",children:[(0,n.jsx)("p",{className:"mb-1 p-2 text-xs font-medium text-fd-muted-foreground",children:t.text.chooseLanguage}),t.locales.map(e=>(0,n.jsx)("button",{type:"button",className:(0,l.QP)("p-2 text-start text-sm",e.locale===t.locale?"bg-fd-primary/10 font-medium text-fd-primary":"hover:bg-fd-accent hover:text-fd-accent-foreground"),onClick:()=>{var r;null==(r=t.onChange)||r.call(t,e.locale)},children:e.name},e.locale))]})]})}function u(e){var t,r;let i=(0,o.s9)(),l=null==(r=i.locales)||null==(t=r.find(e=>e.locale===i.locale))?void 0:t.name;return(0,n.jsx)("span",{...e,children:l})}},1362:(e,t,r)=>{r.d(t,{D:()=>u,N:()=>c});var n=r(2115),o=(e,t,r,n,o,i,l,a)=>{let s=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&i?o.map(e=>i[e]||e):o;r?(s.classList.remove(...n),s.classList.add(i&&i[t]?i[t]:t)):s.setAttribute(e,t)}),r=t,a&&u.includes(r)&&(s.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},i=["light","dark"],l="(prefers-color-scheme: dark)",a=n.createContext(void 0),s={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(a))?e:s},c=e=>n.useContext(a)?n.createElement(n.Fragment,null,e.children):n.createElement(d,{...e}),f=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:c=f,defaultTheme:d=o?"system":"light",attribute:y="data-theme",value:b,children:v,nonce:w,scriptProps:S}=e,[x,I]=n.useState(()=>p(u,d)),[A,T]=n.useState(()=>"system"===x?g():x),O=b?Object.values(b):c,k=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=g());let n=b?b[t]:t,l=r?m(w):null,a=document.documentElement,u=e=>{"class"===e?(a.classList.remove(...O),n&&a.classList.add(n)):e.startsWith("data-")&&(n?a.setAttribute(e,n):a.removeAttribute(e))};if(Array.isArray(y)?y.forEach(u):u(y),s){let e=i.includes(d)?d:null,r=i.includes(t)?t:e;a.style.colorScheme=r}null==l||l()},[w]),N=n.useCallback(e=>{let t="function"==typeof e?e(x):e;I(t);try{localStorage.setItem(u,t)}catch(e){}},[x]),D=n.useCallback(e=>{T(g(e)),"system"===x&&o&&!t&&k("system")},[x,t]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(D),D(e),()=>e.removeListener(D)},[D]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?I(e.newValue):N(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[N]),n.useEffect(()=>{k(null!=t?t:x)},[t,x]);let E=n.useMemo(()=>({theme:x,setTheme:N,forcedTheme:t,resolvedTheme:"system"===x?A:x,themes:o?[...c,"system"]:c,systemTheme:o?A:void 0}),[x,N,t,A,o,c]);return n.createElement(a.Provider,{value:E},n.createElement(h,{forcedTheme:t,storageKey:u,attribute:y,enableSystem:o,enableColorScheme:s,defaultTheme:d,value:b,themes:c,nonce:w,scriptProps:S}),v)},h=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:i,enableSystem:l,enableColorScheme:a,defaultTheme:s,value:u,themes:c,nonce:f,scriptProps:d}=e,h=JSON.stringify([i,r,s,t,c,u,l,a]).slice(1,-1);return n.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(h,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},2359:(e,t,r)=>{r.d(t,{DO:()=>o,Fc:()=>i,JK:()=>l});let n={arabic:"ar",armenian:"am",bulgarian:"bg",czech:"cz",danish:"dk",dutch:"nl",english:"en",finnish:"fi",french:"fr",german:"de",greek:"gr",hungarian:"hu",indian:"in",indonesian:"id",irish:"ie",italian:"it",lithuanian:"lt",nepali:"np",norwegian:"no",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"rs",slovenian:"ru",spanish:"es",swedish:"se",tamil:"ta",turkish:"tr",ukrainian:"uk",sanskrit:"sk"},o={dutch:/[^A-Za-zàèéìòóù0-9_'-]+/gim,english:/[^A-Za-zàèéìòóù0-9_'-]+/gim,french:/[^a-z0-9äâàéèëêïîöôùüûœç-]+/gim,italian:/[^A-Za-zàèéìòóù0-9_'-]+/gim,norwegian:/[^a-z0-9_æøåÆØÅäÄöÖüÜ]+/gim,portuguese:/[^a-z0-9à-úÀ-Ú]/gim,russian:/[^a-z0-9а-яА-ЯёЁ]+/gim,spanish:/[^a-z0-9A-Zá-úÁ-ÚñÑüÜ]+/gim,swedish:/[^a-z0-9_åÅäÄöÖüÜ-]+/gim,german:/[^a-z0-9A-ZäöüÄÖÜß]+/gim,finnish:/[^a-z0-9äöÄÖ]+/gim,danish:/[^a-z0-9æøåÆØÅ]+/gim,hungarian:/[^a-z0-9áéíóöőúüűÁÉÍÓÖŐÚÜŰ]+/gim,romanian:/[^a-z0-9ăâîșțĂÂÎȘȚ]+/gim,serbian:/[^a-z0-9čćžšđČĆŽŠĐ]+/gim,turkish:/[^a-z0-9çÇğĞıİöÖşŞüÜ]+/gim,lithuanian:/[^a-z0-9ąčęėįšųūžĄČĘĖĮŠŲŪŽ]+/gim,arabic:/[^a-z0-9أ-ي]+/gim,nepali:/[^a-z0-9अ-ह]+/gim,irish:/[^a-z0-9áéíóúÁÉÍÓÚ]+/gim,indian:/[^a-z0-9अ-ह]+/gim,armenian:/[^a-z0-9ա-ֆ]+/gim,greek:/[^a-z0-9α-ωά-ώ]+/gim,indonesian:/[^a-z0-9]+/gim,ukrainian:/[^a-z0-9а-яА-ЯіїєІЇЄ]+/gim,slovenian:/[^a-z0-9čžšČŽŠ]+/gim,bulgarian:/[^a-z0-9а-яА-Я]+/gim,tamil:/[^a-z0-9அ-ஹ]+/gim,sanskrit:/[^a-z0-9A-Zāīūṛḷṃṁḥśṣṭḍṇṅñḻḹṝ]+/gim,czech:/[^A-Z0-9a-zěščřžýáíéúůóťďĚŠČŘŽÝÁÍÉÓÚŮŤĎ-]+/gim},i=Object.keys(n);function l(e){return void 0!==e&&i.includes(e)?n[e]:void 0}},3311:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3460:(e,t,r)=>{r.d(t,{e:()=>b,J:()=>g});var n=r(9838);let o=[65,65,65,65,65,65,65,67,69,69,69,69,73,73,73,73,69,78,79,79,79,79,79,null,79,85,85,85,85,89,80,115,97,97,97,97,97,97,97,99,101,101,101,101,105,105,105,105,101,110,111,111,111,111,111,null,111,117,117,117,117,121,112,121,65,97,65,97,65,97,67,99,67,99,67,99,67,99,68,100,68,100,69,101,69,101,69,101,69,101,69,101,71,103,71,103,71,103,71,103,72,104,72,104,73,105,73,105,73,105,73,105,73,105,73,105,74,106,75,107,107,76,108,76,108,76,108,76,108,76,108,78,110,78,110,78,110,110,78,110,79,111,79,111,79,111,79,111,82,114,82,114,82,114,83,115,83,115,83,115,83,115,84,116,84,116,84,116,85,117,85,117,85,117,85,117,85,117,85,117,87,119,89,121,89,90,122,90,122,90,122,115];var i=r(2359);let l={ational:"ate",tional:"tion",enci:"ence",anci:"ance",izer:"ize",bli:"ble",alli:"al",entli:"ent",eli:"e",ousli:"ous",ization:"ize",ation:"ate",ator:"ate",alism:"al",iveness:"ive",fulness:"ful",ousness:"ous",aliti:"al",iviti:"ive",biliti:"ble",logi:"log"},a={icate:"ic",ative:"",alize:"al",iciti:"ic",ical:"ic",ful:"",ness:""},s="[aeiouy]",u="[^aeiou][^aeiouy]*",c=s+"[aeiou]*",f="^("+u+")?"+c+u,d="^("+u+")?"+c+u+"("+c+")?$",h="^("+u+")?"+c+u+c+u,p="^("+u+")?"+s;function m(e){let t,r,n,o,i,c;if(e.length<3)return e;let m=e.substring(0,1);if("y"==m&&(e=m.toUpperCase()+e.substring(1)),o=/^(.+?)([^s])s$/,(n=/^(.+?)(ss|i)es$/).test(e)?e=e.replace(n,"$1$2"):o.test(e)&&(e=e.replace(o,"$1$2")),o=/^(.+?)(ed|ing)$/,(n=/^(.+?)eed$/).test(e)){let t=n.exec(e);(n=new RegExp(f)).test(t[1])&&(n=/.$/,e=e.replace(n,""))}else o.test(e)&&(t=o.exec(e)[1],(o=new RegExp(p)).test(t)&&(e=t,o=/(at|bl|iz)$/,i=RegExp("([^aeiouylsz])\\1$"),c=RegExp("^"+u+s+"[^aeiouwxy]$"),o.test(e)?e+="e":i.test(e)?(n=/.$/,e=e.replace(n,"")):c.test(e)&&(e+="e")));if((n=/^(.+?)y$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(p),t&&n.test(t)&&(e=t+"i")}if((n=/^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(f),t&&n.test(t)&&(e=t+l[r])}if((n=/^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/).test(e)){let o=n.exec(e);t=o?.[1],r=o?.[2],n=new RegExp(f),t&&n.test(t)&&(e=t+a[r])}if(o=/^(.+?)(s|t)(ion)$/,(n=/^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(h),t&&n.test(t)&&(e=t)}else if(o.test(e)){let r=o.exec(e);t=r?.[1]??""+r?.[2]??"",(o=new RegExp(h)).test(t)&&(e=t)}if((n=/^(.+?)e$/).test(e)){let r=n.exec(e);t=r?.[1],n=new RegExp(h),o=new RegExp(d),i=RegExp("^"+u+s+"[^aeiouwxy]$"),t&&(n.test(t)||o.test(t)&&!i.test(t))&&(e=t)}return n=/ll$/,o=new RegExp(h),n.test(e)&&o.test(e)&&(n=/.$/,e=e.replace(n,"")),"y"==m&&(e=m.toLowerCase()+e.substring(1)),e}function g(e,t,r=!0){let n=`${this.language}:${e}:${t}`;return r&&this.normalizationCache.has(n)?this.normalizationCache.get(n):this.stopWords?.includes(t)?(r&&this.normalizationCache.set(n,""),""):(this.stemmer&&!this.stemmerSkipProperties.has(e)&&(t=this.stemmer(t)),t=function(e){let t=[];for(let n=0;n<e.length;n++){var r;t[n]=(r=e.charCodeAt(n))<192||r>383?r:o[r-192]||r}return String.fromCharCode(...t)}(t),r&&this.normalizationCache.set(n,t),t)}function y(e,t,r,o=!0){let l;if(t&&t!==this.language)throw(0,n.$)("LANGUAGE_NOT_SUPPORTED",t);if("string"!=typeof e)return[e];let a=this.normalizeToken.bind(this,r??"");if(r&&this.tokenizeSkipProperties.has(r))l=[a(e,o)];else{let t=i.DO[this.language];l=e.toLowerCase().split(t).map(e=>a(e,o)).filter(Boolean)}let s=function(e){for(;""===e[e.length-1];)e.pop();for(;""===e[0];)e.shift();return e}(l);return this.allowDuplicates?s:Array.from(new Set(s))}function b(e={}){let t,r;if(e.language){if(!i.Fc.includes(e.language))throw(0,n.$)("LANGUAGE_NOT_SUPPORTED",e.language)}else e.language="english";if(e.stemming||e.stemmer&&!("stemming"in e))if(e.stemmer){if("function"!=typeof e.stemmer)throw(0,n.$)("INVALID_STEMMER_FUNCTION_TYPE");t=e.stemmer}else if("english"===e.language)t=m;else throw(0,n.$)("MISSING_STEMMER",e.language);if(!1!==e.stopWords){if(r=[],Array.isArray(e.stopWords))r=e.stopWords;else if("function"==typeof e.stopWords)r=e.stopWords(r);else if(e.stopWords)throw(0,n.$)("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");if(!Array.isArray(r))throw(0,n.$)("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY");for(let e of r)if("string"!=typeof e)throw(0,n.$)("CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY")}let o={tokenize:y,language:e.language,stemmer:t,stemmerSkipProperties:new Set(e.stemmerSkipProperties?[e.stemmerSkipProperties].flat():[]),tokenizeSkipProperties:new Set(e.tokenizeSkipProperties?[e.tokenizeSkipProperties].flat():[]),stopWords:r,allowDuplicates:!!e.allowDuplicates,normalizeToken:g,normalizationCache:new Map};return o.tokenize=y.bind(o),o.normalizeToken=g,o}},3514:(e,t,r)=>{r.d(t,{$J:()=>y,$S:()=>function e(t){return Array.isArray(t)?t.some(t=>e(t)):t?.constructor?.name==="AsyncFunction"},He:()=>p,JN:()=>v,JW:()=>I,NF:()=>m,O6:()=>x,g5:()=>g,gG:()=>k,h:()=>c,iR:()=>T,j7:()=>h,nv:()=>f,wH:()=>w,y$:()=>b});var n=r(9838),o=r(9509);let i=Date.now().toString().slice(5),l=0,a=BigInt(1e3),s=BigInt(1e6),u=BigInt(1e9);function c(e,t){if(t.length<65535)Array.prototype.push.apply(e,t);else{let r=t.length;for(let n=0;n<r;n+=65535)Array.prototype.push.apply(e,t.slice(n,n+65535))}}function f(e,...t){return e.replace(/%(?:(?<position>\d+)\$)?(?<width>-?\d*\.?\d*)(?<type>[dfs])/g,function(...e){let{width:r,type:n,position:o}=e[e.length-1],i=o?t[Number.parseInt(o)-1]:t.shift(),l=""===r?0:Number.parseInt(r);switch(n){case"d":return i.toString().padStart(l,"0");case"f":{let e=i,[t,n]=r.split(".").map(e=>Number.parseFloat(e));return"number"==typeof n&&n>=0&&(e=e.toFixed(n)),"number"==typeof t&&t>=0?e.toString().padStart(l,"0"):e.toString()}case"s":return l<0?i.toString().padEnd(-l," "):i.toString().padStart(l," ");default:return i}})}function d(){return BigInt(Math.floor(1e6*performance.now()))}function h(e){return("number"==typeof e&&(e=BigInt(e)),e<a)?`${e}ns`:e<s?`${e/a}μs`:e<u?`${e/s}ms`:`${e/u}s`}function p(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?d():void 0!==o&&o.release&&"node"===o.release.name||void 0!==o&&"function"==typeof o?.hrtime?.bigint?o.hrtime.bigint():"undefined"!=typeof performance?d():BigInt(0)}function m(){return`${i}-${l++}`}function g(e,t){return void 0===Object.hasOwn?Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0:Object.hasOwn(e,t)?e[t]:void 0}function y(e,t){return t[1]===e[1]?e[0]-t[0]:t[1]-e[1]}function b(e){if(0===e.length)return[];if(1===e.length)return e[0];for(let t=1;t<e.length;t++)if(e[t].length<e[0].length){let r=e[0];e[0]=e[t],e[t]=r}let t=new Map;for(let r of e[0])t.set(r,1);for(let r=1;r<e.length;r++){let n=0;for(let o of e[r]){let e=t.get(o);e===r&&(t.set(o,e+1),n++)}if(0===n)return[]}return e[0].filter(r=>{let n=t.get(r);return void 0!==n&&t.set(r,0),n===e.length})}function v(e,t){let r={},n=t.length;for(let o=0;o<n;o++){let n=t[o],i=n.split("."),l=e,a=i.length;for(let e=0;e<a;e++)if("object"==typeof(l=l[i[e]])){if(null!==l&&"lat"in l&&"lon"in l&&"number"==typeof l.lat&&"number"==typeof l.lon){l=r[n]=l;break}else if(!Array.isArray(l)&&null!==l&&e===a-1){l=void 0;break}}else if((null===l||"object"!=typeof l)&&e<a-1){l=void 0;break}void 0!==l&&(r[n]=l)}return r}function w(e,t){return v(e,[t])[t]}let S={cm:.01,m:1,km:1e3,ft:.3048,yd:.9144,mi:1609.344};function x(e,t){let r=S[t];if(void 0===r)throw Error((0,n.$)("INVALID_DISTANCE_SUFFIX",e).message);return e*r}function I(e,t){e.hits=e.hits.map(e=>({...e,document:{...e.document,...t.reduce((e,t)=>{let r=t.split("."),n=r.pop(),o=e;for(let e of r)o[e]=o[e]??{},o=o[e];return o[n]=null,e},e.document)}}))}let A="intersection"in new Set;function T(...e){if(0===e.length)return new Set;if(1===e.length)return e[0];if(2===e.length){let t=e[0],r=e[1];if(A)return t.intersection(r);let n=new Set,o=t.size<r.size?t:r,i=o===t?r:t;for(let e of o)i.has(e)&&n.add(e);return n}let t={index:0,size:e[0].size};for(let r=1;r<e.length;r++)e[r].size<t.size&&(t.index=r,t.size=e[r].size);if(A){let r=e[t.index];for(let n=0;n<e.length;n++)n!==t.index&&(r=r.intersection(e[n]));return r}let r=e[t.index];for(let n=0;n<e.length;n++){if(n===t.index)continue;let o=e[n];for(let e of r)o.has(e)||r.delete(e)}return r}let O="union"in new Set;function k(e,t){return O?e?e.union(t):t:new Set(e?[...e,...t]:t)}},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4657:(e,t,r)=>{r.d(t,{BaseLinkItem:()=>s});var n=r(5155),o=r(9949),i=r(344),l=r(2115),a=r(5455);let s=(0,l.forwardRef)((e,t)=>{var r;let{item:l,...s}=e,u=(0,i.a8)(),c=null!=(r=l.active)?r:"url",f="none"!==c&&(0,a.$)(l.url,u,"nested-url"===c);return(0,n.jsx)(o.default,{ref:t,href:l.url,external:l.external,...s,"data-active":f,children:s.children})});s.displayName="BaseLinkItem"},5040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5403:(e,t,r)=>{r.d(t,{LargeSearchToggle:()=>c,SearchToggle:()=>u});var n=r(5155),o=r(7543),i=r(8169),l=r(9697),a=r(9688),s=r(7936);function u(e){let{hideIfDisabled:t,size:r="icon",color:l="ghost",...u}=e,{setOpenSearch:c,enabled:f}=(0,i.$A)();return t&&!f?null:(0,n.jsx)("button",{type:"button",className:(0,a.QP)((0,s.r)({size:r,color:l}),u.className),"data-search":"","aria-label":"Open Search",onClick:()=>{c(!0)},children:(0,n.jsx)(o.A,{className:"p-px"})})}function c(e){let{hideIfDisabled:t,...r}=e,{enabled:s,hotKey:u,setOpenSearch:c}=(0,i.$A)(),{text:f}=(0,l.s9)();return t&&!s?null:(0,n.jsxs)("button",{type:"button","data-search-full":"",...r,className:(0,a.QP)("inline-flex items-center gap-2 rounded-full border bg-fd-secondary/50 p-1.5 text-sm text-fd-muted-foreground transition-colors hover:bg-fd-accent hover:text-fd-accent-foreground",r.className),onClick:()=>{c(!0)},children:[(0,n.jsx)(o.A,{className:"ms-1 size-4"}),f.search,(0,n.jsx)("div",{className:"ms-auto inline-flex gap-0.5",children:u.map((e,t)=>(0,n.jsx)("kbd",{className:"rounded-md border bg-fd-background px-1.5",children:e.display},t))})]})}},5455:(e,t,r)=>{r.d(t,{$:()=>n});function n(e,t,r=!0){return e.endsWith("/")&&(e=e.slice(0,-1)),t.endsWith("/")&&(t=t.slice(0,-1)),e===t||r&&t.startsWith(`${e}/`)}},6972:(e,t,r)=>{r.d(t,{vt:()=>ey,$P:()=>ej});var n=r(9838),o=r(3514);function i(e){return{raw:Number(e),formatted:(0,o.j7)(e)}}function l(e){if(e.id){if("string"!=typeof e.id)throw(0,n.$)("DOCUMENT_ID_MUST_BE_STRING",typeof e.id);return e.id}return(0,o.NF)()}let a={string:!1,number:!1,boolean:!1,enum:!1,geopoint:!1,"string[]":!0,"number[]":!0,"boolean[]":!0,"enum[]":!0},s={"string[]":"string","number[]":"number","boolean[]":"boolean","enum[]":"enum"};function u(e){return"string"==typeof e&&/^vector\[\d+\]$/.test(e)}function c(e){return"string"==typeof e&&a[e]}function f(e){let t=Number(e.slice(7,-1));switch(!0){case isNaN(t):throw(0,n.$)("INVALID_VECTOR_VALUE",e);case t<=0:throw(0,n.$)("INVALID_VECTOR_SIZE",e);default:return t}}function d(e){return{internalIdToId:e.internalIdToId}}function h(e,t){let{internalIdToId:r}=t;e.internalDocumentIDStore.idToInternalId.clear(),e.internalDocumentIDStore.internalIdToId=[];let n=r.length;for(let t=0;t<n;t++){let n=r[t];e.internalDocumentIDStore.idToInternalId.set(n,t+1),e.internalDocumentIDStore.internalIdToId.push(n)}}function p(e,t){if("string"==typeof t){let r=e.idToInternalId.get(t);if(r)return r;let n=e.idToInternalId.size+1;return e.idToInternalId.set(t,n),e.internalIdToId.push(t),n}return t>e.internalIdToId.length?p(e,t.toString()):t}function m(e,t){if(e.internalIdToId.length<t)throw Error(`Invalid internalId ${t}`);return e.internalIdToId[t-1]}function g(e,t){return{sharedInternalDocumentStore:t,docs:{},count:0}}function y(e,t){let r=p(e.sharedInternalDocumentStore,t);return e.docs[r]}function b(e,t){let r=t.length,n=Array.from({length:r});for(let o=0;o<r;o++){let r=p(e.sharedInternalDocumentStore,t[o]);n[o]=e.docs[r]}return n}function v(e){return e.docs}function w(e,t,r,n){return void 0===e.docs[r]&&(e.docs[r]=n,e.count++,!0)}function S(e,t){let r=p(e.sharedInternalDocumentStore,t);return void 0!==e.docs[r]&&(delete e.docs[r],e.count--,!0)}function x(e){return e.count}function I(e,t){return{docs:t.docs,count:t.count,sharedInternalDocumentStore:e}}function A(e){return{docs:e.docs,count:e.count}}let T=["beforeInsert","afterInsert","beforeRemove","afterRemove","beforeUpdate","afterUpdate","beforeSearch","afterSearch","beforeInsertMultiple","afterInsertMultiple","beforeRemoveMultiple","afterRemoveMultiple","beforeUpdateMultiple","afterUpdateMultiple","beforeLoad","afterLoad","afterCreate"],O=["tokenizer","index","documentsStore","sorter"],k=["validateSchema","getDocumentIndexId","getDocumentProperties","formatElapsedTime"];function N(e,t,r,n,i){if(e.some(o.$S))return(async()=>{for(let o of e)await o(t,r,n,i)})();for(let o of e)o(t,r,n,i)}function D(e,t,r,n){if(e.some(o.$S))return(async()=>{for(let o of e)await o(t,r,n)})();for(let o of e)o(t,r,n)}class E{k;v;l=null;r=null;h=1;constructor(e,t){this.k=e,this.v=new Set(t)}updateHeight(){this.h=Math.max(E.getHeight(this.l),E.getHeight(this.r))+1}static getHeight(e){return e?e.h:0}getBalanceFactor(){return E.getHeight(this.l)-E.getHeight(this.r)}rotateLeft(){let e=this.r;return this.r=e.l,e.l=this,this.updateHeight(),e.updateHeight(),e}rotateRight(){let e=this.l;return this.l=e.r,e.r=this,this.updateHeight(),e.updateHeight(),e}toJSON(){return{k:this.k,v:Array.from(this.v),l:this.l?this.l.toJSON():null,r:this.r?this.r.toJSON():null,h:this.h}}static fromJSON(e){let t=new E(e.k,e.v);return t.l=e.l?E.fromJSON(e.l):null,t.r=e.r?E.fromJSON(e.r):null,t.h=e.h,t}}class P{root=null;insertCount=0;constructor(e,t){void 0!==e&&void 0!==t&&(this.root=new E(e,t))}insert(e,t,r=1e3){this.root=this.insertNode(this.root,e,t,r)}insertMultiple(e,t,r=1e3){for(let n of t)this.insert(e,n,r)}rebalance(){this.root&&(this.root=this.rebalanceNode(this.root))}toJSON(){return{root:this.root?this.root.toJSON():null,insertCount:this.insertCount}}static fromJSON(e){let t=new P;return t.root=e.root?E.fromJSON(e.root):null,t.insertCount=e.insertCount||0,t}insertNode(e,t,r,n){if(null===e)return new E(t,[r]);let o=[],i=e,l=null;for(;null!==i;)if(o.push({parent:l,node:i}),t<i.k)if(null===i.l){i.l=new E(t,[r]),o.push({parent:i,node:i.l});break}else l=i,i=i.l;else if(!(t>i.k))return i.v.add(r),e;else if(null===i.r){i.r=new E(t,[r]),o.push({parent:i,node:i.r});break}else l=i,i=i.r;let a=!1;this.insertCount++%n==0&&(a=!0);for(let t=o.length-1;t>=0;t--){let{parent:r,node:n}=o[t];if(n.updateHeight(),a){let t=this.rebalanceNode(n);r?r.l===n?r.l=t:r.r===n&&(r.r=t):e=t}}return e}rebalanceNode(e){let t=e.getBalanceFactor();if(t>1){if(e.l&&e.l.getBalanceFactor()>=0)return e.rotateRight();else if(e.l)return e.l=e.l.rotateLeft(),e.rotateRight()}if(t<-1){if(e.r&&0>=e.r.getBalanceFactor())return e.rotateLeft();else if(e.r)return e.r=e.r.rotateRight(),e.rotateLeft()}return e}find(e){let t=this.findNodeByKey(e);return t?t.v:null}contains(e){return null!==this.find(e)}getSize(){let e=0,t=[],r=this.root;for(;r||t.length>0;){for(;r;)t.push(r),r=r.l;r=t.pop(),e++,r=r.r}return e}isBalanced(){if(!this.root)return!0;let e=[this.root];for(;e.length>0;){let t=e.pop();if(Math.abs(t.getBalanceFactor())>1)return!1;t.l&&e.push(t.l),t.r&&e.push(t.r)}return!0}remove(e){this.root=this.removeNode(this.root,e)}removeDocument(e,t){let r=this.findNodeByKey(e);r&&(1===r.v.size?this.root=this.removeNode(this.root,e):r.v=new Set([...r.v.values()].filter(e=>e!==t)))}findNodeByKey(e){let t=this.root;for(;t;)if(e<t.k)t=t.l;else{if(!(e>t.k))return t;t=t.r}return null}removeNode(e,t){if(null===e)return null;let r=[],n=e;for(;null!==n&&n.k!==t;)r.push(n),n=t<n.k?n.l:n.r;if(null===n)return e;if(null===n.l||null===n.r){let t=n.l?n.l:n.r;if(0===r.length)e=t;else{let e=r[r.length-1];e.l===n?e.l=t:e.r=t}}else{let e=n,t=n.r;for(;null!==t.l;)e=t,t=t.l;n.k=t.k,n.v=t.v,e.l===t?e.l=t.r:e.r=t.r,n=e}r.push(n);for(let t=r.length-1;t>=0;t--){let n=r[t];n.updateHeight();let o=this.rebalanceNode(n);if(t>0){let e=r[t-1];e.l===n?e.l=o:e.r===n&&(e.r=o)}else e=o}return e}rangeSearch(e,t){let r=new Set,n=[],i=this.root;for(;i||n.length>0;){for(;i;)n.push(i),i=i.l;if((i=n.pop()).k>=e&&i.k<=t&&(r=(0,o.gG)(r,i.v)),i.k>t)break;i=i.r}return r}greaterThan(e,t=!1){let r=new Set,n=[],i=this.root;for(;i||n.length>0;){for(;i;)n.push(i),i=i.r;if(i=n.pop(),t&&i.k>=e||!t&&i.k>e)r=(0,o.gG)(r,i.v);else if(i.k<=e)break;i=i.l}return r}lessThan(e,t=!1){let r=new Set,n=[],i=this.root;for(;i||n.length>0;){for(;i;)n.push(i),i=i.l;if(i=n.pop(),t&&i.k<=e||!t&&i.k<e)r=(0,o.gG)(r,i.v);else if(i.k>e)break;i=i.r}return r}}class _{numberToDocumentId;constructor(){this.numberToDocumentId=new Map}insert(e,t){this.numberToDocumentId.has(e)?this.numberToDocumentId.get(e).add(t):this.numberToDocumentId.set(e,new Set([t]))}find(e){let t=this.numberToDocumentId.get(e);return t?Array.from(t):null}remove(e){this.numberToDocumentId.delete(e)}removeDocument(e,t){let r=this.numberToDocumentId.get(t);r&&(r.delete(e),0===r.size&&this.numberToDocumentId.delete(t))}contains(e){return this.numberToDocumentId.has(e)}getSize(){let e=0;for(let t of this.numberToDocumentId.values())e+=t.size;return e}filter(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"eq":{let t=e[r],n=this.numberToDocumentId.get(t);return n?Array.from(n):[]}case"in":{let t=e[r],n=new Set;for(let e of t){let t=this.numberToDocumentId.get(e);if(t)for(let e of t)n.add(e)}return Array.from(n)}case"nin":{let t=new Set(e[r]),n=new Set;for(let[e,r]of this.numberToDocumentId.entries())if(!t.has(e))for(let e of r)n.add(e);return Array.from(n)}default:throw Error("Invalid operation")}}filterArr(e){let t=Object.keys(e);if(1!==t.length)throw Error("Invalid operation");let r=t[0];switch(r){case"containsAll":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e].filter(e=>t.has(e)))))}case"containsAny":{let t=e[r].map(e=>this.numberToDocumentId.get(e)??new Set);if(0===t.length)return[];return Array.from(t.reduce((e,t)=>new Set([...e,...t])))}default:throw Error("Invalid operation")}}static fromJSON(e){if(!e.numberToDocumentId)throw Error("Invalid Flat Tree JSON");let t=new _;for(let[r,n]of e.numberToDocumentId)t.numberToDocumentId.set(r,new Set(n));return t}toJSON(){return{numberToDocumentId:Array.from(this.numberToDocumentId.entries()).map(([e,t])=>[e,Array.from(t)])}}}function R(e,t,r){let n=function(e,t,r){if(r<0)return -1;if(e===t)return 0;let n=e.length,o=t.length;if(0===n)return o<=r?o:-1;if(0===o)return n<=r?n:-1;let i=Math.abs(n-o);if(e.startsWith(t))return i<=r?i:-1;if(t.startsWith(e))return 0;if(i>r)return -1;let l=[];for(let e=0;e<=n;e++){l[e]=[e];for(let t=1;t<=o;t++)l[e][t]=0===e?t:0}for(let i=1;i<=n;i++){let n=1/0;for(let r=1;r<=o;r++)e[i-1]===t[r-1]?l[i][r]=l[i-1][r-1]:l[i][r]=Math.min(l[i-1][r]+1,l[i][r-1]+1,l[i-1][r-1]+1),n=Math.min(n,l[i][r]);if(n>r)return -1}return l[n][o]<=r?l[n][o]:-1}(e,t,r);return{distance:n,isBounded:n>=0}}class C{k;s;c=new Map;d=new Set;e;w="";constructor(e,t,r){this.k=e,this.s=t,this.e=r}updateParent(e){this.w=e.w+this.s}addDocument(e){this.d.add(e)}removeDocument(e){return this.d.delete(e)}findAllWords(e,t,r,n){let i=[this];for(;i.length>0;){let l=i.pop();if(l.e){let{w:i,d:a}=l;if(r&&i!==t)continue;if(null!==(0,o.g5)(e,i))if(n){if(!(Math.abs(t.length-i.length)<=n)||!R(t,i,n).isBounded)continue;e[i]=[]}else e[i]=[];if(null!=(0,o.g5)(e,i)&&a.size>0){let t=e[i];for(let e of a)t.includes(e)||t.push(e)}}l.c.size>0&&i.push(...l.c.values())}return e}insert(e,t){let r=this,n=0,o=e.length;for(;n<o;){let i=e[n],l=r.c.get(i);if(l){let i=l.s,a=i.length,s=0;for(;s<a&&n+s<o&&i[s]===e[n+s];)s++;if(s===a){if(r=l,(n+=s)===o){l.e||(l.e=!0),l.addDocument(t);return}continue}let u=i.slice(0,s),c=i.slice(s),f=e.slice(n+s),d=new C(u[0],u,!1);if(r.c.set(u[0],d),d.updateParent(r),l.s=c,l.k=c[0],d.c.set(c[0],l),l.updateParent(d),f){let e=new C(f[0],f,!0);e.addDocument(t),d.c.set(f[0],e),e.updateParent(d)}else d.e=!0,d.addDocument(t);return}{let o=new C(i,e.slice(n),!0);o.addDocument(t),r.c.set(i,o),o.updateParent(r);return}}r.e||(r.e=!0),r.addDocument(t)}_findLevenshtein(e,t,r,n,i){let l=[{node:this,index:t,tolerance:r}];for(;l.length>0;){let{node:t,index:r,tolerance:a}=l.pop();if(t.w.startsWith(e)){t.findAllWords(i,e,!1,0);continue}if(a<0)continue;if(t.e){let{w:r,d:l}=t;if(r&&(R(e,r,n).isBounded&&(i[r]=[]),void 0!==(0,o.g5)(i,r)&&l.size>0)){let e=new Set(i[r]);for(let t of l)e.add(t);i[r]=Array.from(e)}}if(r>=e.length)continue;let s=e[r];if(t.c.has(s)){let e=t.c.get(s);l.push({node:e,index:r+1,tolerance:a})}for(let[e,n]of(l.push({node:t,index:r+1,tolerance:a-1}),t.c))l.push({node:n,index:r,tolerance:a-1}),e!==s&&l.push({node:n,index:r+1,tolerance:a-1})}}find(e){let{term:t,exact:r,tolerance:n}=e;if(n&&!r){let e={};return this._findLevenshtein(t,0,n,n,e),e}{let e=this,o=0,i=t.length;for(;o<i;){let l=t[o],a=e.c.get(l);if(!a)return{};{let l=a.s,s=l.length,u=0;for(;u<s&&o+u<i&&l[u]===t[o+u];)u++;if(u===s)e=a,o+=u;else{if(o+u!==i||r)return{};let e={};return a.findAllWords(e,t,r,n),e}}}let l={};return e.findAllWords(l,t,r,n),l}}contains(e){let t=this,r=0,n=e.length;for(;r<n;){let o=e[r],i=t.c.get(o);if(!i)return!1;{let o=i.s,l=o.length,a=0;for(;a<l&&r+a<n&&o[a]===e[r+a];)a++;if(a<l)return!1;r+=l,t=i}}return!0}removeWord(e){if(!e)return!1;let t=this,r=e.length,n=[];for(let o=0;o<r;o++){let r=e[o];if(!t.c.has(r))return!1;{let e=t.c.get(r);n.push({parent:t,character:r}),o+=e.s.length-1,t=e}}for(t.d.clear(),t.e=!1;n.length>0&&0===t.c.size&&!t.e&&0===t.d.size;){let{parent:e,character:r}=n.pop();e.c.delete(r),t=e}return!0}removeDocumentByWord(e,t,r=!0){if(!e)return!0;let n=this,o=e.length;for(let i=0;i<o;i++){let o=e[i];if(!n.c.has(o))return!1;{let l=n.c.get(o);i+=l.s.length-1,n=l,r&&n.w!==e||n.removeDocument(t)}}return!0}static getCommonPrefix(e,t){let r=Math.min(e.length,t.length),n=0;for(;n<r&&e.charCodeAt(n)===t.charCodeAt(n);)n++;return e.slice(0,n)}toJSON(){return{w:this.w,s:this.s,e:this.e,k:this.k,d:Array.from(this.d),c:Array.from(this.c?.entries())?.map(([e,t])=>[e,t.toJSON()])}}static fromJSON(e){let t=new C(e.k,e.s,e.e);return t.w=e.w,t.d=new Set(e.d),t.c=new Map(e?.c?.map(([e,t])=>[e,C.fromJSON(t)])),t}}class M extends C{constructor(){super("","",!1)}static fromJSON(e){let t=new M;return t.w=e.w,t.s=e.s,t.e=e.e,t.k=e.k,t.d=new Set(e.d),t.c=new Map(e.c?.map(([e,t])=>[e,C.fromJSON(t)])),t}toJSON(){return super.toJSON()}}class L{point;docIDs;left;right;parent;constructor(e,t){this.point=e,this.docIDs=new Set(t),this.left=null,this.right=null,this.parent=null}toJSON(){return{point:this.point,docIDs:Array.from(this.docIDs),left:this.left?this.left.toJSON():null,right:this.right?this.right.toJSON():null}}static fromJSON(e,t=null){let r=new L(e.point,e.docIDs);return r.parent=t,e.left&&(r.left=L.fromJSON(e.left,r)),e.right&&(r.right=L.fromJSON(e.right,r)),r}}class z{root;nodeMap;constructor(){this.root=null,this.nodeMap=new Map}getPointKey(e){return`${e.lon},${e.lat}`}insert(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);if(n)return void t.forEach(e=>n.docIDs.add(e));let o=new L(e,t);if(this.nodeMap.set(r,o),null==this.root){this.root=o;return}let i=this.root,l=0;for(;;){if(0==l%2)if(e.lon<i.point.lon){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}else if(e.lat<i.point.lat){if(null==i.left){i.left=o,o.parent=i;return}i=i.left}else{if(null==i.right){i.right=o,o.parent=i;return}i=i.right}l++}}contains(e){let t=this.getPointKey(e);return this.nodeMap.has(t)}getDocIDsByCoordinates(e){let t=this.getPointKey(e),r=this.nodeMap.get(t);return r?Array.from(r.docIDs):null}removeDocByID(e,t){let r=this.getPointKey(e),n=this.nodeMap.get(r);n&&(n.docIDs.delete(t),0===n.docIDs.size&&(this.nodeMap.delete(r),this.deleteNode(n)))}deleteNode(e){let t=e.parent,r=e.left?e.left:e.right;r&&(r.parent=t),t?t.left===e?t.left=r:t.right===e&&(t.right=r):(this.root=r,this.root&&(this.root.parent=null))}searchByRadius(e,t,r=!0,n="asc",o=!1){let i=o?z.vincentyDistance:z.haversineDistance,l=[{node:this.root,depth:0}],a=[];for(;l.length>0;){let{node:n,depth:o}=l.pop();if(null==n)continue;let s=i(e,n.point);(r?s<=t:s>t)&&a.push({point:n.point,docIDs:Array.from(n.docIDs)}),null!=n.left&&l.push({node:n.left,depth:o+1}),null!=n.right&&l.push({node:n.right,depth:o+1})}return n&&a.sort((t,r)=>{let o=i(e,t.point),l=i(e,r.point);return"asc"===n.toLowerCase()?o-l:l-o}),a}searchByPolygon(e,t=!0,r=null,n=!1){let o=[{node:this.root,depth:0}],i=[];for(;o.length>0;){let{node:r,depth:n}=o.pop();if(null==r)continue;null!=r.left&&o.push({node:r.left,depth:n+1}),null!=r.right&&o.push({node:r.right,depth:n+1});let l=z.isPointInPolygon(e,r.point);(l&&t||!l&&!t)&&i.push({point:r.point,docIDs:Array.from(r.docIDs)})}let l=z.calculatePolygonCentroid(e);if(r){let e=n?z.vincentyDistance:z.haversineDistance;i.sort((t,n)=>{let o=e(l,t.point),i=e(l,n.point);return"asc"===r.toLowerCase()?o-i:i-o})}return i}toJSON(){return{root:this.root?this.root.toJSON():null}}static fromJSON(e){let t=new z;return e.root&&(t.root=L.fromJSON(e.root),t.buildNodeMap(t.root)),t}buildNodeMap(e){if(null==e)return;let t=this.getPointKey(e.point);this.nodeMap.set(t,e),e.left&&this.buildNodeMap(e.left),e.right&&this.buildNodeMap(e.right)}static calculatePolygonCentroid(e){let t=0,r=0,n=0,o=e.length;for(let i=0,l=o-1;i<o;l=i++){let o=e[i].lon,a=e[i].lat,s=e[l].lon,u=e[l].lat,c=o*u-s*a;t+=c,r+=(o+s)*c,n+=(a+u)*c}let i=6*(t/=2);return{lon:r/=i,lat:n/=i}}static isPointInPolygon(e,t){let r=!1,n=t.lon,o=t.lat,i=e.length;for(let t=0,l=i-1;t<i;l=t++){let i=e[t].lon,a=e[t].lat,s=e[l].lon,u=e[l].lat;a>o!=u>o&&n<(s-i)*(o-a)/(u-a)+i&&(r=!r)}return r}static haversineDistance(e,t){let r=Math.PI/180,n=e.lat*r,o=t.lat*r,i=(t.lat-e.lat)*r,l=(t.lon-e.lon)*r,a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(n)*Math.cos(o)*Math.sin(l/2)*Math.sin(l/2);return 2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a))*6371e3}static vincentyDistance(e,t){let r,n,o,i,l,a,s,u=1/298.257223563,c=(1-1/298.257223563)*6378137,f=Math.PI/180,d=e.lat*f,h=t.lat*f,p=(t.lon-e.lon)*f,m=Math.atan((1-u)*Math.tan(d)),g=Math.atan((1-u)*Math.tan(h)),y=Math.sin(m),b=Math.cos(m),v=Math.sin(g),w=Math.cos(g),S=p,x=1e3;do{let e=Math.sin(S),t=Math.cos(S);if(0===(n=Math.sqrt(w*e*(w*e)+(b*v-y*w*t)*(b*v-y*w*t))))return 0;i=Math.atan2(n,o=y*v+b*w*t),isNaN(s=o-2*y*v/(a=1-(l=b*w*e/n)*l))&&(s=0);let c=u/16*a*(4+u*(4-3*a));r=S,S=p+(1-c)*u*l*(i+c*n*(s+c*o*(-1+2*s*s)))}while(Math.abs(S-r)>1e-12&&--x>0);if(0===x)return NaN;let I=a*(0x24ffb2985f71-c*c)/(c*c),A=1+I/16384*(4096+I*(-768+I*(320-175*I))),T=I/1024*(256+I*(-128+I*(74-47*I)));return c*A*(i-T*n*(s+T/4*(o*(-1+2*s*s)-T/6*s*(-3+4*n*n)*(-3+4*s*s))))}}class j{true;false;constructor(){this.true=new Set,this.false=new Set}insert(e,t){t?this.true.add(e):this.false.add(e)}delete(e,t){t?this.true.delete(e):this.false.delete(e)}getSize(){return this.true.size+this.false.size}toJSON(){return{true:Array.from(this.true),false:Array.from(this.false)}}static fromJSON(e){let t=new j;return t.true=new Set(e.true),t.false=new Set(e.false),t}}class U{size;vectors=new Map;constructor(e){this.size=e}add(e,t){t instanceof Float32Array||(t=new Float32Array(t));let r=$(t,this.size);this.vectors.set(e,[r,t])}remove(e){this.vectors.delete(e)}find(e,t,r){return e instanceof Float32Array||(e=new Float32Array(e)),function(e,t,r,n,o){let i=$(e,n),l=[];for(let a of t||r.keys()){let t=r.get(a);if(!t)continue;let s=t[0],u=t[1],c=0;for(let t=0;t<n;t++)c+=e[t]*u[t];let f=c/(i*s);f>=o&&l.push([a,f])}return l}(e,r,this.vectors,this.size,t)}toJSON(){let e=[];for(let[t,[r,n]]of this.vectors)e.push([t,[r,Array.from(n)]]);return{size:this.size,vectors:e}}static fromJSON(e){let t=new U(e.size);for(let[r,[n,o]]of e.vectors)t.vectors.set(r,[n,new Float32Array(o)]);return t}}function $(e,t){let r=0;for(let n=0;n<t;n++)r+=e[n]*e[n];return Math.sqrt(r)}function F(e,t,r,n,o){let i=p(e.sharedInternalDocumentStore,r);e.avgFieldLength[t]=((e.avgFieldLength[t]??0)*(o-1)+n.length)/o,e.fieldLengths[t][i]=n.length,e.frequencies[t][i]={}}function B(e,t,r,n,o){let i=0;for(let e of n)e===o&&i++;let l=p(e.sharedInternalDocumentStore,r),a=i/n.length;e.frequencies[t][l][o]=a,o in e.tokenOccurrences[t]||(e.tokenOccurrences[t][o]=0),e.tokenOccurrences[t][o]=(e.tokenOccurrences[t][o]??0)+1}function W(e,t,r,n){let o=p(e.sharedInternalDocumentStore,r);n>1?e.avgFieldLength[t]=(e.avgFieldLength[t]*n-e.fieldLengths[t][o])/(n-1):e.avgFieldLength[t]=void 0,e.fieldLengths[t][o]=void 0,e.frequencies[t][o]=void 0}function V(e,t,r){e.tokenOccurrences[t][r]--}function H(e,t,r,n,o,i,l,a,s,f,d){if(u(l)){var h,p,m,g,y;return h=t,p=r,m=i,g=0,y=o,void h.vectorIndexes[p].node.add(y,m)}let b=n=>{let{type:i,node:l}=t.indexes[r];switch(i){case"Bool":l[n?"true":"false"].add(o);break;case"AVL":{let e=d?.avlRebalanceThreshold??1;l.insert(n,o,e);break}case"Radix":{let i=s.tokenize(n,a,r,!1);for(let n of(e.insertDocumentScoreParameters(t,r,o,i,f),i))e.insertTokenScoreParameters(t,r,o,i,n),l.insert(n,o);break}case"Flat":l.insert(n,o);break;case"BKD":l.insert(n,[o])}};if(!c(l))return b(i);let v=i.length;for(let e=0;e<v;e++)b(i[e])}function J(e,t,r,n,o,i,l,a,s,c){if(u(l))return t.vectorIndexes[r].node.remove(o),!0;let{type:f,node:d}=t.indexes[r];switch(f){case"AVL":return d.removeDocument(i,o),!0;case"Bool":return d[i?"true":"false"].delete(o),!0;case"Radix":{let l=s.tokenize(i,a,r);for(let i of(e.removeDocumentScoreParameters(t,r,n,c),l))e.removeTokenScoreParameters(t,r,i),d.removeDocumentByWord(i,o);return!0}case"Flat":return d.removeDocument(o,i),!0;case"BKD":return d.removeDocByID(i,o),!1}}function G(e,t,r,n,o,i,l,a,u,f){if(!c(l))return J(e,t,r,n,o,i,l,a,u,f);let d=s[l],h=i.length;for(let l=0;l<h;l++)J(e,t,r,n,o,i[l],d,a,u,f);return!0}function Y(e,t,r,n,o,i,l,a,s,u){let c=Array.from(n),f=e.avgFieldLength[t],d=e.fieldLengths[t],h=e.tokenOccurrences[t],p=e.frequencies[t],m="number"==typeof h[r]?h[r]??0:0,g=c.length;for(let e=0;e<g;e++){let n=c[e];if(s&&!s.has(n))continue;u.has(n)||u.set(n,new Map);let h=u.get(n);h.set(t,(h.get(t)||0)+1);let g=function(e,t,r,n,o,{k:i,b:l,d:a}){return Math.log(1+(r-t+.5)/(t+.5))*(a+e*(i+1))/(e+i*(1-l+l*n/o))}(p?.[n]?.[r]??0,m,o,d[n],f,i);l.has(n)?l.set(n,l.get(n)+g*a):l.set(n,g*a)}}function K(e,t,r,o,i,l,a,s,u,c,f,d=0){let h=r.tokenize(t,o),p=h.length||1,m=new Map,g=new Map;for(let r of i){if(!(r in e.indexes))continue;let o=e.indexes[r],{type:i}=o;if("Radix"!==i)throw(0,n.$)("WRONG_SEARCH_PROPERTY_TYPE",r);let d=s[r]??1;if(d<=0)throw(0,n.$)("INVALID_BOOST_VALUE",d);0!==h.length||t||h.push(""),function(e,t,r,n,o,i,l,a,s,u,c,f){let d=n.length;for(let h=0;h<d;h++){let d=n[h],p=t.find({term:d,exact:o,tolerance:i}),m=Object.keys(p),g=m.length;for(let t=0;t<g;t++){let n=m[t],o=p[n];Y(e,r,n,o,u,s,l,a,c,f)}}}(e,o.node,r,h,l,a,g,d,u,c,f,m)}let y=Array.from(g.entries()).map(([e,t])=>[e,t]).sort((e,t)=>t[1]-e[1]);if(0===y.length)return[];if(1===d)return y;let b=y.filter(([e])=>{let t=m.get(e);return!!t&&Array.from(t.values()).some(e=>e===p)});if(0===d)return b;if(b.length>0){let e=y.filter(([e])=>!b.some(([t])=>t===e)),t=Math.ceil(e.length*d);return[...b,...e.slice(0,t)]}return y}function q(e,t,r,i){let l=Object.keys(r),a=l.reduce((e,t)=>({[t]:new Set,...e}),{});for(let s of l){let l=r[s];if(void 0===e.indexes[s])throw(0,n.$)("UNKNOWN_FILTER_PROPERTY",s);let{node:u,type:c,isArray:f}=e.indexes[s];if("Bool"===c){let e=l?u.true:u.false;a[s]=(0,o.gG)(a[s],e);continue}if("BKD"===c){let e;if("radius"in l)e="radius";else if("polygon"in l)e="polygon";else throw Error(`Invalid operation ${l}`);if("radius"===e){let{value:t,coordinates:r,unit:n="m",inside:i=!0,highPrecision:c=!1}=l[e],f=(0,o.O6)(t,n),d=u.searchByRadius(r,f,i,void 0,c);a[s]=et(a[s],d)}else{let{coordinates:t,inside:r=!0,highPrecision:n=!1}=l[e],o=u.searchByPolygon(t,r,void 0,n);a[s]=et(a[s],o)}continue}if("Radix"===c&&("string"==typeof l||Array.isArray(l))){for(let e of[l].flat())for(let r of t.tokenize(e,i,s)){let e=u.find({term:r,exact:!0});a[s]=function(e,t){e||(e=new Set);let r=Object.keys(t),n=r.length;for(let o=0;o<n;o++){let n=t[r[o]],i=n.length;for(let t=0;t<i;t++)e.add(n[t])}return e}(a[s],e)}continue}let d=Object.keys(l);if(d.length>1)throw(0,n.$)("INVALID_FILTER_OPERATION",d.length);if("Flat"===c){let e=new Set(f?u.filterArr(l):u.filter(l));a[s]=(0,o.gG)(a[s],e);continue}if("AVL"===c){let e,t=d[0],r=l[t];switch(t){case"gt":e=u.greaterThan(r,!1);break;case"gte":e=u.greaterThan(r,!0);break;case"lt":e=u.lessThan(r,!1);break;case"lte":e=u.lessThan(r,!0);break;case"eq":e=u.find(r)??new Set;break;case"between":{let[t,n]=r;e=u.rangeSearch(t,n);break}default:throw(0,n.$)("INVALID_FILTER_OPERATION",t)}a[s]=(0,o.gG)(a[s],e)}}return(0,o.iR)(...Object.values(a))}function Z(e){return e.searchableProperties}function X(e){return e.searchablePropertiesWithTypes}function Q(e,t){let{indexes:r,vectorIndexes:n,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:l,tokenOccurrences:a,avgFieldLength:s,fieldLengths:u}=t,c={},f={};for(let e of Object.keys(r)){let{node:t,type:n,isArray:o}=r[e];switch(n){case"Radix":c[e]={type:"Radix",node:M.fromJSON(t),isArray:o};break;case"Flat":c[e]={type:"Flat",node:_.fromJSON(t),isArray:o};break;case"AVL":c[e]={type:"AVL",node:P.fromJSON(t),isArray:o};break;case"BKD":c[e]={type:"BKD",node:z.fromJSON(t),isArray:o};break;case"Bool":c[e]={type:"Bool",node:j.fromJSON(t),isArray:o};break;default:c[e]=r[e]}}for(let e of Object.keys(n))f[e]={type:"Vector",isArray:!1,node:U.fromJSON(n[e])};return{sharedInternalDocumentStore:e,indexes:c,vectorIndexes:f,searchableProperties:o,searchablePropertiesWithTypes:i,frequencies:l,tokenOccurrences:a,avgFieldLength:s,fieldLengths:u}}function ee(e){let{indexes:t,vectorIndexes:r,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:l,avgFieldLength:a,fieldLengths:s}=e,u={};for(let e of Object.keys(r))u[e]=r[e].node.toJSON();let c={};for(let e of Object.keys(t)){let{type:r,node:n,isArray:o}=t[e];"Flat"===r||"Radix"===r||"AVL"===r||"BKD"===r||"Bool"===r?c[e]={type:r,node:n.toJSON(),isArray:o}:(c[e]=t[e],c[e].node=c[e].node.toJSON())}return{indexes:c,vectorIndexes:u,searchableProperties:n,searchablePropertiesWithTypes:o,frequencies:i,tokenOccurrences:l,avgFieldLength:a,fieldLengths:s}}function et(e,t){e||(e=new Set);let r=t.length;for(let n=0;n<r;n++){let r=t[n].docIDs,o=r.length;for(let t=0;t<o;t++)e.add(r[t])}return e}var er=r(2359);function en(e,t,r,i){return i?.enabled===!1?{disabled:!0}:function e(t,r,i,l,a){let s={language:t.tokenizer.language,sharedInternalDocumentStore:r,enabled:!0,isSorted:!0,sortableProperties:[],sortablePropertiesWithTypes:{},sorts:{}};for(let[c,f]of Object.entries(i)){let i=`${a}${a?".":""}${c}`;if(!l.includes(i)){if("object"==typeof f&&!Array.isArray(f)){let n=e(t,r,f,l,i);(0,o.h)(s.sortableProperties,n.sortableProperties),s.sorts={...s.sorts,...n.sorts},s.sortablePropertiesWithTypes={...s.sortablePropertiesWithTypes,...n.sortablePropertiesWithTypes};continue}if(!u(f))switch(f){case"boolean":case"number":case"string":s.sortableProperties.push(i),s.sortablePropertiesWithTypes[i]=f,s.sorts[i]={docs:new Map,orderedDocsToRemove:new Map,orderedDocs:[],type:f};break;case"geopoint":case"enum":case"enum[]":case"boolean[]":case"number[]":case"string[]":continue;default:throw(0,n.$)("INVALID_SORT_SCHEMA_TYPE",Array.isArray(f)?"array":f,i)}}}return s}(e,t,r,(i||{}).unsortableProperties||[],"")}function eo(e,t,r,n){if(!e.enabled)return;e.isSorted=!1;let o=p(e.sharedInternalDocumentStore,r),i=e.sorts[t];i.orderedDocsToRemove.has(o)&&eu(e,t),i.docs.set(o,i.orderedDocs.length),i.orderedDocs.push([o,n])}function ei(e){if(!e.isSorted&&e.enabled){for(let t of Object.keys(e.sorts))!function(e,t){let r,n=e.sorts[t];switch(n.type){case"string":r=el.bind(null,e.language);break;case"number":r=ea.bind(null);break;case"boolean":r=es.bind(null)}n.orderedDocs.sort(r);let o=n.orderedDocs.length;for(let e=0;e<o;e++){let t=n.orderedDocs[e][0];n.docs.set(t,e)}}(e,t);e.isSorted=!0}}function el(e,t,r){return t[1].localeCompare(r[1],(0,er.JK)(e))}function ea(e,t){return e[1]-t[1]}function es(e,t){return t[1]?-1:1}function eu(e,t){let r=e.sorts[t];r.orderedDocsToRemove.size&&(r.orderedDocs=r.orderedDocs.filter(e=>!r.orderedDocsToRemove.has(e[0])),r.orderedDocsToRemove.clear())}function ec(e,t,r){if(!e.enabled)return;let n=e.sorts[t],o=p(e.sharedInternalDocumentStore,r);n.docs.get(o)&&(n.docs.delete(o),n.orderedDocsToRemove.set(o,!0))}function ef(e,t,r){if(!e.enabled)throw(0,n.$)("SORT_DISABLED");let o=r.property,i="DESC"===r.order,l=e.sorts[o];if(!l)throw(0,n.$)("UNABLE_TO_SORT_ON_UNKNOWN_FIELD",o,e.sortableProperties.join(", "));return eu(e,o),ei(e),t.sort((t,r)=>{let n=l.docs.get(p(e.sharedInternalDocumentStore,t[0])),o=l.docs.get(p(e.sharedInternalDocumentStore,r[0])),a=void 0!==n,s=void 0!==o;return a||s?a?s?i?o-n:n-o:-1:1:0}),t}function ed(e){return e.enabled?e.sortableProperties:[]}function eh(e){return e.enabled?e.sortablePropertiesWithTypes:{}}function ep(e,t){if(!t.enabled)return{enabled:!1};let r=Object.keys(t.sorts).reduce((e,r)=>{let{docs:n,orderedDocs:o,type:i}=t.sorts[r];return e[r]={docs:new Map(Object.entries(n).map(([e,t])=>[+e,t])),orderedDocsToRemove:new Map,orderedDocs:o,type:i},e},{});return{sharedInternalDocumentStore:e,language:t.language,sortableProperties:t.sortableProperties,sortablePropertiesWithTypes:t.sortablePropertiesWithTypes,sorts:r,enabled:!0,isSorted:t.isSorted}}function em(e){if(!e.enabled)return{enabled:!1};for(let t of Object.keys(e.sorts))eu(e,t);ei(e);let t=Object.keys(e.sorts).reduce((t,r)=>{let{docs:n,orderedDocs:o,type:i}=e.sorts[r];return t[r]={docs:Object.fromEntries(n.entries()),orderedDocs:o,type:i},t},{});return{language:e.language,sortableProperties:e.sortableProperties,sortablePropertiesWithTypes:e.sortablePropertiesWithTypes,sorts:t,enabled:e.enabled,isSorted:e.isSorted}}var eg=r(3460);function ey({schema:e,sort:t,language:r,components:a,id:p,plugins:m}){for(let t of(a||(a={}),m??[])){if(!("getComponents"in t)||"function"!=typeof t.getComponents)continue;let r=t.getComponents(e);for(let e of Object.keys(r))if(a[e])throw(0,n.$)("PLUGIN_COMPONENT_CONFLICT",e,t.name);a={...a,...r}}p||(p=(0,o.NF)());let N=a.tokenizer,D=a.index,E=a.documentsStore,R=a.sorter;if(N=N?N.tokenize?N:(0,eg.e)(N):(0,eg.e)({language:r??"english"}),a.tokenizer&&r)throw(0,n.$)("NO_LANGUAGE_WITH_CUSTOM_TOKENIZER");let C={idToInternalId:new Map,internalIdToId:[],save:d,load:h};D||={create:function e(t,r,o,i,l=""){for(let[a,s]of(i||(i={sharedInternalDocumentStore:r,indexes:{},vectorIndexes:{},searchableProperties:[],searchablePropertiesWithTypes:{},frequencies:{},tokenOccurrences:{},avgFieldLength:{},fieldLengths:{}}),Object.entries(o))){let o=`${l}${l?".":""}${a}`;if("object"==typeof s&&!Array.isArray(s)){e(t,r,s,i,o);continue}if(u(s))i.searchableProperties.push(o),i.searchablePropertiesWithTypes[o]=s,i.vectorIndexes[o]={type:"Vector",node:new U(f(s)),isArray:!1};else{let e=/\[/.test(s);switch(s){case"boolean":case"boolean[]":i.indexes[o]={type:"Bool",node:new j,isArray:e};break;case"number":case"number[]":i.indexes[o]={type:"AVL",node:new P(0,[]),isArray:e};break;case"string":case"string[]":i.indexes[o]={type:"Radix",node:new M,isArray:e},i.avgFieldLength[o]=0,i.frequencies[o]={},i.tokenOccurrences[o]={},i.fieldLengths[o]={};break;case"enum":case"enum[]":i.indexes[o]={type:"Flat",node:new _,isArray:e};break;case"geopoint":i.indexes[o]={type:"BKD",node:new z,isArray:e};break;default:throw(0,n.$)("INVALID_SCHEMA_TYPE",Array.isArray(s)?"array":s,o)}i.searchableProperties.push(o),i.searchablePropertiesWithTypes[o]=s}}return i},insert:H,remove:G,insertDocumentScoreParameters:F,insertTokenScoreParameters:B,removeDocumentScoreParameters:W,removeTokenScoreParameters:V,calculateResultScores:Y,search:K,searchByWhereClause:q,getSearchableProperties:Z,getSearchablePropertiesWithTypes:X,load:Q,save:ee},R||={create:en,insert:eo,remove:ec,save:em,load:ep,sortBy:ef,getSortableProperties:ed,getSortablePropertiesWithTypes:eh},E||={create:g,get:y,getMultiple:b,getAll:v,store:w,remove:S,count:x,load:I,save:A};var L=a;let $={formatElapsedTime:i,getDocumentIndexId:l,getDocumentProperties:o.JN,validateSchema:function e(t,r){for(let[o,i]of Object.entries(r)){let r=t[o];if(void 0!==r&&("geopoint"!==i||"object"!=typeof r||"number"!=typeof r.lon||"number"!=typeof r.lat)&&("enum"!==i||"string"!=typeof r&&"number"!=typeof r)){if("enum[]"===i&&Array.isArray(r)){let e=r.length;for(let t=0;t<e;t++)if("string"!=typeof r[t]&&"number"!=typeof r[t])return o+"."+t;continue}if(u(i)){let e=f(i);if(!Array.isArray(r)||r.length!==e)throw(0,n.$)("INVALID_INPUT_VECTOR",o,e,r.length);continue}if(c(i)){if(!Array.isArray(r))return o;let e=s[i],t=r.length;for(let n=0;n<t;n++)if(typeof r[n]!==e)return o+"."+n;continue}if("object"==typeof i){if(!r||"object"!=typeof r)return o;let t=e(r,i);if(t)return o+"."+t;continue}if(typeof r!==i)return o}}}};for(let e of k)if(L[e]){if("function"!=typeof L[e])throw(0,n.$)("COMPONENT_MUST_BE_FUNCTION",e)}else L[e]=$[e];for(let e of Object.keys(L))if(!O.includes(e)&&!k.includes(e))throw(0,n.$)("UNSUPPORTED_COMPONENT",e);let{getDocumentProperties:J,getDocumentIndexId:et,validateSchema:er,formatElapsedTime:ei}=a,el={data:{},caches:{},schema:e,tokenizer:N,index:D,sorter:R,documentsStore:E,internalDocumentIDStore:C,getDocumentProperties:J,getDocumentIndexId:et,validateSchema:er,beforeInsert:[],afterInsert:[],beforeRemove:[],afterRemove:[],beforeUpdate:[],afterUpdate:[],beforeSearch:[],afterSearch:[],beforeInsertMultiple:[],afterInsertMultiple:[],beforeRemoveMultiple:[],afterRemoveMultiple:[],afterUpdateMultiple:[],beforeUpdateMultiple:[],afterCreate:[],formatElapsedTime:ei,id:p,plugins:m,version:"{{VERSION}}"};for(let r of(el.data={index:el.index.create(el,C,e),docs:el.documentsStore.create(el,C),sorting:el.sorter.create(el,C,e,t)},T))el[r]=(el[r]??[]).concat(function(e,t){let r=[],o=e.plugins?.length;if(!o)return r;for(let i=0;i<o;i++)try{let n=e.plugins[i];"function"==typeof n[t]&&r.push(n[t])}catch(e){throw console.error("Caught error in getAllPluginsByHook:",e),(0,n.$)("PLUGIN_CRASHED")}return r}(el,r));let ea=el.afterCreate;return ea&&function(e,t){if(e.some(o.$S))return(async()=>{for(let r of e)await r(t)})();for(let r of e)r(t)}(ea,el),el}function eb(e,t,r,n,o){let i=e.validateSchema(t,e.schema);if(i)throw createError("SCHEMA_VALIDATION_FAILURE",i);return isAsyncFunction(e.beforeInsert)||isAsyncFunction(e.afterInsert)||isAsyncFunction(e.index.beforeInsert)||isAsyncFunction(e.index.insert)||isAsyncFunction(e.index.afterInsert)?eS(e,t,r,n,o):function(e,t,r,n,o){let{index:i,docs:l}=e.data,a=e.getDocumentIndexId(t);if("string"!=typeof a)throw createError("DOCUMENT_ID_MUST_BE_STRING",typeof a);let s=getInternalDocumentId(e.internalDocumentIDStore,a);if(!e.documentsStore.store(l,a,s,t))throw createError("DOCUMENT_ALREADY_EXISTS",a);let u=e.documentsStore.count(l);n||runSingleHook(e.beforeInsert,e,a,t);let c=e.index.getSearchableProperties(i),f=e.index.getSearchablePropertiesWithTypes(i),d=e.getDocumentProperties(t,c);for(let[e,t]of Object.entries(d))void 0!==t&&ex(typeof t,f[e],e,t);return function(e,t,r,n,o,i,l,a){for(let l of r){let r=n[l];if(void 0===r)continue;let s=e.index.getSearchablePropertiesWithTypes(e.data.index)[l],u=getInternalDocumentId(e.internalDocumentIDStore,t);e.index.beforeInsert?.(e.data.index,l,t,r,s,i,e.tokenizer,o),e.index.insert(e.index,e.data.index,l,t,u,r,s,i,e.tokenizer,o,a),e.index.afterInsert?.(e.data.index,l,t,r,s,i,e.tokenizer,o)}let s=e.sorter.getSortableProperties(e.data.sorting),u=e.getDocumentProperties(l,s);for(let r of s){let n=u[r];if(void 0===n)continue;let o=e.sorter.getSortablePropertiesWithTypes(e.data.sorting)[r];e.sorter.insert(e.data.sorting,r,t,n,o,i)}}(e,a,c,d,u,r,t,o),n||runSingleHook(e.afterInsert,e,a,t),trackInsertion(e),a}(e,t,r,n,o)}Symbol("orama.insertions"),Symbol("orama.removals"),globalThis.process?.emitWarning;let ev=new Set(["enum","enum[]"]),ew=new Set(["string","number"]);async function eS(e,t,r,n,o){let{index:i,docs:l}=e.data,a=e.getDocumentIndexId(t);if("string"!=typeof a)throw createError("DOCUMENT_ID_MUST_BE_STRING",typeof a);let s=getInternalDocumentId(e.internalDocumentIDStore,a);if(!e.documentsStore.store(l,a,s,t))throw createError("DOCUMENT_ALREADY_EXISTS",a);let u=e.documentsStore.count(l);n||await runSingleHook(e.beforeInsert,e,a,t);let c=e.index.getSearchableProperties(i),f=e.index.getSearchablePropertiesWithTypes(i),d=e.getDocumentProperties(t,c);for(let[e,t]of Object.entries(d))void 0!==t&&ex(typeof t,f[e],e,t);return await eI(e,a,c,d,u,r,t,o),n||await runSingleHook(e.afterInsert,e,a,t),trackInsertion(e),a}function ex(e,t,r,n){if(!(isGeoPointType(t)&&"object"==typeof n&&"number"==typeof n.lon&&"number"==typeof n.lat||isVectorType(t)&&Array.isArray(n)||isArrayType(t)&&Array.isArray(n))&&!(ev.has(t)&&ew.has(e))&&e!==t)throw createError("INVALID_DOCUMENT_PROPERTY",r,t,e)}async function eI(e,t,r,n,o,i,l,a){for(let l of r){let r=n[l];if(void 0===r)continue;let s=e.index.getSearchablePropertiesWithTypes(e.data.index)[l];await e.index.beforeInsert?.(e.data.index,l,t,r,s,i,e.tokenizer,o);let u=e.internalDocumentIDStore.idToInternalId.get(t);await e.index.insert(e.index,e.data.index,l,t,u,r,s,i,e.tokenizer,o,a),await e.index.afterInsert?.(e.data.index,l,t,r,s,i,e.tokenizer,o)}let s=e.sorter.getSortableProperties(e.data.sorting),u=e.getDocumentProperties(l,s);for(let r of s){let n=u[r];if(void 0===n)continue;let o=e.sorter.getSortablePropertiesWithTypes(e.data.sorting)[r];e.sorter.insert(e.data.sorting,r,t,n,o,i)}}async function eA(e,t,r,n){let o=!0,{index:i,docs:l}=e.data,a=e.documentsStore.get(l,t);if(!a)return!1;let s=getInternalDocumentId(e.internalDocumentIDStore,t),u=getDocumentIdFromInternalId(e.internalDocumentIDStore,s),c=e.documentsStore.count(l);n||await runSingleHook(e.beforeRemove,e,u);let f=e.index.getSearchableProperties(i),d=e.index.getSearchablePropertiesWithTypes(i),h=e.getDocumentProperties(a,f);for(let n of f){let i=h[n];if(void 0===i)continue;let l=d[n];await e.index.beforeRemove?.(e.data.index,n,u,i,l,r,e.tokenizer,c),await e.index.remove(e.index,e.data.index,n,t,s,i,l,r,e.tokenizer,c)||(o=!1),await e.index.afterRemove?.(e.data.index,n,u,i,l,r,e.tokenizer,c)}let p=await e.sorter.getSortableProperties(e.data.sorting),m=await e.getDocumentProperties(a,p);for(let r of p)void 0!==m[r]&&e.sorter.remove(e.data.sorting,r,t);return n||await runSingleHook(e.afterRemove,e,u),e.documentsStore.remove(e.data.docs,t,s),trackRemoval(e),o}let eT="fulltext";function eO(e,t){return e[1]-t[1]}function ek(e,t){return t[1]-e[1]}function eN(e,t,r){let i={},l=t.map(([e])=>e),a=e.documentsStore.getMultiple(e.data.docs,l),s=Object.keys(r),u=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e of s){let t;if("number"===u[e]){let{ranges:n}=r[e],o=n.length,i=Array.from({length:o});for(let e=0;e<o;e++){let t=n[e];i[e]=[`${t.from}-${t.to}`,0]}t=Object.fromEntries(i)}i[e]={count:0,values:t??{}}}let c=a.length;for(let e=0;e<c;e++){let t=a[e];for(let e of s){let l=e.includes(".")?(0,o.wH)(t,e):t[e],a=u[e],s=i[e].values;switch(a){case"number":eD(r[e].ranges,s)(l);break;case"number[]":{let t=new Set,n=eD(r[e].ranges,s,t);for(let e of l)n(e);break}case"boolean":case"enum":case"string":eE(s,a)(l);break;case"boolean[]":case"enum[]":case"string[]":{let e=eE(s,"boolean[]"===a?"boolean":"string",new Set);for(let t of l)e(t);break}default:throw(0,n.$)("FACET_NOT_SUPPORTED",a)}}}for(let e of s){let t=i[e];if(t.count=Object.keys(t.values).length,"string"===u[e]){let n=r[e],o=function(e="desc"){return"asc"===e.toLowerCase()?eO:ek}(n.sort);t.values=Object.fromEntries(Object.entries(t.values).sort(o).slice(n.offset??0,n.limit??10))}}return i}function eD(e,t,r){return n=>{for(let o of e){let e=`${o.from}-${o.to}`;!r?.has(e)&&n>=o.from&&n<=o.to&&(void 0===t[e]?t[e]=1:(t[e]++,r?.add(e)))}}}function eE(e,t,r){let n="boolean"===t?"false":"";return t=>{let o=t?.toString()??n;r?.has(o)||(e[o]=(e[o]??0)+1,r?.add(o))}}let eP={reducer:(e,t,r,n)=>(t[n]=r,t),getInitialValue:e=>Array.from({length:e})},e_=["string","number","boolean"];function eR(e,t,r){let i=r.properties,l=i.length,a=e.index.getSearchablePropertiesWithTypes(e.data.index);for(let e=0;e<l;e++){let t=i[e];if(void 0===a[t])throw(0,n.$)("UNKNOWN_GROUP_BY_PROPERTY",t);if(!e_.includes(a[t]))throw(0,n.$)("INVALID_GROUP_BY_PROPERTY",t,e_.join(", "),a[t])}let s=t.map(([t])=>m(e.internalDocumentIDStore,t)),u=e.documentsStore.getMultiple(e.data.docs,s),c=u.length,f=r.maxResult||Number.MAX_SAFE_INTEGER,d=[],h={};for(let e=0;e<l;e++){let t=i[e],r={property:t,perValue:{}},n=new Set;for(let e=0;e<c;e++){let i=u[e],l=(0,o.wH)(i,t);if(void 0===l)continue;let a="boolean"!=typeof l?l:""+l,s=r.perValue[a]??{indexes:[],count:0};s.count>=f||(s.indexes.push(e),s.count++,r.perValue[a]=s,n.add(l))}d.push(Array.from(n)),h[t]=r}let p=function e(t,r=0){if(r+1===t.length)return t[r].map(e=>[e]);let n=t[r],i=e(t,r+1),l=[];for(let e of n)for(let t of i){let r=[e];(0,o.h)(r,t),l.push(r)}return l}(d),g=p.length,y=[];for(let e=0;e<g;e++){let t=p[e],r=t.length,n={values:[],indexes:[]},l=[];for(let e=0;e<r;e++){let r=t[e],o=i[e];l.push(h[o].perValue["boolean"!=typeof r?r:""+r].indexes),n.values.push(r)}n.indexes=(0,o.y$)(l).sort((e,t)=>e-t),0!==n.indexes.length&&y.push(n)}let b=y.length,v=Array.from({length:b});for(let e=0;e<b;e++){let n=y[e],o=r.reduce||eP,i=n.indexes.map(e=>({id:s[e],score:t[e][1],document:u[e]})),l=o.reducer.bind(null,n.values),a=o.getInitialValue(n.indexes.length),c=i.reduce(l,a);v[e]={values:n.values,result:c}}return v}function eC(e,t,r){let o,i,{term:l,properties:a}=t,s=e.data.index,u=e.caches.propertiesToSearch;if(!u){let t=e.index.getSearchablePropertiesWithTypes(s);u=(u=e.index.getSearchableProperties(s)).filter(e=>t[e].startsWith("string")),e.caches.propertiesToSearch=u}if(a&&"*"!==a){for(let e of a)if(!u.includes(e))throw(0,n.$)("UNKNOWN_INDEX",e,u.join(", "));u=u.filter(e=>a.includes(e))}if(Object.keys(t.where??{}).length>0&&(o=e.index.searchByWhereClause(s,e.tokenizer,t.where,r)),l||a){let n=e.documentsStore.count(e.data.docs);i=e.index.search(s,l||"",e.tokenizer,r,u,t.exact||!1,t.tolerance||0,t.boost||{},function(e){let t=e??{};return t.k=t.k??eM.k,t.b=t.b??eM.b,t.d=t.d??eM.d,t}(t.relevance),n,o,void 0!==t.threshold&&null!==t.threshold?t.threshold:1)}else i=(o?Array.from(o):Object.keys(e.documentsStore.getAll(e.data.docs))).map(e=>[+e,0]);return i}let eM={k:1.2,b:.75,d:.5};function eL(e,t,r){let o,i=t.vector;if(i&&(!("value"in i)||!("property"in i)))throw(0,n.$)("INVALID_VECTOR_INPUT",Object.keys(i).join(", "));let l=e.data.index.vectorIndexes[i.property],a=l.node.size;if(i?.value.length!==a){if(i?.property===void 0||i?.value.length===void 0)throw(0,n.$)("INVALID_INPUT_VECTOR","undefined",a,"undefined");throw(0,n.$)("INVALID_INPUT_VECTOR",i.property,a,i.value.length)}let s=e.data.index;return Object.keys(t.where??{}).length>0&&(o=e.index.searchByWhereClause(s,e.tokenizer,t.where,r)),l.node.find(i.value,t.similarity??.8,o)}function ez(e){return e[1]}function ej(e,t,r){let i=t.mode??eT;if(i===eT){let n=(0,o.He)();function l(){let i,l=Object.keys(e.data.index.vectorIndexes),a=t.facets&&Object.keys(t.facets).length>0,{limit:s=10,offset:u=0,distinctOn:c,includeVectors:f=!1}=t,d=!0===t.preflight,h=eC(e,t,r);if(t.sortBy)if("function"==typeof t.sortBy){let r=h.map(([e])=>e),n=e.documentsStore.getMultiple(e.data.docs,r).map((e,t)=>[h[t][0],h[t][1],e]);n.sort(t.sortBy),h=n.map(([e,t])=>[e,t])}else h=e.sorter.sortBy(e.data.sorting,h,t.sortBy).map(([t,r])=>[p(e.internalDocumentIDStore,t),r]);else h=h.sort(o.$J);d||(i=c?function(e,t,r,n,i){let l=e.data.docs,a=new Map,s=[],u=new Set,c=t.length,f=0;for(let d=0;d<c;d++){let c=t[d];if(void 0===c)continue;let[h,p]=c;if(u.has(h))continue;let g=e.documentsStore.get(l,h),y=(0,o.wH)(g,i);if(!(void 0===y||a.has(y))&&(a.set(y,!0),!(++f<=r)&&(s.push({id:m(e.internalDocumentIDStore,h),score:p,document:g}),u.add(h),f>=r+n)))break}return s}(e,h,u,s,c):eU(e,h,u,s));let g={elapsed:{formatted:"",raw:0},hits:[],count:h.length};return void 0!==i&&(g.hits=i.filter(Boolean),f||(0,o.JW)(g,l)),a&&(g.facets=eN(e,h,t.facets)),t.groupBy&&(g.groups=eR(e,h,t.groupBy)),g.elapsed=e.formatElapsedTime((0,o.He)()-n),g}async function a(){e.beforeSearch&&await D(e.beforeSearch,e,t,r);let n=l();return e.afterSearch&&await N(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?a():l()}if("vector"===i)return function(e,t,r="english"){let n=(0,o.He)();function i(){let i=eL(e,t,r).sort(o.$J),l=[];t.facets&&Object.keys(t.facets).length>0&&(l=eN(e,i,t.facets));let a=t.vector.property,s=t.includeVectors??!1,u=t.limit??10,c=t.offset??0,f=Array.from({length:u});for(let t=0;t<u;t++){let r=i[t+c];if(!r)break;let n=e.data.docs.docs[r[0]];if(n){s||(n[a]=null);let o={id:m(e.internalDocumentIDStore,r[0]),score:r[1],document:n};f[t]=o}}let d=[];t.groupBy&&(d=eR(e,i,t.groupBy));let h=(0,o.He)()-n;return{count:i.length,hits:f.filter(Boolean),elapsed:{raw:Number(h),formatted:(0,o.j7)(h)},...l?{facets:l}:{},...d?{groups:d}:{}}}async function l(){e.beforeSearch&&await D(e.beforeSearch,e,t,r);let n=i();return e.afterSearch&&await N(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?l():i()}(e,t);if("hybrid"===i)return function(e,t,r){let n=(0,o.He)();function i(){let r,i,l=function(e,t,r){let n=function(e){let t=Math.max.apply(Math,e.map(ez));return e.map(([e,r])=>[e,r/t])}(eC(e,t,r)),o=eL(e,t,r),i=t.hybridWeights;return function(e,t,r,n){var o;let i=Math.max.apply(Math,e.map(ez)),l=Math.max.apply(Math,t.map(ez)),{text:a,vector:s}=n&&n.text&&n.vector?n:(o=0,{text:.5,vector:.5}),u=new Map,c=e.length,f=(e,t)=>e*a+t*s;for(let t=0;t<c;t++){let[r,n]=e[t],o=f(n/i,0);u.set(r,o)}let d=t.length;for(let e=0;e<d;e++){let[r,n]=t[e],o=n/l,i=u.get(r)??0;u.set(r,i+f(0,o))}return[...u].sort((e,t)=>t[1]-e[1])}(n,o,t.term??"",i)}(e,t,void 0);t.facets&&Object.keys(t.facets).length>0&&(r=eN(e,l,t.facets)),t.groupBy&&(i=eR(e,l,t.groupBy));let a=eU(e,l,t.offset??0,t.limit??10).filter(Boolean),s=(0,o.He)(),u={count:l.length,elapsed:{raw:Number(s-n),formatted:(0,o.j7)(s-n)},hits:a,...r?{facets:r}:{},...i?{groups:i}:{}};if(!t.includeVectors){let t=Object.keys(e.data.index.vectorIndexes);(0,o.JW)(u,t)}return u}async function l(){e.beforeSearch&&await D(e.beforeSearch,e,t,void 0);let n=i();return e.afterSearch&&await N(e.afterSearch,e,t,r,n),n}return e.beforeSearch?.length||e.afterSearch?.length?l():i()}(e,t);throw(0,n.$)("INVALID_SEARCH_MODE",i)}function eU(e,t,r,n){let o=e.data.docs,i=Array.from({length:n}),l=new Set;for(let a=r;a<n+r;a++){let r=t[a];if(void 0===r)break;let[n,s]=r;if(!l.has(n)){let t=e.documentsStore.get(o,n);i[a]={id:m(e.internalDocumentIDStore,n),score:s,document:t},l.add(n)}}return i}r(7377)},7377:(e,t,r)=>{r.d(t,{JK:()=>n.J}),r(3514);var n=r(3460)},7434:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7516:(e,t,r)=>{r.d(t,{e:()=>a});var n=r(7377);let o={language:"mandarin"},i=new Intl.Segmenter("zh-CN",{granularity:"word"});function l(e,t,r){let n;if("string"!=typeof e)return[e];let o=function(e){for(;""===e[e.length-1];)e.pop();for(;""===e[0];)e.shift();return e}(r&&this?.tokenizeSkipProperties?.has(r)?[this?.normalizeToken?.bind(this,r??"")(e)]:function(e){let t=i.segment(e),r=[];for(let e of t)e.isWordLike&&r.push(e.segment);return r}(e));return this.allowDuplicates?o:Array.from(new Set(o))}function a(e=o){let t={tokenize:l,language:e.language,stemmerSkipProperties:new Set(e.stemmerSkipProperties?[e.stemmerSkipProperties].flat():[]),tokenizeSkipProperties:new Set(e.tokenizeSkipProperties?[e.tokenizeSkipProperties].flat():[]),stopWords:e.stopWords,allowDuplicates:!!e.allowDuplicates,normalizeToken:n.JK,normalizationCache:new Map};return t.tokenize=l.bind(l),t}},7924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8169:(e,t,r)=>{r.d(t,{$A:()=>l,YL:()=>s});var n=r(5155),o=r(2115);let i=(0,r(344).q6)("SearchContext",{enabled:!1,hotKey:[],setOpenSearch:()=>void 0});function l(){return i.use()}function a(){let[e,t]=(0,o.useState)("⌘");return(0,o.useEffect)(()=>{window.navigator.userAgent.includes("Windows")&&t("Ctrl")},[]),e}function s(e){let{SearchDialog:t,children:r,preload:l=!0,options:s,hotKey:u=[{key:e=>e.metaKey||e.ctrlKey,display:(0,n.jsx)(a,{})},{key:"k",display:"K"}],links:c}=e,[f,d]=(0,o.useState)(!l&&void 0);return(0,o.useEffect)(()=>{let e=e=>{u.every(t=>"string"==typeof t.key?e.key===t.key:t.key(e))&&(d(!0),e.preventDefault())};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}},[u]),(0,n.jsxs)(i.Provider,{value:(0,o.useMemo)(()=>({enabled:!0,hotKey:u,setOpenSearch:d}),[u]),children:[void 0!==f&&(0,n.jsx)(t,{open:f,onOpenChange:d,links:c,...s}),r]})}},8564:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9838:(e,t,r)=>{r.d(t,{$:()=>a});var n=r(2359),o=r(3514);let i=n.Fc.join("\n - "),l={NO_LANGUAGE_WITH_CUSTOM_TOKENIZER:"Do not pass the language option to create when using a custom tokenizer.",LANGUAGE_NOT_SUPPORTED:`Language "%s" is not supported.
Supported languages are:
 - ${i}`,INVALID_STEMMER_FUNCTION_TYPE:"config.stemmer property must be a function.",MISSING_STEMMER:'As of version 1.0.0 @orama/orama does not ship non English stemmers by default. To solve this, please explicitly import and specify the "%s" stemmer from the package @orama/stemmers. See https://docs.orama.com/open-source/text-analysis/stemming for more information.',CUSTOM_STOP_WORDS_MUST_BE_FUNCTION_OR_ARRAY:"Custom stop words array must only contain strings.",UNSUPPORTED_COMPONENT:'Unsupported component "%s".',COMPONENT_MUST_BE_FUNCTION:'The component "%s" must be a function.',COMPONENT_MUST_BE_FUNCTION_OR_ARRAY_FUNCTIONS:'The component "%s" must be a function or an array of functions.',INVALID_SCHEMA_TYPE:'Unsupported schema type "%s" at "%s". Expected "string", "boolean" or "number" or array of them.',DOCUMENT_ID_MUST_BE_STRING:'Document id must be of type "string". Got "%s" instead.',DOCUMENT_ALREADY_EXISTS:'A document with id "%s" already exists.',DOCUMENT_DOES_NOT_EXIST:'A document with id "%s" does not exists.',MISSING_DOCUMENT_PROPERTY:'Missing searchable property "%s".',INVALID_DOCUMENT_PROPERTY:'Invalid document property "%s": expected "%s", got "%s"',UNKNOWN_INDEX:'Invalid property name "%s". Expected a wildcard string ("*") or array containing one of the following properties: %s',INVALID_BOOST_VALUE:"Boost value must be a number greater than, or less than 0.",INVALID_FILTER_OPERATION:"You can only use one operation per filter, you requested %d.",SCHEMA_VALIDATION_FAILURE:'Cannot insert document due schema validation failure on "%s" property.',INVALID_SORT_SCHEMA_TYPE:'Unsupported sort schema type "%s" at "%s". Expected "string" or "number".',CANNOT_SORT_BY_ARRAY:'Cannot configure sort for "%s" because it is an array (%s).',UNABLE_TO_SORT_ON_UNKNOWN_FIELD:'Unable to sort on unknown field "%s". Allowed fields: %s',SORT_DISABLED:"Sort is disabled. Please read the documentation at https://docs.oramasearch for more information.",UNKNOWN_GROUP_BY_PROPERTY:'Unknown groupBy property "%s".',INVALID_GROUP_BY_PROPERTY:'Invalid groupBy property "%s". Allowed types: "%s", but given "%s".',UNKNOWN_FILTER_PROPERTY:'Unknown filter property "%s".',INVALID_VECTOR_SIZE:'Vector size must be a number greater than 0. Got "%s" instead.',INVALID_VECTOR_VALUE:'Vector value must be a number greater than 0. Got "%s" instead.',INVALID_INPUT_VECTOR:`Property "%s" was declared as a %s-dimensional vector, but got a %s-dimensional vector instead.
Input vectors must be of the size declared in the schema, as calculating similarity between vectors of different sizes can lead to unexpected results.`,WRONG_SEARCH_PROPERTY_TYPE:'Property "%s" is not searchable. Only "string" properties are searchable.',FACET_NOT_SUPPORTED:'Facet doens\'t support the type "%s".',INVALID_DISTANCE_SUFFIX:'Invalid distance suffix "%s". Valid suffixes are: cm, m, km, mi, yd, ft.',INVALID_SEARCH_MODE:'Invalid search mode "%s". Valid modes are: "fulltext", "vector", "hybrid".',MISSING_VECTOR_AND_SECURE_PROXY:"No vector was provided and no secure proxy was configured. Please provide a vector or configure an Orama Secure Proxy to perform hybrid search.",MISSING_TERM:'"term" is a required parameter when performing hybrid search. Please provide a search term.',INVALID_VECTOR_INPUT:'Invalid "vector" property. Expected an object with "value" and "property" properties, but got "%s" instead.',PLUGIN_CRASHED:"A plugin crashed during initialization. Please check the error message for more information:",PLUGIN_SECURE_PROXY_NOT_FOUND:`Could not find '@orama/secure-proxy-plugin' installed in your Orama instance.
Please install it before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,PLUGIN_SECURE_PROXY_MISSING_CHAT_MODEL:`Could not find a chat model defined in the secure proxy plugin configuration.
Please provide a chat model before proceeding with creating an answer session.
Read more at https://docs.orama.com/open-source/plugins/plugin-secure-proxy#plugin-secure-proxy
`,ANSWER_SESSION_LAST_MESSAGE_IS_NOT_ASSISTANT:"The last message in the session is not an assistant message. Cannot regenerate non-assistant messages.",PLUGIN_COMPONENT_CONFLICT:'The component "%s" is already defined. The plugin "%s" is trying to redefine it.'};function a(e,...t){let r=Error((0,o.nv)(l[e]??`Unsupported Orama Error code: ${e}`,...t));return r.code=e,"captureStackTrace"in Error.prototype&&Error.captureStackTrace(r),r}},9946:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:f,...d}=e;return(0,n.createElement)("svg",{ref:t,...s,width:o,height:o,stroke:r,strokeWidth:l?24*Number(i)/Number(o):i,className:a("lucide",u),...d},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:s,...c}=r;return(0,n.createElement)(u,{ref:i,iconNode:t,className:a("lucide-".concat(o(l(e))),"lucide-".concat(e),s),...c})});return r.displayName=l(e),r}}}]);