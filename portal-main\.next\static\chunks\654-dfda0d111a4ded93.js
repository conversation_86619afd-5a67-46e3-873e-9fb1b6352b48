"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[654],{70:(e,t,r)=>{r.d(t,{Breadcrumb:()=>R,Footer:()=>O,LastUpdate:()=>T,PageArticle:()=>M,PageBody:()=>C,TocPopover:()=>k,TocPopoverContent:()=>P,TocPopoverTrigger:()=>j});var n=r(5155),l=r(2115),o=r(2829),a=r(3536);let i=(0,a.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),s=(0,a.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var c=r(9949),u=r(9688),d=r(9697),f=r(8693),h=r(263),p=r(344),m=r(244),x=r(1339),g=r(5455),v=r(8265),y=r(8758),b=r(6441);let w=(0,p.q6)("TocPopoverContext");function j(e){var t,r;let{items:a,...i}=e,{text:s}=(0,d.s9)(),{open:c}=w.use(),h=b.R3(),p=(0,l.useMemo)(()=>a.findIndex(e=>h===e.url.slice(1)),[a,h]),m=(0,f.L)().at(-1),x=-1!==p&&!c;return(0,n.jsxs)(y.R6,{...i,className:(0,u.QP)("flex flex-row items-center text-sm text-fd-muted-foreground gap-2.5 px-4 py-2.5 text-start focus-visible:outline-none [&_svg]:shrink-0 [&_svg]:size-4 md:px-6",i.className),children:[(0,n.jsx)(N,{value:(p+1)/a.length,max:1,className:(0,u.QP)(c&&"text-fd-primary")}),(0,n.jsxs)("span",{className:(0,u.QP)("grid flex-1 *:row-start-1 *:col-start-1",c&&"text-fd-foreground"),children:[(0,n.jsx)("span",{className:(0,u.QP)("truncate transition-all",x&&"opacity-0 -translate-y-full pointer-events-none"),children:null!=(r=null==m?void 0:m.name)?r:s.toc}),(0,n.jsx)("span",{className:(0,u.QP)("truncate transition-all",!x&&"opacity-0 translate-y-full pointer-events-none"),children:null==(t=a[p])?void 0:t.title})]}),(0,n.jsx)(o.A,{className:(0,u.QP)("transition-transform",c&&"rotate-180")})]})}function N(e){let{value:t,strokeWidth:r=2,size:l=24,min:o=0,max:a=100,...i}=e,s=t<o?o:t>a?a:t,c=(l-r)/2,u=2*Math.PI*c,d=s/a*u,f={cx:l/2,cy:l/2,r:c,fill:"none",strokeWidth:r};return(0,n.jsxs)("svg",{role:"progressbar",viewBox:"0 0 ".concat(l," ").concat(l),"aria-valuenow":s,"aria-valuemin":o,"aria-valuemax":a,...i,children:[(0,n.jsx)("circle",{...f,className:"stroke-current/25"}),(0,n.jsx)("circle",{...f,stroke:"currentColor",strokeDasharray:u,strokeDashoffset:u-d,strokeLinecap:"round",transform:"rotate(-90 ".concat(l/2," ").concat(l/2,")"),className:"transition-all"})]})}function P(e){return(0,n.jsx)(y.Ke,{"data-toc-popover":"",...e,className:(0,u.QP)("flex flex-col max-h-[50vh]",e.className),children:e.children})}function k(e){let t=(0,l.useRef)(null),[r,o]=(0,l.useState)(!1),a=(0,h.c)(),{tocNav:i}=(0,x.v)(),{isTransparent:s}=(0,x.h)(),c=(0,v.J)(e=>{r&&t.current&&!t.current.contains(e.target)&&o(!1)});return(0,l.useEffect)(()=>(window.addEventListener("click",c),()=>{window.removeEventListener("click",c)}),[c]),(0,n.jsx)("div",{...e,className:(0,u.QP)("sticky overflow-visible z-10",i,e.className),style:{...e.style,top:"calc(var(--fd-banner-height) + var(--fd-nav-height))"},children:(0,n.jsx)(w.Provider,{value:(0,l.useMemo)(()=>({open:r,setOpen:o}),[o,r]),children:(0,n.jsx)(y.Nt,{open:r,onOpenChange:o,asChild:!0,children:(0,n.jsx)("header",{ref:t,id:"nd-tocnav",...e,className:(0,u.QP)("border-b border-fd-foreground/10 backdrop-blur-sm transition-colors",(!s||r)&&"bg-fd-background/80",r&&"shadow-lg",a.open&&"max-md:hidden"),children:e.children})})})})}function C(e){let{page:t}=(0,x.v)();return(0,n.jsx)("div",{id:"nd-page",...e,className:(0,u.QP)("flex w-full min-w-0 flex-col",t,e.className),children:e.children})}function M(e){let{article:t}=(0,x.v)();return(0,n.jsx)("article",{...e,className:(0,u.QP)("flex w-full flex-1 flex-col gap-6 px-4 md:px-6 pt-8 md:pt-12 xl:px-12 xl:mx-auto",t,e.className),children:e.children})}function T(e){let{text:t}=(0,d.s9)(),[r,o]=(0,l.useState)("");return(0,l.useEffect)(()=>{o(e.date.toLocaleDateString())},[e.date]),(0,n.jsxs)("p",{className:"text-sm text-fd-muted-foreground",children:[t.lastUpdate," ",r]})}let E=new WeakMap;function O(e){let{items:t}=e,{root:r}=(0,f.t)(),o=(0,p.a8)(),{previous:a,next:i}=(0,l.useMemo)(()=>{if(t)return t;let e=E.get(r),n=null!=e?e:function e(t){let r=[];return t.forEach(t=>{if("folder"===t.type){t.index&&r.push(t.index),r.push(...e(t.children));return}"page"!==t.type||t.external||r.push(t)}),r}(r.children);E.set(r,n);let l=n.findIndex(e=>(0,g.$)(e.url,o,!1));return -1===l?{}:{previous:n[l-1],next:n[l+1]}},[t,o,r]);return(0,n.jsxs)("div",{className:(0,u.QP)("@container grid gap-4 pb-6",a&&i?"grid-cols-2":"grid-cols-1"),children:[a?(0,n.jsx)(S,{item:a,index:0}):null,i?(0,n.jsx)(S,{item:i,index:1}):null]})}function S(e){var t;let{item:r,index:l}=e,{text:o}=(0,d.s9)();return(0,n.jsxs)(c.default,{href:r.url,className:(0,u.QP)("flex flex-col gap-2 rounded-lg border p-4 text-sm transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground @max-lg:col-span-full",1===l&&"text-end"),children:[(0,n.jsxs)("div",{className:(0,u.QP)("inline-flex items-center gap-1.5 font-medium",1===l&&"flex-row-reverse"),children:[(0,n.jsx)(0===l?i:s,{className:"-mx-1 size-4 shrink-0 rtl:rotate-180"}),(0,n.jsx)("p",{children:r.name})]}),(0,n.jsx)("p",{className:"text-fd-muted-foreground truncate",children:null!=(t=r.description)?t:0===l?o.previousPage:o.nextPage})]})}function R(e){let t=(0,f.L)(),{root:r}=(0,f.t)(),o=(0,l.useMemo)(()=>{var n;return(0,m.Pp)(r,t,{includePage:null!=(n=e.includePage)&&n,...e})},[e,t,r]);return 0===o.length?null:(0,n.jsx)("div",{className:"flex flex-row items-center gap-1.5 text-[15px] text-fd-muted-foreground",children:o.map((e,t)=>{let r=(0,u.QP)("truncate",t===o.length-1&&"text-fd-primary font-medium");return(0,n.jsxs)(l.Fragment,{children:[0!==t&&(0,n.jsx)("span",{className:"text-fd-foreground/30",children:"/"}),e.url?(0,n.jsx)(c.default,{href:e.url,className:(0,u.QP)(r,"transition-opacity hover:opacity-80"),children:e.name}):(0,n.jsx)("span",{className:r,children:e.name})]},t)})})}},244:(e,t,r)=>{function n(e,t,r){let{includePage:n=!0,includeSeparator:l=!1,includeRoot:o}=r,a=[];return t.forEach((e,r)=>{if("separator"===e.type&&l&&a.push({name:e.name}),"folder"===e.type){let n=t.at(r+1);if(n&&e.index===n)return;if(e.root){a=[];return}a.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&a.push({name:e.name,url:e.url})}),o&&a.unshift({name:e.name,url:"object"==typeof o?o.url:void 0}),a}r.d(t,{Pp:()=>n,oe:()=>function e(t,r){let n;for(let l of(r.endsWith("/")&&(r=r.slice(0,-1)),t)){if("separator"===l.type&&(n=l),"folder"===l.type){if(l.index?.url===r){let e=[];return n&&e.push(n),e.push(l,l.index),e}let t=e(l.children,r);if(t)return t.unshift(l),n&&t.unshift(n),t}if("page"===l.type&&l.url===r){let e=[];return n&&e.push(n),e.push(l),e}}return null}}),r(9189),r(2115)},263:(e,t,r)=>{r.d(t,{G:()=>u,c:()=>c});var n=r(5155),l=r(2115),o=r(344),a=r(5547),i=r(3259);let s=(0,o.q6)("SidebarContext");function c(){return s.use()}function u(e){let{children:t}=e,r=(0,l.useRef)(!0),[c,u]=(0,l.useState)(!1),[d,f]=(0,l.useState)(!1),h=(0,o.a8)();return(0,i.T)(h,()=>{r.current&&u(!1),r.current=!0}),(0,n.jsx)(s.Provider,{value:(0,l.useMemo)(()=>({open:c,setOpen:u,collapsed:d,setCollapsed:f,closeOnRedirect:r}),[c,d]),children:(0,n.jsx)(a.GB,{open:c,onOpenChange:u,children:t})})}},1639:(e,t,r)=>{r.d(t,{j:()=>u});var n=r(5155),l=r(2115),o=r(6441),a=r(3259),i=r(8265);function s(e,t){if(0===t.length||0===e.clientHeight)return[0,0];let r=Number.MAX_VALUE,n=0;for(let l of t){let t=e.querySelector(`a[href="#${l}"]`);if(!t)continue;let o=getComputedStyle(t);r=Math.min(r,t.offsetTop+parseFloat(o.paddingTop)),n=Math.max(n,t.offsetTop+t.clientHeight-parseFloat(o.paddingBottom))}return[r,n-r]}function c(e,t){e.style.setProperty("--fd-top",`${t[0]}px`),e.style.setProperty("--fd-height",`${t[1]}px`)}function u({containerRef:e,...t}){let r=o.Mf(),u=(0,l.useRef)(null),d=(0,i.J)(()=>{e.current&&u.current&&c(u.current,s(e.current,r))});return(0,l.useEffect)(()=>{if(!e.current)return;let t=e.current;d();let r=new ResizeObserver(d);return r.observe(t),()=>{r.disconnect()}},[e,d]),(0,a.T)(r,()=>{e.current&&u.current&&c(u.current,s(e.current,r))}),(0,n.jsx)("div",{ref:u,role:"none",...t})}},1778:(e,t,r)=>{r.d(t,{default:()=>c});var n=r(5155),l=r(6441),o=r(2115),a=r(9688),i=r(1639),s=r(8070);function c(e){let{items:t}=e,r=(0,o.useRef)(null),[l,a]=(0,o.useState)();return((0,o.useEffect)(()=>{if(!r.current)return;let e=r.current;function n(){if(0===e.clientHeight)return;let r=0,n=0,l=[];for(let o=0;o<t.length;o++){let a=e.querySelector('a[href="#'.concat(t[o].url.slice(1),'"]'));if(!a)continue;let i=getComputedStyle(a),s=u(t[o].depth)+1,c=a.offsetTop+parseFloat(i.paddingTop),d=a.offsetTop+a.clientHeight-parseFloat(i.paddingBottom);r=Math.max(s,r),n=Math.max(n,d),l.push("".concat(0===o?"M":"L").concat(s," ").concat(c)),l.push("L".concat(s," ").concat(d))}a({path:l.join(" "),width:r+1,height:n})}let l=new ResizeObserver(n);return n(),l.observe(e),()=>{l.disconnect()}},[t]),0===t.length)?(0,n.jsx)(s.k,{}):(0,n.jsxs)(n.Fragment,{children:[l?(0,n.jsx)("div",{className:"absolute start-0 top-0 rtl:-scale-x-100",style:{width:l.width,height:l.height,maskImage:'url("data:image/svg+xml,'.concat(encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 '.concat(l.width," ").concat(l.height,'"><path d="').concat(l.path,'" stroke="black" stroke-width="1" fill="none" /></svg>')),'")')},children:(0,n.jsx)(i.j,{containerRef:r,className:"mt-(--fd-top) h-(--fd-height) bg-fd-primary transition-all"})}):null,(0,n.jsx)("div",{className:"flex flex-col",ref:r,children:t.map((e,r)=>{var l,o;return(0,n.jsx)(d,{item:e,upper:null==(l=t[r-1])?void 0:l.depth,lower:null==(o=t[r+1])?void 0:o.depth},e.url)})})]})}function u(e){return 10*(e>=3)}function d(e){var t;let{item:r,upper:o=r.depth,lower:i=r.depth}=e,s=u(r.depth),c=u(o),d=u(i);return(0,n.jsxs)(l.Cz,{href:r.url,style:{paddingInlineStart:(t=r.depth)<=2?14:3===t?26:36},className:"prose relative py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",children:[s!==c?(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",className:"absolute -top-1.5 start-0 size-4 rtl:-scale-x-100",children:(0,n.jsx)("line",{x1:c,y1:"0",x2:s,y2:"12",className:"stroke-fd-foreground/10",strokeWidth:"1"})}):null,(0,n.jsx)("div",{className:(0,a.QP)("absolute inset-y-0 w-px bg-fd-foreground/10",s!==c&&"top-1.5",s!==d&&"bottom-1.5"),style:{insetInlineStart:s}}),r.title]})}},3259:(e,t,r)=>{r.d(t,{T:()=>n.T});var n=r(5936);r(9189)},5455:(e,t,r)=>{r.d(t,{$:()=>n});function n(e,t,r=!0){return e.endsWith("/")&&(e=e.slice(0,-1)),t.endsWith("/")&&(t=t.slice(0,-1)),e===t||r&&t.startsWith(`${e}/`)}},5547:(e,t,r)=>{r.d(t,{GB:()=>s,Pg:()=>u,x2:()=>c}),r(9189);var n=r(2115),l=r(1114),o=r(5155),a=(0,n.createContext)(null);function i(){let e=(0,n.useContext)(a);if(!e)throw Error("Missing sidebar provider");return e}function s(e){let[t,r]=void 0===e.open?(0,n.useState)(!1):[e.open,e.onOpenChange];return(0,o.jsx)(a.Provider,{value:(0,n.useMemo)(()=>({open:t,setOpen:null!=r?r:()=>void 0}),[t,r]),children:e.children})}function c(e){let{as:t,...r}=e,{open:n,setOpen:l}=i();return(0,o.jsx)(null!=t?t:"button",{"aria-label":"Toggle Sidebar","data-open":n,onClick:()=>{l(!n)},...r})}function u(e){let{as:t,blockScrollingWidth:r,removeScrollOn:a=r?"(width < ".concat(r,"px)"):void 0,...s}=e,{open:c}=i(),[u,d]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{if(!a)return;let e=window.matchMedia(a),t=()=>{d(e.matches)};return t(),e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}},[a]),(0,o.jsx)(l.A,{as:null!=t?t:"aside","data-open":c,enabled:!!(u&&c),...s,children:s.children})}},5936:(e,t,r)=>{r.d(t,{T:()=>l});var n=r(2115);function l(e,t,r=function e(t,r){if(Array.isArray(t)&&Array.isArray(r))return r.length!==t.length||t.some((t,n)=>e(t,r[n]));if("object"==typeof t&&t&&"object"==typeof r&&r){let n=Object.keys(t),l=Object.keys(r);return n.length!==l.length||n.some(n=>e(t[n],r[n]))}return t!==r}){let[o,a]=(0,n.useState)(e);r(o,e)&&(t(e,o),a(e))}},6441:(e,t,r)=>{r.d(t,{AnchorProvider:()=>v,N2:()=>g,Cz:()=>y,R3:()=>m,Mf:()=>x});var n=r(5936);r(9189);var l=r(2115);let o=e=>"object"==typeof e&&null!=e&&1===e.nodeType,a=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,i=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let r=getComputedStyle(e,null);return a(r.overflowY,t)||a(r.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},s=(e,t,r,n,l,o,a,i)=>o<e&&a>t||o>e&&a<t?0:o<=e&&i<=r||a>=t&&i>=r?o-e-n:a>t&&i<r||o<e&&i>r?a-t+l:0,c=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},u=(e,t)=>{var r,n,l,a;if("undefined"==typeof document)return[];let{scrollMode:u,block:d,inline:f,boundary:h,skipOverflowHiddenElements:p}=t,m="function"==typeof h?h:e=>e!==h;if(!o(e))throw TypeError("Invalid target");let x=document.scrollingElement||document.documentElement,g=[],v=e;for(;o(v)&&m(v);){if((v=c(v))===x){g.push(v);break}null!=v&&v===document.body&&i(v)&&!i(document.documentElement)||null!=v&&i(v,p)&&g.push(v)}let y=null!=(n=null==(r=window.visualViewport)?void 0:r.width)?n:innerWidth,b=null!=(a=null==(l=window.visualViewport)?void 0:l.height)?a:innerHeight,{scrollX:w,scrollY:j}=window,{height:N,width:P,top:k,right:C,bottom:M,left:T}=e.getBoundingClientRect(),{top:E,right:O,bottom:S,left:R}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),Q="start"===d||"nearest"===d?k-E:"end"===d?M+S:k+N/2-E+S,H="center"===f?T+P/2-R+O:"end"===f?C+O:T-R,L=[];for(let e=0;e<g.length;e++){let t=g[e],{height:r,width:n,top:l,right:o,bottom:a,left:c}=t.getBoundingClientRect();if("if-needed"===u&&k>=0&&T>=0&&M<=b&&C<=y&&(t===x&&!i(t)||k>=l&&M<=a&&T>=c&&C<=o))break;let h=getComputedStyle(t),p=parseInt(h.borderLeftWidth,10),m=parseInt(h.borderTopWidth,10),v=parseInt(h.borderRightWidth,10),E=parseInt(h.borderBottomWidth,10),O=0,S=0,R="offsetWidth"in t?t.offsetWidth-t.clientWidth-p-v:0,W="offsetHeight"in t?t.offsetHeight-t.clientHeight-m-E:0,_="offsetWidth"in t?0===t.offsetWidth?0:n/t.offsetWidth:0,A="offsetHeight"in t?0===t.offsetHeight?0:r/t.offsetHeight:0;if(x===t)O="start"===d?Q:"end"===d?Q-b:"nearest"===d?s(j,j+b,b,m,E,j+Q,j+Q+N,N):Q-b/2,S="start"===f?H:"center"===f?H-y/2:"end"===f?H-y:s(w,w+y,y,p,v,w+H,w+H+P,P),O=Math.max(0,O+j),S=Math.max(0,S+w);else{O="start"===d?Q-l-m:"end"===d?Q-a+E+W:"nearest"===d?s(l,a,r,m,E+W,Q,Q+N,N):Q-(l+r/2)+W/2,S="start"===f?H-c-p:"center"===f?H-(c+n/2)+R/2:"end"===f?H-o+v+R:s(c,o,n,p,v+R,H,H+P,P);let{scrollLeft:e,scrollTop:i}=t;O=0===A?0:Math.max(0,Math.min(i+O/A,t.scrollHeight-r/A+W)),S=0===_?0:Math.max(0,Math.min(e+S/_,t.scrollWidth-n/_+R)),Q+=i-O,H+=e-S}L.push({el:t,top:O,left:S})}return L},d=e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"};var f=r(5155),h=(0,l.createContext)([]),p=(0,l.createContext)({current:null});function m(){return(0,l.useContext)(h).at(-1)}function x(){return(0,l.useContext)(h)}function g(e){let{containerRef:t,children:r}=e;return(0,f.jsx)(p.Provider,{value:t,children:r})}function v(e){let{toc:t,single:r=!0,children:n}=e,o=(0,l.useMemo)(()=>t.map(e=>e.url.split("#")[1]),[t]);return(0,f.jsx)(h.Provider,{value:function(e,t){let[r,n]=(0,l.useState)([]);return(0,l.useEffect)(()=>{let r=[],l=new IntersectionObserver(e=>{for(let t of e)t.isIntersecting&&!r.includes(t.target.id)?r=[...r,t.target.id]:!t.isIntersecting&&r.includes(t.target.id)&&(r=r.filter(e=>e!==t.target.id));r.length>0&&n(r)},{rootMargin:t?"-80px 0% -70% 0%":"-20px 0% -40% 0%",threshold:1});function o(){let r=document.scrollingElement;r&&(0===r.scrollTop&&t?n(e.slice(0,1)):r.scrollTop+r.clientHeight>=r.scrollHeight-6&&n(r=>r.length>0&&!t?e.slice(e.indexOf(r[0])):e.slice(-1)))}for(let t of e){let e=document.getElementById(t);e&&l.observe(e)}return o(),window.addEventListener("scroll",o),()=>{window.removeEventListener("scroll",o),l.disconnect()}},[t,e]),t?r.slice(0,1):r}(o,r),children:n})}var y=(0,l.forwardRef)((e,t)=>{let{onActiveChange:r,...o}=e,a=(0,l.useContext)(p),i=x(),s=(0,l.useRef)(null),c=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{t.forEach(t=>{"function"==typeof t?t(e):null!==t&&(t.current=e)})}}(s,t),h=i.includes(o.href.slice(1));return(0,n.T)(h,e=>{let t=s.current;t&&(e&&a.current&&function(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;let r=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(u(e,t));let n="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:l,top:o,left:a}of u(e,d(t))){let e=o-r.top+r.bottom,t=a-r.left+r.right;l.scroll({top:e,left:t,behavior:n})}}(t,{behavior:"smooth",block:"center",inline:"center",scrollMode:"always",boundary:a.current}),null==r||r(e))}),(0,f.jsx)("a",{ref:c,"data-active":h,...o,children:o.children})});y.displayName="TOCItem"},7242:(e,t,r)=>{r.d(t,{CodeBlock:()=>f,Pre:()=>d});var n=r(5155),l=r(3536);let o=(0,l.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),a=(0,l.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var i=r(2115),s=r(9688),c=r(5675),u=r(7936);let d=(0,i.forwardRef)((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)("pre",{ref:t,className:(0,s.QP)("p-4 focus-visible:outline-none",r),...l,children:l.children})});d.displayName="Pre";let f=(0,i.forwardRef)((e,t)=>{let{title:r,allowCopy:l=!0,keepBackground:o=!1,icon:a,viewportProps:u,...d}=e,f=(0,i.useRef)(null),p=(0,i.useCallback)(()=>{var e,t;let r=null==(e=f.current)?void 0:e.getElementsByTagName("pre").item(0);if(!r)return;let n=r.cloneNode(!0);n.querySelectorAll(".nd-copy-ignore").forEach(e=>{e.remove()}),navigator.clipboard.writeText(null!=(t=n.textContent)?t:"")},[]);return(0,n.jsxs)("figure",{ref:t,...d,className:(0,s.QP)("not-prose group fd-codeblock relative my-4 overflow-hidden rounded-lg border bg-fd-secondary/50 text-sm",o&&"bg-(--shiki-light-bg) dark:bg-(--shiki-dark-bg)",d.className),children:[r?(0,n.jsxs)("div",{className:"flex items-center gap-2 border-b bg-fd-muted px-4 py-1.5",children:[a?(0,n.jsx)("div",{className:"text-fd-muted-foreground [&_svg]:size-3.5",dangerouslySetInnerHTML:"string"==typeof a?{__html:a}:void 0,children:"string"!=typeof a?a:null}):null,(0,n.jsx)("figcaption",{className:"flex-1 truncate text-fd-muted-foreground",children:r}),l?(0,n.jsx)(h,{className:"-me-2",onCopy:p}):null]}):l&&(0,n.jsx)(h,{className:"absolute right-2 top-2 z-[2] backdrop-blur-md",onCopy:p}),(0,n.jsxs)(c.FK,{ref:f,dir:"ltr",children:[(0,n.jsx)(c.Gl,{...u,className:(0,s.QP)("max-h-[600px]",null==u?void 0:u.className),children:d.children}),(0,n.jsx)(c.$H,{orientation:"horizontal"})]})]})});function h(e){let{className:t,onCopy:r,...l}=e,[c,d]=function(e){let[t,r]=(0,i.useState)(!1),n=(0,i.useRef)(null),l=(0,i.useRef)(e);l.current=e;let o=(0,i.useCallback)(()=>{n.current&&window.clearTimeout(n.current),n.current=window.setTimeout(()=>{r(!1)},1500),l.current(),r(!0)},[]);return(0,i.useEffect)(()=>()=>{n.current&&window.clearTimeout(n.current)},[]),[t,o]}(r);return(0,n.jsxs)("button",{type:"button",className:(0,s.QP)((0,u.r)({color:"ghost"}),"transition-opacity group-hover:opacity-100 [&_svg]:size-3.5",!c&&"[@media(hover:hover)]:opacity-0",t),"aria-label":c?"Copied Text":"Copy Text",onClick:d,...l,children:[(0,n.jsx)(o,{className:(0,s.QP)("transition-transform",!c&&"scale-0")}),(0,n.jsx)(a,{className:(0,s.QP)("absolute transition-transform",c&&"scale-0")})]})}f.displayName="CodeBlock"},8070:(e,t,r)=>{r.d(t,{TOCItems:()=>p,TOCScrollArea:()=>h,Toc:()=>d,k:()=>f});var n=r(5155),l=r(6441),o=r(2115),a=r(9688),i=r(9697),s=r(1639),c=r(5675),u=r(1339);function d(e){let{toc:t}=(0,u.v)();return(0,n.jsx)("div",{id:"nd-toc",...e,className:(0,a.QP)("sticky top-[calc(var(--fd-banner-height)+var(--fd-nav-height))] h-(--fd-toc-height) pb-2 pt-12",t,e.className),style:{...e.style,"--fd-toc-height":"calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"},children:(0,n.jsx)("div",{className:"flex h-full w-(--fd-toc-width) max-w-full flex-col gap-3 pe-4",children:e.children})})}function f(){let{text:e}=(0,i.s9)();return(0,n.jsx)("div",{className:"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground",children:e.tocNoHeadings})}function h(e){let{isMenu:t,...r}=e,i=(0,o.useRef)(null);return(0,n.jsx)(c.FK,{...r,className:(0,a.QP)("flex flex-col ps-px",r.className),children:(0,n.jsx)(c.Gl,{ref:i,className:(0,a.QP)("relative min-h-0 text-sm",t&&"[mask-image:linear-gradient(to_bottom,transparent,white_16px,white_calc(100%-16px),transparent)] px-4 md:px-6 py-2"),children:(0,n.jsx)(l.N2,{containerRef:i,children:r.children})})})}function p(e){let{items:t}=e,r=(0,o.useRef)(null);return 0===t.length?(0,n.jsx)(f,{}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.j,{containerRef:r,className:"absolute top-(--fd-top) h-(--fd-height) w-px bg-fd-primary transition-all"}),(0,n.jsx)("div",{ref:r,className:"flex flex-col border-s border-fd-foreground/10",children:t.map(e=>(0,n.jsx)(m,{item:e},e.url))})]})}function m(e){let{item:t}=e;return(0,n.jsx)(l.Cz,{href:t.url,className:(0,a.QP)("prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",t.depth<=2&&"ps-3",3===t.depth&&"ps-6",t.depth>=4&&"ps-8"),children:t.title})}},8693:(e,t,r)=>{r.d(t,{L:()=>u,TreeContextProvider:()=>c,t:()=>d});var n=r(5155),l=r(344),o=r(2115),a=r(244);let i=(0,l.q6)("TreeContext"),s=(0,l.q6)("PathContext",[]);function c(e){var t,r,c;let u=(0,o.useRef)(0),d=(0,l.a8)(),f=(0,o.useMemo)(()=>e.tree,[null!=(t=e.tree.$id)?t:e.tree]),h=(0,o.useMemo)(()=>{var e;return null!=(e=(0,a.oe)(f.children,d))?e:[]},[f,d]),p=null!=(r=h.findLast(e=>"folder"===e.type&&e.root))?r:f;return null!=p.$id||(p.$id=String(u.current++)),(0,n.jsx)(i.Provider,{value:(0,o.useMemo)(()=>({root:p}),[p]),children:(0,n.jsx)(s.Provider,{value:h,children:e.children})})}function u(){return s.use()}function d(){return i.use("You must wrap this component under <DocsLayout />")}},9249:(e,t,r)=>{r.d(t,{C6:()=>l,Cl:()=>o,Tt:()=>a,fX:()=>i});var n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function l(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var l in t=arguments[r])Object.prototype.hasOwnProperty.call(t,l)&&(e[l]=t[l]);return e}).apply(this,arguments)};function a(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)0>t.indexOf(n[l])&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r}Object.create;function i(e,t,r){if(r||2==arguments.length)for(var n,l=0,o=t.length;l<o;l++)!n&&l in t||(n||(n=Array.prototype.slice.call(t,0,l)),n[l]=t[l]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError}}]);