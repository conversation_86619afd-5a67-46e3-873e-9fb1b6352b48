import { createMDX } from 'fumadocs-mdx/next'
import createNextIntlPlugin from 'next-intl/plugin'

const withMDX = createMDX()
const withNextIntl = createNextIntlPlugin('./lib/next-intl-requests.ts')

/** @type {import('next').NextConfig} */
const config = {
  turbopack: true,
  output: 'export', // 启用静态导出
  trailingSlash: true, // 为静态导出添加尾部斜杠
  images: {
    unoptimized: true, // 禁用图片优化（静态导出需要）
    domains: ['ten-framework-assets.s3.amazonaws.com'],
  },
}

export default withNextIntl(withMDX(config))
