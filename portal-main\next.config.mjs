import { createMDX } from 'fumadocs-mdx/next'
import createNextIntlPlugin from 'next-intl/plugin'

const withMDX = createMDX()
const withNextIntl = createNextIntlPlugin('./lib/next-intl-requests.ts')

/** @type {import('next').NextConfig} */
const config = {
  output: 'export', // 启用静态导出
  trailingSlash: true, // 为静态导出添加尾部斜杠
  distDir: 'out', // 静态导出输出目录
  images: {
    unoptimized: true, // 禁用图片优化（静态导出需要）
    domains: ['ten-framework-assets.s3.amazonaws.com'],
  },
  // 静态导出时跳过API路由检查
  skipTrailingSlashRedirect: true,
  // 确保所有页面都被静态生成
  generateBuildId: () => 'static-export',
}

export default withNextIntl(withMDX(config))
