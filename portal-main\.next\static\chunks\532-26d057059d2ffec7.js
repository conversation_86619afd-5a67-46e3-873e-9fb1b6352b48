"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[532],{8265:(e,t,r)=>{r.d(t,{J:()=>a}),r(9189);var n=r(2115);function a(e){let t=(0,n.useRef)(e);return t.current=e,(0,n.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return t.current(...r)},[])}},8532:(e,t,r)=>{r.r(t),r.d(t,{Primitive:()=>n,Tab:()=>en,Tabs:()=>et});var n={};r.r(n),r.d(n,{Tabs:()=>q,TabsContent:()=>X,TabsList:()=>Y,TabsTrigger:()=>W});var a=r(5155),o=r(2115),s=r(9688),l=r(5185),i=r(6081),u=r(7328),c=r(6101),d=r(1285),f=r(3655),m=r(9033),p=r(5845),v=r(4315),b="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[w,x,y]=(0,u.N)(g),[T,j]=(0,i.A)(g,[y]),[C,I]=T(g),N=o.forwardRef((e,t)=>(0,a.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(R,{...e,ref:t})})}));N.displayName=g;var R=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:s=!1,dir:i,currentTabStopId:u,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:w,onEntryFocus:y,preventScrollOnEntryFocus:T=!1,...j}=e,I=o.useRef(null),N=(0,c.s)(t,I),R=(0,v.jH)(i),[E,F]=(0,p.i)({prop:u,defaultProp:null!=d?d:null,onChange:w,caller:g}),[A,M]=o.useState(!1),S=(0,m.c)(y),P=x(r),k=o.useRef(!1),[L,G]=o.useState(0);return o.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(b,S),()=>e.removeEventListener(b,S)},[S]),(0,a.jsx)(C,{scope:r,orientation:n,dir:R,loop:s,currentTabStopId:E,onItemFocus:o.useCallback(e=>F(e),[F]),onItemShiftTab:o.useCallback(()=>M(!0),[]),onFocusableItemAdd:o.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>G(e=>e-1),[]),children:(0,a.jsx)(f.sG.div,{tabIndex:A||0===L?-1:0,"data-orientation":n,...j,ref:N,style:{outline:"none",...e.style},onMouseDown:(0,l.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,l.m)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(b,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),T)}}k.current=!1}),onBlur:(0,l.m)(e.onBlur,()=>M(!1))})})}),E="RovingFocusGroupItem",F=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:s=!1,tabStopId:i,children:u,...c}=e,m=(0,d.B)(),p=i||m,v=I(E,r),b=v.currentTabStopId===p,h=x(r),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:T}=v;return o.useEffect(()=>{if(n)return g(),()=>y()},[n,g,y]),(0,a.jsx)(w.ItemSlot,{scope:r,id:p,focusable:n,active:s,children:(0,a.jsx)(f.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{n?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,l.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,l.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return A[a]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}}),children:"function"==typeof u?u({isCurrentTabStop:b,hasTabStop:null!=T}):u})})});F.displayName=E;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var M=r(8905),S="Tabs",[P,k]=(0,i.A)(S,[j]),L=j(),[G,K]=P(S),_=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:s,orientation:l="horizontal",dir:i,activationMode:u="automatic",...c}=e,m=(0,v.jH)(i),[b,h]=(0,p.i)({prop:n,onChange:o,defaultProp:null!=s?s:"",caller:S});return(0,a.jsx)(G,{scope:r,baseId:(0,d.B)(),value:b,onValueChange:h,orientation:l,dir:m,activationMode:u,children:(0,a.jsx)(f.sG.div,{dir:m,"data-orientation":l,...c,ref:t})})});_.displayName=S;var Q="TabsList",B=o.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,s=K(Q,r),l=L(r);return(0,a.jsx)(N,{asChild:!0,...l,orientation:s.orientation,dir:s.dir,loop:n,children:(0,a.jsx)(f.sG.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});B.displayName=Q;var V="TabsTrigger",H=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...s}=e,i=K(V,r),u=L(r),c=z(i.baseId,n),d=J(i.baseId,n),m=n===i.value;return(0,a.jsx)(F,{asChild:!0,...u,focusable:!o,active:m,children:(0,a.jsx)(f.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":d,"data-state":m?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...s,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(n)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(n)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;m||o||!e||i.onValueChange(n)})})})});H.displayName=V;var O="TabsContent",U=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:s,children:l,...i}=e,u=K(O,r),c=z(u.baseId,n),d=J(u.baseId,n),m=n===u.value,p=o.useRef(m);return o.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(M.C,{present:s||m,children:r=>{let{present:n}=r;return(0,a.jsx)(f.sG.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:d,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&l})}})});function z(e,t){return"".concat(e,"-trigger-").concat(t)}function J(e,t){return"".concat(e,"-content-").concat(t)}U.displayName=O;let q=o.forwardRef((e,t)=>(0,a.jsx)(_,{ref:t,...e,className:(0,s.QP)("flex flex-col overflow-hidden rounded-xl border bg-fd-card",e.className)}));q.displayName="Tabs";let Y=o.forwardRef((e,t)=>(0,a.jsx)(B,{ref:t,...e,className:(0,s.QP)("flex flex-row items-end gap-4 overflow-x-auto bg-fd-secondary px-4 text-fd-muted-foreground",e.className)}));Y.displayName="TabsList";let W=o.forwardRef((e,t)=>(0,a.jsx)(H,{ref:t,...e,className:(0,s.QP)("whitespace-nowrap border-b border-transparent py-2 text-sm font-medium transition-colors hover:text-fd-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-fd-primary data-[state=active]:text-fd-primary",e.className)}));W.displayName="TabsTrigger";let X=o.forwardRef((e,t)=>(0,a.jsx)(U,{ref:t,...e,className:(0,s.QP)("p-4",e.className)}));X.displayName="TabsContent";var Z=r(8265);let $=new Map,ee=(0,o.createContext)(null);function et(e){let{groupId:t,items:r=[],persist:n=!1,defaultIndex:l=0,updateAnchor:i=!1,...u}=e,c=(0,o.useMemo)(()=>r.map(e=>er(e)),[r]),[d,f]=(0,o.useState)(c[l]),m=(0,o.useMemo)(()=>new Map,[]),p=(0,o.useMemo)(()=>[],[r]),v=(0,Z.J)(e=>{c.includes(e)&&f(e)});return(0,o.useLayoutEffect)(()=>{var e;if(!t)return;let r=n?localStorage.getItem(t):sessionStorage.getItem(t);r&&v(r);let a=null!=(e=$.get(t))?e:[];return a.push(v),$.set(t,a),()=>{var e;let r=null!=(e=$.get(t))?e:[];$.set(t,r.filter(e=>e!==v))}},[t,v,n]),(0,o.useLayoutEffect)(()=>{let e=window.location.hash.slice(1);if(e){for(let[t,r]of m.entries())if(r===e){f(t);break}}},[m]),(0,a.jsxs)(q,{value:d,onValueChange:e=>{if(i){let t=m.get(e);t&&window.history.replaceState(null,"","#".concat(t))}if(t){var r;null==(r=$.get(t))||r.forEach(t=>{t(e)}),n?localStorage.setItem(t,e):sessionStorage.setItem(t,e)}else f(e)},...u,className:(0,s.QP)("my-4",u.className),children:[(0,a.jsx)(Y,{children:c.map((e,t)=>(0,a.jsx)(W,{value:e,children:r[t]},e))}),(0,a.jsx)(ee.Provider,{value:(0,o.useMemo)(()=>({items:r,valueToIdMap:m,collection:p}),[m,p,r]),children:u.children})]})}function er(e){return e.toLowerCase().replace(/\s/,"-")}function en(e){let{value:t,className:r,...n}=e,l=(0,o.useContext)(ee),i=null!=t?t:null==l?void 0:l.items.at(function(){let e=(0,o.useId)(),t=(0,o.useContext)(ee);if(!t)throw Error("You must wrap your component in <Tabs>");let r=t.collection;function n(){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}return(0,o.useMemo)(()=>{n(),r.includes(e)||r.push(e)},[r]),(0,o.useEffect)(()=>n,[]),r.indexOf(e)}());if(!i)throw Error("Failed to resolve tab `value`, please pass a `value` prop to the Tab component.");let u=er(i);return n.id&&l&&l.valueToIdMap.set(u,n.id),(0,a.jsx)(X,{value:u,className:(0,s.QP)("prose-no-margin [&>figure:only-child]:-m-4 [&>figure:only-child]:rounded-none [&>figure:only-child]:border-none",r),...n,children:n.children})}}}]);