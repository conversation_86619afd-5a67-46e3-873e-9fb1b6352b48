"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_algolia-NTWLS6J3_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-NTWLS6J3.js":
/*!*************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/algolia-NTWLS6J3.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: () => (/* binding */ groupResults),\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_MLKGABMK_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-MLKGABMK.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-MLKGABMK.js\");\n\n\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(index, query, tag, options) {\n  let filters = options?.filters;\n  if (tag) filters = filters ? `tag:${tag} AND (${filters})` : `tag:${tag}`;\n  if (query.length === 0) {\n    const result2 = await index.search(query, {\n      distinct: 1,\n      hitsPerPage: 8,\n      ...options,\n      filters\n    });\n    return groupResults(result2.hits).filter((hit) => hit.type === \"page\");\n  }\n  const result = await index.search(query, {\n    distinct: 5,\n    hitsPerPage: 10,\n    ...options,\n    filters\n  });\n  return groupResults(result.hits);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-NTWLS6J3.js\n"));

/***/ })

}]);