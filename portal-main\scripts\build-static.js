#!/usr/bin/env node

/**
 * 完整的静态导出构建脚本
 * 包含所有必要的预处理步骤
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 导入构建脚本
const { buildGitHubStarsData } = require('./build-github-stars')
const { buildSearchIndex } = require('./build-search-index')

async function buildStatic() {
  console.log('🚀 开始静态导出构建...')
  
  try {
    // 步骤1: 构建GitHub星标数据
    console.log('\n📊 步骤1: 构建GitHub星标数据...')
    await buildGitHubStarsData()
    
    // 步骤2: 构建搜索索引
    console.log('\n🔍 步骤2: 构建搜索索引...')
    await buildSearchIndex()
    
    // 步骤3: 运行Next.js构建
    console.log('\n⚡ 步骤3: 运行Next.js构建...')
    execSync('next build', { stdio: 'inherit' })
    
    // 步骤4: 验证输出
    console.log('\n✅ 步骤4: 验证构建输出...')
    const outDir = path.join(process.cwd(), 'out')
    
    if (fs.existsSync(outDir)) {
      console.log('✅ 静态文件已生成到 out/ 目录')
      
      // 检查关键文件
      const keyFiles = [
        'github-stars.json',
        'search-index.json',
        'index.html',
        'en/index.html',
        'cn/index.html'
      ]
      
      keyFiles.forEach(file => {
        const filePath = path.join(outDir, file)
        if (fs.existsSync(filePath)) {
          console.log(`✅ ${file} - 存在`)
        } else {
          console.log(`⚠️  ${file} - 缺失`)
        }
      })
      
      // 显示目录结构
      console.log('\n📁 输出目录结构:')
      showDirectoryStructure(outDir, '', 2)
      
    } else {
      console.error('❌ 构建输出目录不存在')
      process.exit(1)
    }
    
    console.log('\n🎉 静态导出构建完成!')
    console.log('💡 提示: 可以使用以下命令预览:')
    console.log('   npx serve out')
    console.log('   或者将 out/ 目录部署到任何静态托管服务')
    
  } catch (error) {
    console.error('\n💥 构建失败:', error.message)
    process.exit(1)
  }
}

function showDirectoryStructure(dir, prefix = '', maxDepth = 3, currentDepth = 0) {
  if (currentDepth >= maxDepth) return
  
  try {
    const items = fs.readdirSync(dir).slice(0, 10) // 限制显示数量
    
    items.forEach((item, index) => {
      const isLast = index === items.length - 1
      const itemPath = path.join(dir, item)
      const stat = fs.statSync(itemPath)
      
      const connector = isLast ? '└── ' : '├── '
      console.log(prefix + connector + item)
      
      if (stat.isDirectory() && currentDepth < maxDepth - 1) {
        const newPrefix = prefix + (isLast ? '    ' : '│   ')
        showDirectoryStructure(itemPath, newPrefix, maxDepth, currentDepth + 1)
      }
    })
    
    if (fs.readdirSync(dir).length > 10) {
      console.log(prefix + '└── ...')
    }
  } catch (error) {
    console.log(prefix + '└── (无法读取)')
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  buildStatic()
}

module.exports = { buildStatic }
