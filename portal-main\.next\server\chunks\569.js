"use strict";exports.id=569,exports.ids=[569],exports.modules={11310:(e,t,n)=>{n.d(t,{DT:()=>l,FP:()=>i,TK:()=>o,Zn:()=>a,aM:()=>s,x3:()=>u});var r=n(65835);function o(e){return"string"==typeof e?{pathname:e}:e}function a(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}function i({pathname:e,locale:t,params:n,pathnames:o,query:i}){function s(e){let t=o[e];return t||(t=e),t}function l(e,o){let s=(0,r.Wl)(e,t,o);return n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),s=s.replace(RegExp(n,"g"),r)}),s=s.replace(/\[\[\.\.\..+\]\]/g,""),s=(0,r.po)(s),i&&(s+=a(i)),s}if("string"==typeof e)return l(s(e),e);{let{pathname:t,...n}=e;return{...n,pathname:l(s(t),t)}}}function s(e,t,n){let o=(0,r.FD)(Object.keys(n)),a=decodeURI(t);for(let t of o){let o=n[t];if("string"==typeof o){if((0,r.ql)(o,a))return t}else if((0,r.ql)((0,r.Wl)(o,e,t),a))return t}return t}function l(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function u(e,t,n,o){let a,{mode:i}=n.localePrefix;return void 0!==o?a=o:(0,r._x)(e)&&("always"===i?a=!0:"as-needed"===i&&(a=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),a?(0,r.PJ)((0,r.XP)(t,n.localePrefix),e):e}},28966:(e,t,n)=>{n.d(t,{Navbar:()=>o,NavbarLink:()=>u,NavbarMenu:()=>a,NavbarMenuContent:()=>i,NavbarMenuLink:()=>l,NavbarMenuTrigger:()=>s});var r=n(12907);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\navbar.js","Navbar"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call NavbarMenu() from the server but NavbarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\navbar.js","NavbarMenu"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call NavbarMenuContent() from the server but NavbarMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\navbar.js","NavbarMenuContent"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call NavbarMenuTrigger() from the server but NavbarMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\navbar.js","NavbarMenuTrigger"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call NavbarMenuLink() from the server but NavbarMenuLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\navbar.js","NavbarMenuLink"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call NavbarLink() from the server but NavbarLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\navbar.js","NavbarLink")},29425:(e,t,n)=>{n.d(t,{m:()=>v});var r=n(37413),o=n(61120),a=n(85752),i=n(8974),s=n(3231),l=n(28966),u=n(36163),c=n(24274),d=n(78941),f=n(47909),m=n(88598),p=n(69767),h=n(48615);function v(e){let{nav:t,links:n,githubUrl:o,i18n:l,disableThemeSwitch:u=!1,themeSwitch:c={enabled:!u},searchToggle:d,...f}=e;return(0,r.jsx)(s.NavProvider,{transparentMode:t?.transparentMode,children:(0,r.jsxs)("main",{id:"nd-home-layout",...f,className:(0,i.QP)("flex flex-1 flex-col pt-14",f.className),children:[(0,a.NI)(t,(0,r.jsx)(g,{links:n,nav:t,themeSwitch:c,searchToggle:d,i18n:l,githubUrl:o})),e.children]})})}function g({nav:e={},i18n:t=!1,links:n,githubUrl:i,themeSwitch:s,searchToggle:v}){let g=(0,o.useMemo)(()=>(0,a.CY)(n,i),[n,i]),w=g.filter(e=>["nav","all"].includes(e.on??"all")),N=g.filter(e=>["menu","all"].includes(e.on??"all"));return(0,r.jsxs)(l.Navbar,{children:[(0,r.jsx)(p.default,{href:e.url??"/",className:"inline-flex items-center gap-2.5 font-semibold",children:e.title}),e.children,(0,r.jsx)("ul",{className:"flex flex-row items-center gap-2 px-6 max-sm:hidden",children:w.filter(e=>!b(e)).map((e,t)=>(0,r.jsx)(x,{item:e,className:"text-sm"},t))}),(0,r.jsxs)("div",{className:"flex flex-row items-center justify-end gap-1.5 flex-1",children:[(0,a.Q0)("sm",v,(0,r.jsx)(u.SearchToggle,{className:"lg:hidden",hideIfDisabled:!0})),(0,a.Q0)("lg",v,(0,r.jsx)(u.LargeSearchToggle,{className:"w-full max-w-[240px] max-lg:hidden",hideIfDisabled:!0})),(0,a.NI)(s,(0,r.jsx)(c.ThemeToggle,{className:"max-lg:hidden",mode:s?.mode})),t?(0,r.jsx)(d.LanguageToggle,{className:"max-lg:hidden",children:(0,r.jsx)(f.A,{className:"size-5"})}):null]}),(0,r.jsxs)("ul",{className:"flex flex-row items-center",children:[w.filter(b).map((e,t)=>(0,r.jsx)(x,{item:e,className:"-me-1.5 max-lg:hidden"},t)),(0,r.jsxs)(h.Menu,{className:"lg:hidden",children:[(0,r.jsx)(h.MenuTrigger,{"aria-label":"Toggle Menu",className:"group -me-2",enableHover:e.enableHoverToOpen,children:(0,r.jsx)(m.A,{className:"size-3 transition-transform duration-300 group-data-[state=open]:rotate-180"})}),(0,r.jsxs)(h.MenuContent,{className:"sm:flex-row sm:items-center sm:justify-end",children:[N.filter(e=>!b(e)).map((e,t)=>(0,r.jsx)(h.MenuLinkItem,{item:e,className:"sm:hidden"},t)),(0,r.jsxs)("div",{className:"-ms-1.5 flex flex-row items-center gap-1.5 max-sm:mt-2",children:[N.filter(b).map((e,t)=>(0,r.jsx)(h.MenuLinkItem,{item:e,className:"-me-1.5"},t)),(0,r.jsx)("div",{role:"separator",className:"flex-1"}),t?(0,r.jsxs)(d.LanguageToggle,{children:[(0,r.jsx)(f.A,{className:"size-5"}),(0,r.jsx)(d.LanguageToggleText,{}),(0,r.jsx)(m.A,{className:"size-3 text-fd-muted-foreground"})]}):null,(0,a.NI)(s,(0,r.jsx)(c.ThemeToggle,{mode:s?.mode}))]})]})]})]})]})}function x({item:e,...t}){if("custom"===e.type)return(0,r.jsx)("div",{...t,children:e.children});if("menu"===e.type){let n=e.items.map((e,t)=>{if("custom"===e.type)return(0,r.jsx)(o.Fragment,{children:e.children},t);let{banner:n=e.icon?(0,r.jsx)("div",{className:"w-fit rounded-md border bg-fd-muted p-1 [&_svg]:size-4",children:e.icon}):null,...a}=e.menu??{};return(0,r.jsx)(l.NavbarMenuLink,{href:e.url,...a,children:a.children??(0,r.jsxs)(r.Fragment,{children:[n,(0,r.jsx)("p",{className:"-mb-1 text-sm font-medium",children:e.text}),e.description?(0,r.jsx)("p",{className:"text-[13px] text-fd-muted-foreground",children:e.description}):null]})},t)});return(0,r.jsxs)(l.NavbarMenu,{children:[(0,r.jsx)(l.NavbarMenuTrigger,{...t,children:e.url?(0,r.jsx)(p.default,{href:e.url,children:e.text}):e.text}),(0,r.jsx)(l.NavbarMenuContent,{children:n})]})}return(0,r.jsx)(l.NavbarLink,{...t,item:e,variant:e.type,"aria-label":"icon"===e.type?e.label:void 0,children:"icon"===e.type?e.icon:e.text})}function b(e){return"secondary"in e&&!0===e.secondary||"icon"===e.type}},31116:(e,t,n)=>{n.d(t,{Navbar:()=>m,NavbarLink:()=>b,NavbarMenu:()=>p,NavbarMenuContent:()=>h,NavbarMenuLink:()=>g,NavbarMenuTrigger:()=>v});var r=n(60687),o=n(43210),a=n(24224),i=n(46250),s=n(82348),l=n(97363),u=n(68386),c=n(353),d=n(22880);let f=(0,a.F)("inline-flex items-center gap-1 p-2 text-fd-muted-foreground transition-colors hover:text-fd-accent-foreground data-[active=true]:text-fd-primary [&_svg]:size-4");function m(e){let[t,n]=(0,o.useState)(""),{isTransparent:a}=(0,c.h)();return(0,r.jsx)(u.KS,{value:t,onValueChange:n,asChild:!0,children:(0,r.jsxs)("header",{id:"nd-nav",...e,className:(0,s.QP)("fixed left-1/2 top-(--fd-banner-height) z-40 box-content w-full max-w-fd-container -translate-x-1/2 border-b border-fd-foreground/10 transition-colors lg:mt-2 lg:w-[calc(100%-1rem)] lg:rounded-2xl lg:border",t.length>0?"shadow-lg":"shadow-sm",(!a||t.length>0)&&"bg-fd-background/80 backdrop-blur-lg",e.className),children:[(0,r.jsx)(u.SK,{className:"flex h-14 w-full flex-row items-center px-4 lg:h-12",asChild:!0,children:(0,r.jsx)("nav",{children:e.children})}),(0,r.jsx)(u.QW,{})]})})}let p=u.JD;function h(e){return(0,r.jsx)(u.hA,{...e,className:(0,s.QP)("grid grid-cols-1 gap-3 px-4 pb-4 md:grid-cols-2 lg:grid-cols-3",e.className),children:e.children})}function v(e){return(0,r.jsx)(u.wd,{...e,className:(0,s.QP)(f(),"rounded-md",e.className),children:e.children})}function g(e){return(0,r.jsx)(u.Ws,{asChild:!0,children:(0,r.jsx)(i.default,{...e,className:(0,s.QP)("flex flex-col gap-2 rounded-lg border bg-fd-card p-3 transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground",e.className),children:e.children})})}let x=(0,a.F)("",{variants:{variant:{main:f(),button:(0,d.r)({color:"secondary",className:"gap-1.5 [&_svg]:size-4"}),icon:(0,d.r)({color:"ghost",size:"icon"})}},defaultVariants:{variant:"main"}});function b({item:e,variant:t,...n}){return(0,r.jsx)(u.JD,{children:(0,r.jsx)(u.Ws,{asChild:!0,children:(0,r.jsx)(l.BaseLinkItem,{...n,item:e,className:(0,s.QP)(x({variant:t}),n.className),children:n.children})})})}},39130:(e,t,n)=>{n.d(t,{default:()=>u});var r=n(85814),o=n(16189),a=n(43210),i=n(8610),s=n(99128),l=n(60687),u=(0,a.forwardRef)(function({href:e,locale:t,localeCookie:n,onClick:a,prefetch:u,...c},d){let f=(0,i.Ym)(),m=null!=t&&t!==f,p=(0,o.usePathname)();return m&&(u=!1),(0,l.jsx)(r,{ref:d,href:e,hrefLang:m?t:void 0,onClick:function(e){(0,s.A)(n,p,f,t),a&&a(e)},prefetch:u,...c})})},48615:(e,t,n)=>{n.d(t,{Menu:()=>a,MenuContent:()=>s,MenuLinkItem:()=>o,MenuTrigger:()=>i});var r=n(12907);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call MenuLinkItem() from the server but MenuLinkItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\menu.js","MenuLinkItem"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call Menu() from the server but Menu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\menu.js","Menu"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call MenuTrigger() from the server but MenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\menu.js","MenuTrigger"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call MenuContent() from the server but MenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\portal-main\\portal-main\\node_modules\\fumadocs-ui\\dist\\layouts\\home\\menu.js","MenuContent")},65809:(e,t,n)=>{n.d(t,{Menu:()=>f,MenuContent:()=>p,MenuLinkItem:()=>d,MenuTrigger:()=>m});var r=n(60687),o=n(97363),a=n(82348),i=n(68386),s=n(46250),l=n(24224),u=n(22880);let c=(0,l.F)("",{variants:{variant:{main:"inline-flex items-center gap-2 py-1.5 transition-colors hover:text-fd-popover-foreground/50 data-[active=true]:font-medium data-[active=true]:text-fd-primary [&_svg]:size-4",icon:(0,u.r)({size:"icon",color:"ghost"}),button:(0,u.r)({color:"secondary",className:"gap-1.5 [&_svg]:size-4"})}},defaultVariants:{variant:"main"}});function d({item:e,...t}){if("custom"===e.type)return(0,r.jsx)("div",{className:(0,a.QP)("grid",t.className),children:e.children});if("menu"===e.type){let n=(0,r.jsxs)(r.Fragment,{children:[e.icon,e.text]});return(0,r.jsxs)("div",{className:(0,a.QP)("mb-4 flex flex-col",t.className),children:[(0,r.jsx)("p",{className:"mb-1 text-sm text-fd-muted-foreground",children:e.url?(0,r.jsx)(i.Ws,{asChild:!0,children:(0,r.jsx)(s.default,{href:e.url,children:n})}):n}),e.items.map((e,t)=>(0,r.jsx)(d,{item:e},t))]})}return(0,r.jsx)(i.Ws,{asChild:!0,children:(0,r.jsxs)(o.BaseLinkItem,{item:e,className:(0,a.QP)(c({variant:e.type}),t.className),"aria-label":"icon"===e.type?e.label:void 0,children:[e.icon,"icon"===e.type?void 0:e.text]})})}let f=i.JD;function m({enableHover:e=!1,...t}){return(0,r.jsx)(i.wd,{...t,onPointerMove:e?void 0:e=>e.preventDefault(),className:(0,a.QP)((0,u.r)({size:"icon",color:"ghost"}),t.className),children:t.children})}function p(e){return(0,r.jsx)(i.hA,{...e,className:(0,a.QP)("flex flex-col p-4",e.className),children:e.children})}},65835:(e,t,n)=>{function r(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function o(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function a(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function i(e,t){return t===e||t.startsWith(`${e}/`)}function s(e,t,n){return"string"==typeof e?e:e[t]||n}function l(e){let t=function(){try{return!0}catch{return!1}}();if("/"!==e){let n=e.endsWith("/");t&&!n?e+="/":!t&&n&&(e=e.slice(0,-1))}return e}function u(e,t){let n=l(e),r=l(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(n).test(r)}function c(e,t){return"never"!==t.mode&&t.prefixes?.[e]||d(e)}function d(e){return"/"+e}function f(e){return e.includes("[[...")}function m(e){return e.includes("[...")}function p(e){return e.includes("[")}function h(e,t){let n=e.split("/"),r=t.split("/"),o=Math.max(n.length,r.length);for(let e=0;e<o;e++){let t=n[e],o=r[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!p(t)&&p(o))return -1;if(p(t)&&!p(o))return 1;if(!m(t)&&m(o))return -1;if(m(t)&&!m(o))return 1;if(!f(t)&&f(o))return -1;if(f(t)&&!f(o))return 1}}return 0}function v(e){return e.sort(h)}function g(e){return"function"==typeof e.then}n.d(t,{FD:()=>v,MY:()=>o,PJ:()=>a,Wl:()=>s,XP:()=>c,_x:()=>r,bL:()=>d,po:()=>l,ql:()=>u,wO:()=>i,yL:()=>g})},68386:(e,t,n)=>{n.d(t,{KS:()=>em,hA:()=>eg,JD:()=>eh,Ws:()=>ex,SK:()=>ep,wd:()=>ev,QW:()=>eb});var r=n(60687),o=n(43210),a=n(51215),i=n(11273),s=n(70569),l=n(14163),u=n(65551),c=n(98599),d=n(43),f=n(46059),m=n(96963),p=n(9510),h=n(31355),v=n(66156),g=n(13495),x=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),b=o.forwardRef((e,t)=>(0,r.jsx)(l.sG.span,{...e,ref:t,style:{...x,...e.style}}));b.displayName="VisuallyHidden";var w="NavigationMenu",[N,j,C]=(0,p.N)(w),[y,M,R]=(0,p.N)(w),[T,E]=(0,i.A)(w,[C,R]),[k,I]=T(w),[L,P]=T(w),D=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:a,onValueChange:i,defaultValue:s,delayDuration:f=200,skipDelayDuration:m=300,orientation:p="horizontal",dir:h,...v}=e,[g,x]=o.useState(null),b=(0,c.s)(t,e=>x(e)),N=(0,d.jH)(h),j=o.useRef(0),C=o.useRef(0),y=o.useRef(0),[M,R]=o.useState(!0),[T,E]=(0,u.i)({prop:a,onChange:e=>{let t=m>0;""!==e?(window.clearTimeout(y.current),t&&R(!1)):(window.clearTimeout(y.current),y.current=window.setTimeout(()=>R(!0),m)),i?.(e)},defaultProp:s??"",caller:w}),k=o.useCallback(()=>{window.clearTimeout(C.current),C.current=window.setTimeout(()=>E(""),150)},[E]),I=o.useCallback(e=>{window.clearTimeout(C.current),E(e)},[E]),L=o.useCallback(e=>{T===e?window.clearTimeout(C.current):j.current=window.setTimeout(()=>{window.clearTimeout(C.current),E(e)},f)},[T,E,f]);return o.useEffect(()=>()=>{window.clearTimeout(j.current),window.clearTimeout(C.current),window.clearTimeout(y.current)},[]),(0,r.jsx)(_,{scope:n,isRootMenu:!0,value:T,dir:N,orientation:p,rootNavigationMenu:g,onTriggerEnter:e=>{window.clearTimeout(j.current),M?L(e):I(e)},onTriggerLeave:()=>{window.clearTimeout(j.current),k()},onContentEnter:()=>window.clearTimeout(C.current),onContentLeave:k,onItemSelect:e=>{E(t=>t===e?"":e)},onItemDismiss:()=>E(""),children:(0,r.jsx)(l.sG.nav,{"aria-label":"Main","data-orientation":p,dir:N,...v,ref:b})})});D.displayName=w;var A="NavigationMenuSub";o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:o,onValueChange:a,defaultValue:i,orientation:s="horizontal",...c}=e,d=I(A,n),[f,m]=(0,u.i)({prop:o,onChange:a,defaultProp:i??"",caller:A});return(0,r.jsx)(_,{scope:n,isRootMenu:!1,value:f,dir:d.dir,orientation:s,rootNavigationMenu:d.rootNavigationMenu,onTriggerEnter:e=>m(e),onItemSelect:e=>m(e),onItemDismiss:()=>m(""),children:(0,r.jsx)(l.sG.div,{"data-orientation":s,...c,ref:t})})}).displayName=A;var _=e=>{let{scope:t,isRootMenu:n,rootNavigationMenu:a,dir:i,orientation:s,children:l,value:u,onItemSelect:c,onItemDismiss:d,onTriggerEnter:f,onTriggerLeave:p,onContentEnter:h,onContentLeave:v}=e,[x,b]=o.useState(null),[w,j]=o.useState(new Map),[C,y]=o.useState(null);return(0,r.jsx)(k,{scope:t,isRootMenu:n,rootNavigationMenu:a,value:u,previousValue:function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(u),baseId:(0,m.B)(),dir:i,orientation:s,viewport:x,onViewportChange:b,indicatorTrack:C,onIndicatorTrackChange:y,onTriggerEnter:(0,g.c)(f),onTriggerLeave:(0,g.c)(p),onContentEnter:(0,g.c)(h),onContentLeave:(0,g.c)(v),onItemSelect:(0,g.c)(c),onItemDismiss:(0,g.c)(d),onViewportContentChange:o.useCallback((e,t)=>{j(n=>(n.set(e,t),new Map(n)))},[]),onViewportContentRemove:o.useCallback(e=>{j(t=>t.has(e)?(t.delete(e),new Map(t)):t)},[]),children:(0,r.jsx)(N.Provider,{scope:t,children:(0,r.jsx)(L,{scope:t,items:w,children:l})})})},S="NavigationMenuList",F=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,a=I(S,n),i=(0,r.jsx)(l.sG.ul,{"data-orientation":a.orientation,...o,ref:t});return(0,r.jsx)(l.sG.div,{style:{position:"relative"},ref:a.onIndicatorTrackChange,children:(0,r.jsx)(N.Slot,{scope:n,children:a.isRootMenu?(0,r.jsx)(en,{asChild:!0,children:i}):i})})});F.displayName=S;var O="NavigationMenuItem",[z,K]=T(O),Q=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:a,...i}=e,s=(0,m.B)(),u=o.useRef(null),c=o.useRef(null),d=o.useRef(null),f=o.useRef(()=>{}),p=o.useRef(!1),h=o.useCallback((e="start")=>{if(u.current){f.current();let t=ea(u.current);t.length&&ei("start"===e?t:t.reverse())}},[]),v=o.useCallback(()=>{if(u.current){let e=ea(u.current);e.length&&(f.current=function(e){return e.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}),()=>{e.forEach(e=>{let t=e.dataset.tabindex;e.setAttribute("tabindex",t)})}}(e))}},[]);return(0,r.jsx)(z,{scope:n,value:a||s||"LEGACY_REACT_AUTO_VALUE",triggerRef:c,contentRef:u,focusProxyRef:d,wasEscapeCloseRef:p,onEntryKeyDown:h,onFocusProxyEnter:h,onRootContentClose:v,onContentFocusOutside:v,children:(0,r.jsx)(l.sG.li,{...i,ref:t})})});Q.displayName=O;var W="NavigationMenuTrigger",U=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,disabled:a,...i}=e,u=I(W,e.__scopeNavigationMenu),d=K(W,e.__scopeNavigationMenu),f=o.useRef(null),m=(0,c.s)(f,d.triggerRef,t),p=eu(u.baseId,d.value),h=ec(u.baseId,d.value),v=o.useRef(!1),g=o.useRef(!1),x=d.value===u.value;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.ItemSlot,{scope:n,value:d.value,children:(0,r.jsx)(eo,{asChild:!0,children:(0,r.jsx)(l.sG.button,{id:p,disabled:a,"data-disabled":a?"":void 0,"data-state":el(x),"aria-expanded":x,"aria-controls":h,...i,ref:m,onPointerEnter:(0,s.m)(e.onPointerEnter,()=>{g.current=!1,d.wasEscapeCloseRef.current=!1}),onPointerMove:(0,s.m)(e.onPointerMove,ed(()=>{a||g.current||d.wasEscapeCloseRef.current||v.current||(u.onTriggerEnter(d.value),v.current=!0)})),onPointerLeave:(0,s.m)(e.onPointerLeave,ed(()=>{a||(u.onTriggerLeave(),v.current=!1)})),onClick:(0,s.m)(e.onClick,()=>{u.onItemSelect(d.value),g.current=x}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{let t={horizontal:"ArrowDown",vertical:"rtl"===u.dir?"ArrowLeft":"ArrowRight"}[u.orientation];x&&e.key===t&&(d.onEntryKeyDown(),e.preventDefault())})})})}),x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b,{"aria-hidden":!0,tabIndex:0,ref:d.focusProxyRef,onFocus:e=>{let t=d.contentRef.current,n=e.relatedTarget,r=n===f.current,o=t?.contains(n);(r||!o)&&d.onFocusProxyEnter(r?"start":"end")}}),u.viewport&&(0,r.jsx)("span",{"aria-owns":h})]})]})});U.displayName=W;var $="navigationMenu.linkSelect",G=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,active:o,onSelect:a,...i}=e;return(0,r.jsx)(eo,{asChild:!0,children:(0,r.jsx)(l.sG.a,{"data-active":o?"":void 0,"aria-current":o?"page":void 0,...i,ref:t,onClick:(0,s.m)(e.onClick,e=>{let t=e.target,n=new CustomEvent($,{bubbles:!0,cancelable:!0});if(t.addEventListener($,e=>a?.(e),{once:!0}),(0,l.hO)(t,n),!n.defaultPrevented&&!e.metaKey){let e=new CustomEvent(B,{bubbles:!0,cancelable:!0});(0,l.hO)(t,e)}},{checkForDefaultPrevented:!1})})})});G.displayName="NavigationMenuLink";var V="NavigationMenuIndicator";o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=I(V,e.__scopeNavigationMenu),s=!!i.value;return i.indicatorTrack?a.createPortal((0,r.jsx)(f.C,{present:n||s,children:(0,r.jsx)(H,{...o,ref:t})}),i.indicatorTrack):null}).displayName=V;var H=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...a}=e,i=I(V,n),s=j(n),[u,c]=o.useState(null),[d,f]=o.useState(null),m="horizontal"===i.orientation,p=!!i.value;o.useEffect(()=>{let e=s(),t=e.find(e=>e.value===i.value)?.ref.current;t&&c(t)},[s,i.value]);let h=()=>{u&&f({size:m?u.offsetWidth:u.offsetHeight,offset:m?u.offsetLeft:u.offsetTop})};return es(u,h),es(i.indicatorTrack,h),d?(0,r.jsx)(l.sG.div,{"aria-hidden":!0,"data-state":p?"visible":"hidden","data-orientation":i.orientation,...a,ref:t,style:{position:"absolute",...m?{left:0,width:d.size+"px",transform:`translateX(${d.offset}px)`}:{top:0,height:d.size+"px",transform:`translateY(${d.offset}px)`},...a.style}}):null}),q="NavigationMenuContent",J=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,a=I(q,e.__scopeNavigationMenu),i=K(q,e.__scopeNavigationMenu),l=(0,c.s)(i.contentRef,t),u=i.value===a.value,d={value:i.value,triggerRef:i.triggerRef,focusProxyRef:i.focusProxyRef,wasEscapeCloseRef:i.wasEscapeCloseRef,onContentFocusOutside:i.onContentFocusOutside,onRootContentClose:i.onRootContentClose,...o};return a.viewport?(0,r.jsx)(Y,{forceMount:n,...d,ref:l}):(0,r.jsx)(f.C,{present:n||u,children:(0,r.jsx)(X,{"data-state":el(u),...d,ref:l,onPointerEnter:(0,s.m)(e.onPointerEnter,a.onContentEnter),onPointerLeave:(0,s.m)(e.onPointerLeave,ed(a.onContentLeave)),style:{pointerEvents:!u&&a.isRootMenu?"none":void 0,...d.style}})})});J.displayName=q;var Y=o.forwardRef((e,t)=>{let{onViewportContentChange:n,onViewportContentRemove:r}=I(q,e.__scopeNavigationMenu);return(0,v.N)(()=>{n(e.value,{ref:t,...e})},[e,t,n]),(0,v.N)(()=>()=>r(e.value),[e.value,r]),null}),B="navigationMenu.rootContentDismiss",X=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:a,triggerRef:i,focusProxyRef:l,wasEscapeCloseRef:u,onRootContentClose:d,onContentFocusOutside:f,...m}=e,p=I(q,n),v=o.useRef(null),g=(0,c.s)(v,t),x=eu(p.baseId,a),b=ec(p.baseId,a),w=j(n),N=o.useRef(null),{onItemDismiss:C}=p;o.useEffect(()=>{let e=v.current;if(p.isRootMenu&&e){let t=()=>{C(),d(),e.contains(document.activeElement)&&i.current?.focus()};return e.addEventListener(B,t),()=>e.removeEventListener(B,t)}},[p.isRootMenu,e.value,i,C,d]);let y=o.useMemo(()=>{let e=w().map(e=>e.value);"rtl"===p.dir&&e.reverse();let t=e.indexOf(p.value),n=e.indexOf(p.previousValue),r=a===p.value,o=n===e.indexOf(a);if(!r&&!o)return N.current;let i=(()=>{if(t!==n){if(r&&-1!==n)return t>n?"from-end":"from-start";if(o&&-1!==t)return t>n?"to-start":"to-end"}return null})();return N.current=i,i},[p.previousValue,p.value,p.dir,w,a]);return(0,r.jsx)(en,{asChild:!0,children:(0,r.jsx)(h.qW,{id:b,"aria-labelledby":x,"data-motion":y,"data-orientation":p.orientation,...m,ref:g,disableOutsidePointerEvents:!1,onDismiss:()=>{let e=new Event(B,{bubbles:!0,cancelable:!0});v.current?.dispatchEvent(e)},onFocusOutside:(0,s.m)(e.onFocusOutside,e=>{f();let t=e.target;p.rootNavigationMenu?.contains(t)&&e.preventDefault()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.target,n=w().some(e=>e.ref.current?.contains(t)),r=p.isRootMenu&&p.viewport?.contains(t);(n||r||!p.isRootMenu)&&e.preventDefault()}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{let t=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!t){let t=ea(e.currentTarget),n=document.activeElement,r=t.findIndex(e=>e===n);ei(e.shiftKey?t.slice(0,r).reverse():t.slice(r+1,t.length))?e.preventDefault():l.current?.focus()}}),onEscapeKeyDown:(0,s.m)(e.onEscapeKeyDown,e=>{u.current=!0})})})}),Z="NavigationMenuViewport",ee=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,a=!!I(Z,e.__scopeNavigationMenu).value;return(0,r.jsx)(f.C,{present:n||a,children:(0,r.jsx)(et,{...o,ref:t})})});ee.displayName=Z;var et=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,children:a,...i}=e,u=I(Z,n),d=(0,c.s)(t,u.onViewportChange),m=P(q,e.__scopeNavigationMenu),[p,h]=o.useState(null),[v,g]=o.useState(null),x=p?p?.width+"px":void 0,b=p?p?.height+"px":void 0,w=!!u.value,N=w?u.value:u.previousValue;return es(v,()=>{v&&h({width:v.offsetWidth,height:v.offsetHeight})}),(0,r.jsx)(l.sG.div,{"data-state":el(w),"data-orientation":u.orientation,...i,ref:d,style:{pointerEvents:!w&&u.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":x,"--radix-navigation-menu-viewport-height":b,...i.style},onPointerEnter:(0,s.m)(e.onPointerEnter,u.onContentEnter),onPointerLeave:(0,s.m)(e.onPointerLeave,ed(u.onContentLeave)),children:Array.from(m.items).map(([e,{ref:t,forceMount:n,...o}])=>{let a=N===e;return(0,r.jsx)(f.C,{present:n||a,children:(0,r.jsx)(X,{...o,ref:(0,c.t)(t,e=>{a&&e&&g(e)})})},e)})})}),en=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,a=I("FocusGroup",n);return(0,r.jsx)(y.Provider,{scope:n,children:(0,r.jsx)(y.Slot,{scope:n,children:(0,r.jsx)(l.sG.div,{dir:a.dir,...o,ref:t})})})}),er=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],eo=o.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,a=M(n),i=I("FocusGroupItem",n);return(0,r.jsx)(y.ItemSlot,{scope:n,children:(0,r.jsx)(l.sG.button,{...o,ref:t,onKeyDown:(0,s.m)(e.onKeyDown,e=>{if(["Home","End",...er].includes(e.key)){let t=a().map(e=>e.ref.current);if(["rtl"===i.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(e.key)&&t.reverse(),er.includes(e.key)){let n=t.indexOf(e.currentTarget);t=t.slice(n+1)}setTimeout(()=>ei(t)),e.preventDefault()}})})})});function ea(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ei(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}function es(e,t){let n=(0,g.c)(t);(0,v.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}function el(e){return e?"open":"closed"}function eu(e,t){return`${e}-trigger-${t}`}function ec(e,t){return`${e}-content-${t}`}function ed(e){return t=>"mouse"===t.pointerType?e(t):void 0}var ef=n(82348);let em=D,ep=F,eh=o.forwardRef(({className:e,children:t,...n},o)=>(0,r.jsx)(Q,{ref:o,className:(0,ef.QP)("list-none",e),...n,children:t}));eh.displayName=Q.displayName;let ev=o.forwardRef(({className:e,children:t,...n},o)=>(0,r.jsx)(U,{ref:o,className:(0,ef.QP)("data-[state=open]:bg-fd-accent/50",e),...n,children:t}));ev.displayName=U.displayName;let eg=o.forwardRef(({className:e,...t},n)=>(0,r.jsx)(J,{ref:n,className:(0,ef.QP)("absolute inset-x-0 top-0 data-[motion=from-end]:animate-fd-enterFromRight data-[motion=from-start]:animate-fd-enterFromLeft data-[motion=to-end]:animate-fd-exitToRight data-[motion=to-start]:animate-fd-exitToLeft",e),...t}));eg.displayName=J.displayName;let ex=G,eb=o.forwardRef(({className:e,...t},n)=>(0,r.jsx)("div",{ref:n,className:"flex w-full justify-center",children:(0,r.jsx)(ee,{...t,className:(0,ef.QP)("relative h-(--radix-navigation-menu-viewport-height) w-full origin-[top_center] overflow-hidden text-fd-popover-foreground transition-[width,height] duration-300 data-[state=closed]:animate-fd-nav-menu-out data-[state=open]:animate-fd-nav-menu-in",e)})}));eb.displayName=ee.displayName},99128:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(11310);function o(e,t,n,o){if(!e||o===n||null==o||!t)return;let a=(0,r.DT)(t),{name:i,...s}=e;s.path||(s.path=""!==a?a:"/");let l=`${i}=${o};`;for(let[e,t]of Object.entries(s))l+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(l+="="+t),l+=";";document.cookie=l}}};