"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[828],{469:(t,e,i)=>{i.d(e,{DT:()=>l,FP:()=>a,TK:()=>r,Zn:()=>s,aM:()=>o,x3:()=>u});var n=i(3225);function r(t){return"string"==typeof t?{pathname:t}:t}function s(t){let e=new URLSearchParams;for(let[i,n]of Object.entries(t))Array.isArray(n)?n.forEach(t=>{e.append(i,String(t))}):e.set(i,String(n));return"?"+e.toString()}function a({pathname:t,locale:e,params:i,pathnames:r,query:a}){function o(t){let e=r[t];return e||(e=t),e}function l(t,r){let o=(0,n.Wl)(t,e,r);return i&&Object.entries(i).forEach(([t,e])=>{let i,n;Array.isArray(e)?(i=`(\\[)?\\[...${t}\\](\\])?`,n=e.map(t=>String(t)).join("/")):(i=`\\[${t}\\]`,n=String(e)),o=o.replace(RegExp(i,"g"),n)}),o=o.replace(/\[\[\.\.\..+\]\]/g,""),o=(0,n.po)(o),a&&(o+=s(a)),o}if("string"==typeof t)return l(o(t),t);{let{pathname:e,...i}=t;return{...i,pathname:l(o(e),e)}}}function o(t,e,i){let r=(0,n.FD)(Object.keys(i)),s=decodeURI(e);for(let e of r){let r=i[e];if("string"==typeof r){if((0,n.ql)(r,s))return e}else if((0,n.ql)((0,n.Wl)(r,t,e),s))return e}return e}function l(t,e=window.location.pathname){return"/"===t?e:e.replace(t,"")}function u(t,e,i,r){let s,{mode:a}=i.localePrefix;return void 0!==r?s=r:(0,n._x)(t)&&("always"===a?s=!0:"as-needed"===a&&(s=i.domains?!i.domains.some(t=>t.defaultLocale===e):e!==i.defaultLocale)),s?(0,n.PJ)((0,n.XP)(e,i.localePrefix),t):t}},981:(t,e,i)=>{i.d(e,{A:()=>p});var n=i(5695),r=i(2115),s=i.t(r,2),a=i(6453),o=s["use".trim()],l=i(3225),u=i(6160),h=i(469),c=i(5155),d=i(8986);function p(t){let{Link:e,config:i,getPathname:s,...p}=function(t,e){var i,s,a;let d={...i=e||{},localePrefix:"object"==typeof(a=i.localePrefix)?a:{mode:a||"always"},localeCookie:!!((s=i.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof s&&s},localeDetection:i.localeDetection??!0,alternateLinks:i.alternateLinks??!0},p=d.pathnames,m=(0,r.forwardRef)(function({href:e,locale:i,...n},r){let s,a;"object"==typeof e?(s=e.pathname,a=e.params):s=e;let h=(0,l._x)(e),m=t(),g=(0,l.yL)(m)?o(m):m,y=h?f({locale:i||g,href:null==p?s:{pathname:s,params:a}},null!=i||void 0):s;return(0,c.jsx)(u.default,{ref:r,href:"object"==typeof e?{...e,pathname:y}:y,locale:i,localeCookie:d.localeCookie,...n})});function f(t,e){let i,{href:n,locale:r}=t;return null==p?"object"==typeof n?(i=n.pathname,n.query&&(i+=(0,h.Zn)(n.query))):i=n:i=(0,h.FP)({locale:r,...(0,h.TK)(n),pathnames:d.pathnames}),(0,h.x3)(i,r,d,e)}function g(t){return function(e,...i){return t(f(e),...i)}}return{config:d,Link:m,redirect:g(n.redirect),permanentRedirect:g(n.permanentRedirect),getPathname:f}}(a.Ym,t);return{...p,Link:e,usePathname:function(){let t=function(t){let e=(0,n.usePathname)(),i=(0,a.Ym)();return(0,r.useMemo)(()=>{if(!e)return e;let n=e,r=(0,l.XP)(i,t.localePrefix);if((0,l.wO)(r,e))n=(0,l.MY)(e,r);else if("as-needed"===t.localePrefix.mode&&t.localePrefix.prefixes){let t=(0,l.bL)(i);(0,l.wO)(t,e)&&(n=(0,l.MY)(e,t))}return n},[t.localePrefix,i,e])}(i),e=(0,a.Ym)();return(0,r.useMemo)(()=>t&&i.pathnames?(0,h.aM)(e,t,i.pathnames):t,[e,t])},useRouter:function(){let t=(0,n.useRouter)(),e=(0,a.Ym)(),o=(0,n.usePathname)();return(0,r.useMemo)(()=>{function n(t){return function(n,r){let{locale:a,...l}=r||{},u=[s({href:n,locale:a||e})];Object.keys(l).length>0&&u.push(l),t(...u),(0,d.A)(i.localeCookie,o,e,a)}}return{...t,push:n(t.push),replace:n(t.replace),prefetch:n(t.prefetch)}},[e,o,t])},getPathname:s}}},1362:(t,e,i)=>{i.d(e,{D:()=>u,N:()=>h});var n=i(2115),r=(t,e,i,n,r,s,a,o)=>{let l=document.documentElement,u=["light","dark"];function h(e){var i;(Array.isArray(t)?t:[t]).forEach(t=>{let i="class"===t,n=i&&s?r.map(t=>s[t]||t):r;i?(l.classList.remove(...n),l.classList.add(s&&s[e]?s[e]:e)):l.setAttribute(t,e)}),i=e,o&&u.includes(i)&&(l.style.colorScheme=i)}if(n)h(n);else try{let t=localStorage.getItem(e)||i,n=a&&"system"===t?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":t;h(n)}catch(t){}},s=["light","dark"],a="(prefers-color-scheme: dark)",o=n.createContext(void 0),l={setTheme:t=>{},themes:[]},u=()=>{var t;return null!=(t=n.useContext(o))?t:l},h=t=>n.useContext(o)?n.createElement(n.Fragment,null,t.children):n.createElement(d,{...t}),c=["light","dark"],d=t=>{let{forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:r=!0,enableColorScheme:l=!0,storageKey:u="theme",themes:h=c,defaultTheme:d=r?"system":"light",attribute:y="data-theme",value:v,children:x,nonce:w,scriptProps:b}=t,[P,T]=n.useState(()=>m(u,d)),[S,M]=n.useState(()=>"system"===P?g():P),A=v?Object.values(v):h,E=n.useCallback(t=>{let e=t;if(!e)return;"system"===t&&r&&(e=g());let n=v?v[e]:e,a=i?f(w):null,o=document.documentElement,u=t=>{"class"===t?(o.classList.remove(...A),n&&o.classList.add(n)):t.startsWith("data-")&&(n?o.setAttribute(t,n):o.removeAttribute(t))};if(Array.isArray(y)?y.forEach(u):u(y),l){let t=s.includes(d)?d:null,i=s.includes(e)?e:t;o.style.colorScheme=i}null==a||a()},[w]),C=n.useCallback(t=>{let e="function"==typeof t?t(P):t;T(e);try{localStorage.setItem(u,e)}catch(t){}},[P]),k=n.useCallback(t=>{M(g(t)),"system"===P&&r&&!e&&E("system")},[P,e]);n.useEffect(()=>{let t=window.matchMedia(a);return t.addListener(k),k(t),()=>t.removeListener(k)},[k]),n.useEffect(()=>{let t=t=>{t.key===u&&(t.newValue?T(t.newValue):C(d))};return window.addEventListener("storage",t),()=>window.removeEventListener("storage",t)},[C]),n.useEffect(()=>{E(null!=e?e:P)},[e,P]);let V=n.useMemo(()=>({theme:P,setTheme:C,forcedTheme:e,resolvedTheme:"system"===P?S:P,themes:r?[...h,"system"]:h,systemTheme:r?S:void 0}),[P,C,e,S,r,h]);return n.createElement(o.Provider,{value:V},n.createElement(p,{forcedTheme:e,storageKey:u,attribute:y,enableSystem:r,enableColorScheme:l,defaultTheme:d,value:v,themes:h,nonce:w,scriptProps:b}),x)},p=n.memo(t=>{let{forcedTheme:e,storageKey:i,attribute:s,enableSystem:a,enableColorScheme:o,defaultTheme:l,value:u,themes:h,nonce:c,scriptProps:d}=t,p=JSON.stringify([s,i,l,e,h,u,a,o]).slice(1,-1);return n.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(r.toString(),")(").concat(p,")")}})}),m=(t,e)=>{let i;try{i=localStorage.getItem(t)||void 0}catch(t){}return i||e},f=t=>{let e=document.createElement("style");return t&&e.setAttribute("nonce",t),e.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},g=t=>(t||(t=window.matchMedia(a)),t.matches?"dark":"light")},2085:(t,e,i)=>{i.d(e,{F:()=>a});var n=i(2596);let r=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,s=n.$,a=(t,e)=>i=>{var n;if((null==e?void 0:e.variants)==null)return s(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:a,defaultVariants:o}=e,l=Object.keys(a).map(t=>{let e=null==i?void 0:i[t],n=null==o?void 0:o[t];if(null===e)return null;let s=r(e)||r(n);return a[t][s]}),u=i&&Object.entries(i).reduce((t,e)=>{let[i,n]=e;return void 0===n||(t[i]=n),t},{});return s(t,l,null==e||null==(n=e.compoundVariants)?void 0:n.reduce((t,e)=>{let{class:i,className:n,...r}=e;return Object.entries(r).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...o,...u}[e]):({...o,...u})[e]===i})?[...t,i,n]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},2596:(t,e,i)=>{i.d(e,{$:()=>n});function n(){for(var t,e,i=0,n="",r=arguments.length;i<r;i++)(t=arguments[i])&&(e=function t(e){var i,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(n=t(e[i]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}(t))&&(n&&(n+=" "),n+=e);return n}},3225:(t,e,i)=>{function n(t){return("object"==typeof t?null==t.host&&null==t.hostname:!/^[a-z]+:/i.test(t))&&!function(t){let e="object"==typeof t?t.pathname:t;return null!=e&&!e.startsWith("/")}(t)}function r(t,e){return t.replace(RegExp(`^${e}`),"")||"/"}function s(t,e){let i=t;return/^\/(\?.*)?$/.test(e)&&(e=e.slice(1)),i+=e}function a(t,e){return e===t||e.startsWith(`${t}/`)}function o(t,e,i){return"string"==typeof t?t:t[e]||i}function l(t){let e=function(){try{return!0}catch{return!1}}();if("/"!==t){let i=t.endsWith("/");e&&!i?t+="/":!e&&i&&(t=t.slice(0,-1))}return t}function u(t,e){let i=l(t),n=l(e);return(function(t){let e=t.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${e}$`)})(i).test(n)}function h(t,e){return"never"!==e.mode&&e.prefixes?.[t]||c(t)}function c(t){return"/"+t}function d(t){return t.includes("[[...")}function p(t){return t.includes("[...")}function m(t){return t.includes("[")}function f(t,e){let i=t.split("/"),n=e.split("/"),r=Math.max(i.length,n.length);for(let t=0;t<r;t++){let e=i[t],r=n[t];if(!e&&r)return -1;if(e&&!r)return 1;if(e||r){if(!m(e)&&m(r))return -1;if(m(e)&&!m(r))return 1;if(!p(e)&&p(r))return -1;if(p(e)&&!p(r))return 1;if(!d(e)&&d(r))return -1;if(d(e)&&!d(r))return 1}}return 0}function g(t){return t.sort(f)}function y(t){return"function"==typeof t.then}i.d(e,{FD:()=>g,MY:()=>r,PJ:()=>s,Wl:()=>o,XP:()=>h,_x:()=>n,bL:()=>c,po:()=>l,ql:()=>u,wO:()=>a,yL:()=>y})},3237:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function a(t,e,i,n){if("function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}return e}function o(t,e,i){let n=t.getProps();return a(n,e,void 0!==i?i:n.custom,t)}i.d(e,{P:()=>sM});let l=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],u=new Set(l),h=new Set(["width","height","top","left","right","bottom",...l]),c=t=>Array.isArray(t),d=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),p=t=>c(t)?t[t.length-1]||0:t,m=["read","resolveKeyframes","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null},g={skipAnimations:!1,useManualTiming:!1};function y(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=m.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(h.schedule(e),t()),l++,e(o)}let h={schedule:(t,e=!1,s=!1)=>{let o=s&&r?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(o=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&f.value&&f.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{read:o,resolveKeyframes:l,update:u,preRender:h,render:c,postRender:d}=a,p=()=>{let s=g.useManualTiming?r.timestamp:performance.now();i=!1,g.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,o.process(r),l.process(r),u.process(r),h.process(r),c.process(r),d.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(p))},y=()=>{i=!0,n=!0,r.isProcessing||t(p)};return{schedule:m.reduce((t,e)=>{let n=a[e];return t[e]=(t,e=!1,r=!1)=>(i||y(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<m.length;e++)a[m[e]].cancel(t)},state:r,steps:a}}let v=t=>t,{schedule:x,cancel:w,state:b,steps:P}=y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:v,!0);function T(){n=void 0}let S={now:()=>(void 0===n&&S.set(b.isProcessing||g.useManualTiming?b.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(T)}};function M(t,e){-1===t.indexOf(e)&&t.push(e)}function A(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class E{constructor(){this.subscriptions=[]}add(t){return M(this.subscriptions,t),()=>A(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let C=t=>!isNaN(parseFloat(t)),k={current:void 0};class V{constructor(t,e={}){this.version="12.7.4",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=S.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=S.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=C(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new E);let i=this.events[t].add(e);return"change"===t?()=>{i(),x.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return k.current&&k.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=S.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function D(t,e){return new V(t,e)}let R=t=>!!(t&&t.getVelocity);function L(t,e){let i=t.getValue("willChange");if(R(i)&&i.add)return i.add(e);if(!i&&g.WillChange){let i=new g.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),F="data-"+j("framerAppearId"),B={current:!1},O=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function I(t,e,i,n){if(t===e&&i===n)return v;let r=e=>(function(t,e,i,n,r){let s,a,o=0;do(s=O(a=e+(i-e)/2,n,r)-t)>0?i=a:e=a;while(Math.abs(s)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:O(r(t),e,n)}let U=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,N=t=>e=>1-t(1-e),$=I(.33,1.53,.69,.99),W=N($),z=U(W),Y=t=>(t*=2)<1?.5*W(t):.5*(2-Math.pow(2,-10*(t-1))),X=t=>1-Math.sin(Math.acos(t)),H=N(X),q=U(X),K=t=>/^0[^.\s]+$/u.test(t),_=(t,e,i)=>i>e?e:i<t?t:i,Z={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},G={...Z,transform:t=>_(0,1,t)},J={...Z,default:1},Q=t=>Math.round(1e5*t)/1e5,tt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,te=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ti=(t,e)=>i=>!!("string"==typeof i&&te.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tn=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,a,o]=n.match(tt);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tr=t=>_(0,255,t),ts={...Z,transform:t=>Math.round(tr(t))},ta={test:ti("rgb","red"),parse:tn("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+ts.transform(t)+", "+ts.transform(e)+", "+ts.transform(i)+", "+Q(G.transform(n))+")"},to={test:ti("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:ta.transform},tl=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tu=tl("deg"),th=tl("%"),tc=tl("px"),td=tl("vh"),tp=tl("vw"),tm={...th,parse:t=>th.parse(t)/100,transform:t=>th.transform(100*t)},tf={test:ti("hsl","hue"),parse:tn("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+th.transform(Q(e))+", "+th.transform(Q(i))+", "+Q(G.transform(n))+")"},tg={test:t=>ta.test(t)||to.test(t)||tf.test(t),parse:t=>ta.test(t)?ta.parse(t):tf.test(t)?tf.parse(t):to.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ta.transform(t):tf.transform(t)},ty=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tv="number",tx="color",tw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,a=e.replace(tw,t=>(tg.test(t)?(n.color.push(s),r.push(tx),i.push(tg.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tv),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:a,indexes:n,types:r}}function tP(t){return tb(t).values}function tT(t){let{split:e,types:i}=tb(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tv?r+=Q(t[s]):e===tx?r+=tg.transform(t[s]):r+=t[s]}return r}}let tS=t=>"number"==typeof t?0:t,tM={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tt)?.length||0)+(t.match(ty)?.length||0)>0},parse:tP,createTransformer:tT,getAnimatableNone:function(t){let e=tP(t);return tT(t)(e.map(tS))}},tA=new Set(["brightness","contrast","saturate","opacity"]);function tE(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tt)||[];if(!n)return t;let r=i.replace(n,""),s=+!!tA.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let tC=/\b([a-z-]*)\(.*?\)/gu,tk={...tM,getAnimatableNone:t=>{let e=t.match(tC);return e?e.map(tE).join(" "):t}},tV={...Z,transform:Math.round},tD={borderWidth:tc,borderTopWidth:tc,borderRightWidth:tc,borderBottomWidth:tc,borderLeftWidth:tc,borderRadius:tc,radius:tc,borderTopLeftRadius:tc,borderTopRightRadius:tc,borderBottomRightRadius:tc,borderBottomLeftRadius:tc,width:tc,maxWidth:tc,height:tc,maxHeight:tc,top:tc,right:tc,bottom:tc,left:tc,padding:tc,paddingTop:tc,paddingRight:tc,paddingBottom:tc,paddingLeft:tc,margin:tc,marginTop:tc,marginRight:tc,marginBottom:tc,marginLeft:tc,backgroundPositionX:tc,backgroundPositionY:tc,rotate:tu,rotateX:tu,rotateY:tu,rotateZ:tu,scale:J,scaleX:J,scaleY:J,scaleZ:J,skew:tu,skewX:tu,skewY:tu,distance:tc,translateX:tc,translateY:tc,translateZ:tc,x:tc,y:tc,z:tc,perspective:tc,transformPerspective:tc,opacity:G,originX:tm,originY:tm,originZ:tc,zIndex:tV,size:tc,fillOpacity:G,strokeOpacity:G,numOctaves:tV},tR={...tD,color:tg,backgroundColor:tg,outlineColor:tg,fill:tg,stroke:tg,borderColor:tg,borderTopColor:tg,borderRightColor:tg,borderBottomColor:tg,borderLeftColor:tg,filter:tk,WebkitFilter:tk},tL=t=>tR[t];function tj(t,e){let i=tL(t);return i!==tk&&(i=tM),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tF=new Set(["auto","none","0"]),tB=t=>180*t/Math.PI,tO=t=>tU(tB(Math.atan2(t[1],t[0]))),tI={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tO,rotateZ:tO,skewX:t=>tB(Math.atan(t[1])),skewY:t=>tB(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},tU=t=>((t%=360)<0&&(t+=360),t),tN=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),t$=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tW={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tN,scaleY:t$,scale:t=>(tN(t)+t$(t))/2,rotateX:t=>tU(tB(Math.atan2(t[6],t[5]))),rotateY:t=>tU(tB(Math.atan2(-t[2],t[0]))),rotateZ:tO,rotate:tO,skewX:t=>tB(Math.atan(t[4])),skewY:t=>tB(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tz(t){return+!!t.includes("scale")}function tY(t,e){let i,n;if(!t||"none"===t)return tz(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=tW,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tI,n=e}if(!n)return tz(e);let s=i[e],a=n[1].split(",").map(tH);return"function"==typeof s?s(a):a[s]}let tX=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return tY(i,e)};function tH(t){return parseFloat(t.trim())}let tq=t=>t===Z||t===tc,tK=new Set(["x","y","z"]),t_=l.filter(t=>!tK.has(t)),tZ={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>tY(e,"x"),y:(t,{transform:e})=>tY(e,"y")};tZ.translateX=tZ.x,tZ.translateY=tZ.y;let tG=new Set,tJ=!1,tQ=!1;function t0(){if(tQ){let t=Array.from(tG).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return t_.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tQ=!1,tJ=!1,tG.forEach(t=>t.complete()),tG.clear()}function t1(){tG.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tQ=!0)})}class t2{constructor(t,e,i,n,r,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tG.add(this),tJ||(tJ=!0,x.read(t1),x.resolveKeyframes(t0))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;for(let r=0;r<t.length;r++)if(null===t[r])if(0===r){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}else t[r]=t[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tG.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tG.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let t5=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),t3=t=>e=>"string"==typeof e&&e.startsWith(t),t9=t3("--"),t6=t3("var(--"),t4=t=>!!t6(t)&&t8.test(t.split("/*")[0].trim()),t8=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,t7=()=>{},et=()=>{},ee=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ei=t=>e=>e.test(t),en=[Z,tc,th,tu,tp,td,{test:t=>"auto"===t,parse:t=>t}],er=t=>en.find(ei(t));class es extends t2{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&t4(n=n.trim())){let r=function t(e,i,n=1){et(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=ee.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let t=a.trim();return t5(t)?parseFloat(t):t}return t4(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!h.has(i)||2!==t.length)return;let[n,r]=t,s=er(n),a=er(r);if(s!==a)if(tq(s)&&tq(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||K(n))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!tF.has(e)&&tb(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=tj(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tZ[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=tZ[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let ea=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tM.test(t)||"0"===t)&&!t.startsWith("url("));function eo(t){return"function"==typeof t&&"applyToOptions"in t}let el=t=>null!==t;function eu(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(el),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return s&&void 0!==n?n:r[s]}class eh{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=S.now(),this.options={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(t1(),t0()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=S.now(),this.hasAttemptedResolve=!0;let{name:i,type:n,velocity:r,delay:s,onComplete:a,onUpdate:o,isGenerator:l}=this.options;if(!l&&!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],a=ea(r,e),o=ea(s,e);return t7(a===o,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eo(i))&&n)}(t,i,n,r))if(B.current||!s){o&&o(eu(t,this.options,e)),a&&a(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let ec=(t,e,i)=>t+(e-t)*i;function ed(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ep(t,e){return i=>i>0?e:t}let em=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},ef=[to,ta,tf],eg=t=>ef.find(e=>e.test(t));function ey(t){let e=eg(t);if(t7(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tf&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;r=ed(o,n,t+1/3),s=ed(o,n,t),a=ed(o,n,t-1/3)}else r=s=a=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(i)),i}let ev=(t,e)=>{let i=ey(t),n=ey(e);if(!i||!n)return ep(t,e);let r={...i};return t=>(r.red=em(i.red,n.red,t),r.green=em(i.green,n.green,t),r.blue=em(i.blue,n.blue,t),r.alpha=ec(i.alpha,n.alpha,t),ta.transform(r))},ex=(t,e)=>i=>e(t(i)),ew=(...t)=>t.reduce(ex),eb=new Set(["none","hidden"]);function eP(t,e){return i=>ec(t,e,i)}function eT(t){return"number"==typeof t?eP:"string"==typeof t?t4(t)?ep:tg.test(t)?ev:eA:Array.isArray(t)?eS:"object"==typeof t?tg.test(t)?ev:eM:ep}function eS(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>eT(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function eM(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=eT(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let eA=(t,e)=>{let i=tM.createTransformer(e),n=tb(t),r=tb(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?eb.has(t)&&!r.values.length||eb.has(e)&&!n.values.length?function(t,e){return eb.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):ew(eS(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],a=t.indexes[s][n[s]],o=t.values[a]??0;i[r]=o,n[s]++}return i}(n,r),r.values),i):(t7(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),ep(t,e))};function eE(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?ec(t,e,i):eT(t)(t,e)}function eC(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let ek={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},eV=t=>1e3*t,eD=t=>t/1e3;function eR(t,e){return t*Math.sqrt(1-e*e)}function eL(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}let ej={};function eF(t){let e;return()=>(void 0===e&&(e=t()),e)}let eB=function(t,e){let i=eF(t);return()=>ej[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eO=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`},eI=["duration","bounce"],eU=["stiffness","damping","mass"];function eN(t,e){return e.some(e=>void 0!==t[e])}function e$(t=ek.visualDuration,e=ek.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:ek.velocity,stiffness:ek.stiffness,damping:ek.damping,mass:ek.mass,isResolvedFromDuration:!1,...t};if(!eN(t,eU)&&eN(t,eI))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*_(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:ek.mass,stiffness:n,damping:r}}else{let i=function({duration:t=ek.duration,bounce:e=ek.bounce,velocity:i=ek.velocity,mass:n=ek.mass}){let r,s;t7(t<=eV(ek.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=_(ek.minDamping,ek.maxDamping,a),t=_(ek.minDuration,ek.maxDuration,eD(t)),a<1?(r=e=>{let n=e*a,r=n*t;return .001-(n-i)/eR(e,a)*Math.exp(-r)},s=e=>{let n=e*a*t,s=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-n),l=eR(Math.pow(e,2),a);return(n*i+i-s)*o*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=eV(t),isNaN(o))return{stiffness:ek.stiffness,damping:ek.damping,duration:t};{let e=Math.pow(o,2)*n;return{stiffness:e,damping:2*a*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:ek.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-eD(n.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*c)),y=o-a,v=eD(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?ek.restSpeed.granular:ek.restSpeed.default),s||(s=x?ek.restDelta.granular:ek.restDelta.default),g<1){let t=eR(v,g);i=e=>o-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return o-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let n=0;g<1&&(n=0===t?eV(f):eC(i,t,e));let a=Math.abs(o-e)<=s;l.done=Math.abs(n)<=r&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(eL(w),2e4),e=eO(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function eW({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let c,d,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let w=t=>-y*Math.exp(-t/n),b=t=>x+w(t),P=t=>{let e=w(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},T=t=>{f(m.value)&&(c=t,d=e$({keyframes:[m.value,g(m.value)],velocity:eC(b,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>=c)?d.next(t-c):(e||P(t),m)}}}e$.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(eL(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:eD(r)}}(t,100,e$);return t.ease=eB()?e.ease:"easeOut",t.duration=eV(e.duration),t.type="keyframes",t};let ez=I(.42,0,1,1),eY=I(0,0,.58,1),eX=I(.42,0,.58,1),eH=t=>Array.isArray(t)&&"number"!=typeof t[0],eq=t=>Array.isArray(t)&&"number"==typeof t[0],eK={linear:v,easeIn:ez,easeInOut:eX,easeOut:eY,circIn:X,circInOut:q,circOut:H,backIn:W,backInOut:z,backOut:$,anticipate:Y},e_=t=>{if(eq(t)){et(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return I(e,i,n,r)}return"string"==typeof t?(et(void 0!==eK[t],`Invalid easing type '${t}'`),eK[t]):t},eZ=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eG({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=eH(n)?n.map(e_):e_(n),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(et(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],r=i||eE,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=ew(Array.isArray(e)?e[i]||v:e,s)),n.push(s)}return n}(e,n,r),l=o.length,u=i=>{if(a&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=eZ(t[n],t[n+1],i);return o[n](r)};return i?e=>u(_(t[0],t[s-1],e)):u}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=eZ(0,e,n);t.push(ec(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||eX).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let eJ=t=>{let e=({timestamp:e})=>t(e);return{start:()=>x.update(e,!0),stop:()=>w(e),now:()=>b.isProcessing?b.timestamp:S.now()}},eQ={layout:0,mainThread:0,waapi:0},e0={decay:eW,inertia:eW,tween:eG,keyframes:eG,spring:e$},e1=t=>t/100;class e2 extends eh{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:n,keyframes:r}=this.options,s=n?.KeyframeResolver||t2;this.resolver=new s(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:a,velocity:o=0}=this.options,l=eo(n)?n:e0[n]||eG;l!==eG&&"number"!=typeof t[0]&&(e=ew(e1,eE(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===a&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=eL(u));let{calculatedDuration:h}=u,c=h+s;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:c,totalDuration:c*(r+1)-s}}onPostResolved(){let{autoplay:t=!0}=this.options;eQ.mainThread++,this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:r,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return r.next(0);let{delay:c,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let g=this.currentTime-c*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,x=r;if(d){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=s)),v=_(0,1,i)*h}let w=y?{done:!1,value:o[0]}:x.next(v);a&&(w.value=a(w.value));let{done:b}=w;y||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return P&&void 0!==n&&(w.value=eu(o,this.options,n)),f&&f(w.value),P&&this.finish(),w}get duration(){let{resolved:t}=this;return t?eD(t.calculatedDuration):0}get time(){return eD(this.currentTime)}set time(t){t=eV(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=eD(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=eJ,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=i??this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=this.currentTime??0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),eQ.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}get finished(){return this.currentFinishedPromise}}let e5=new Set(["opacity","clipPath","filter","transform"]),e3=eF(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),e9=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,e6={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e9([0,.65,.55,1]),circOut:e9([.55,0,1,.45]),backIn:e9([.31,.01,.66,-.59]),backOut:e9([.33,1.53,.69,.99])};function e4(t,e){t.timeline=e,t.onfinish=null}let e8={anticipate:Y,backInOut:z,circInOut:q};class e7 extends eh{constructor(t){super(t);let{name:e,motionValue:i,element:n,keyframes:r}=this.options;this.resolver=new es(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:n=300,times:r,ease:s,type:a,motionValue:o,name:l,startTime:u}=this.options;if(!o.owner||!o.owner.current)return!1;if("string"==typeof s&&eB()&&s in e8&&(s=e8[s]),eo((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&eB()||!e||"string"==typeof e&&(e in e6||eB())||eq(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:o,element:l,...u}=this.options,h=function(t,e){let i=new e2({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},r=[],s=0;for(;!n.done&&s<2e4;)r.push((n=i.sample(s)).value),s+=10;return{times:void 0,keyframes:r,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),n=h.duration,r=h.times,s=h.ease,a="keyframes"}let h=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:a="loop",ease:o="easeInOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e&&eB()?eO(e,i):eq(e)?e9(e):Array.isArray(e)?e.map(e=>t(e,i)||e6.easeOut):e6[e]}(o,r);Array.isArray(c)&&(h.easing=c),f.value&&eQ.waapi++;let d=t.animate(h,{delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal",pseudoElement:void 0});return f.value&&d.finished.finally(()=>{eQ.waapi--}),d}(o.owner.current,l,t,{...this.options,duration:n,times:r,ease:s});return h.startTime=u??this.calcStartTime(),this.pendingTimeline?(e4(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;o.set(eu(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:n,times:r,type:a,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return eD(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return eD(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=eV(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}get finished(){return this.resolved.animation.finished}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return v;let{animation:i}=e;e4(i,t)}else this.pendingTimeline=t;return v}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:n,type:r,ease:s,times:a}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:o,element:l,...u}=this.options,h=new e2({...u,keyframes:i,duration:n,type:r,ease:s,times:a,isGenerator:!0}),c=eV(this.time);t.setWithVelocity(h.sample(c-10).value,h.sample(c).value,10)}let{onStop:o}=this.options;o&&o(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:a}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return e3()&&i&&e5.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==r&&0!==s&&"inertia"!==a}}let it={type:"spring",stiffness:500,damping:25,restSpeed:10},ie=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ii={type:"keyframes",duration:.8},ir={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},is=(t,{keyframes:e})=>e.length>2?ii:u.has(t)?t.startsWith("scale")?ie(e[1]):it:ir;function ia(t,e){return t?.[e]??t?.default??t}let io=eF(()=>void 0!==window.ScrollTimeline);class il{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>io()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class iu extends il{then(t,e){return this.finished.finally(t).then(()=>{})}}let ih=(t,e,i,n={},r,s)=>a=>{let o=ia(n,t)||{},l=o.delay||n.delay||0,{elapsed:u=0}=n;u-=eV(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-u,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&(h={...h,...is(t,h)}),h.duration&&(h.duration=eV(h.duration)),h.repeatDelay&&(h.repeatDelay=eV(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(c=!0)),(B.current||g.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,c&&!s&&void 0!==e.get()){let t=eu(h.keyframes,o);if(void 0!==t)return x.update(()=>{h.onUpdate(t),h.onComplete()}),new iu([])}return!s&&e7.supports(h)?new e7(h):new e2(h)};function ic(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(s=n);let u=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in l){let n=t.getValue(e,t.latestValues[e]??null),r=l[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let a={delay:i,...ia(s||{},e)},o=!1;if(window.MotionHandoffAnimation){let i=t.props[F];if(i){let t=window.MotionHandoffAnimation(i,e,x);null!==t&&(a.startTime=t,o=!0)}}L(t,e),n.start(ih(e,n,r,t.shouldReduceMotion&&h.has(e)?{type:!1}:a,t,o));let d=n.animation;d&&u.push(d)}return a&&Promise.all(u).then(()=>{x.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=o(t,e)||{};for(let e in r={...r,...i}){let i=p(r[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,D(i))}}(t,a)})}),u}function id(t,e,i={}){let n=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(ic(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=r;return function(t,e,i=0,n=0,r=1,s){let a=[],o=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(ip).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(id(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,s+n,a,o,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function ip(t,e){return t.sortNodePosition(e)}function im(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ig(t){return"string"==typeof t||Array.isArray(t)}let iy=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],iv=["initial",...iy],ix=iv.length,iw=[...iy].reverse(),ib=iy.length;function iP(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iT(){return{animate:iP(!0),whileInView:iP(),whileHover:iP(),whileTap:iP(),whileDrag:iP(),whileFocus:iP(),exit:iP()}}class iS{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iM extends iS{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>id(t,e,i)));else if("string"==typeof e)n=id(t,e,i);else{let r="function"==typeof e?o(t,e,i.custom):e;n=Promise.all(ic(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iT(),n=!0,s=e=>(i,n)=>{let r=o(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ix;t++){let n=iv[t],r=e.props[n];(ig(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],d=new Set,p={},m=1/0;for(let e=0;e<ib;e++){var f,g;let o=iw[e],y=i[o],v=void 0!==l[o]?l[o]:u[o],x=ig(v),w=o===a?y.isActive:null;!1===w&&(m=e);let b=v===u[o]&&v!==l[o]&&x;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),y.protectedKeys={...p},!y.isActive&&null===w||!v&&!y.prevProp||r(v)||"boolean"==typeof v)continue;let P=(f=y.prevProp,"string"==typeof(g=v)?g!==f:!!Array.isArray(g)&&!im(g,f)),T=P||o===a&&y.isActive&&!b&&x||e>m&&x,S=!1,M=Array.isArray(v)?v:[v],A=M.reduce(s(o),{});!1===w&&(A={});let{prevResolvedValues:E={}}=y,C={...E,...A},k=e=>{T=!0,d.has(e)&&(S=!0,d.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=A[t],i=E[t];if(p.hasOwnProperty(t))continue;let n=!1;(c(e)&&c(i)?im(e,i):e===i)?void 0!==e&&d.has(t)?k(t):y.protectedKeys[t]=!0:null!=e?k(t):d.add(t)}y.prevProp=v,y.prevResolvedValues=A,y.isActive&&(p={...p,...A}),n&&t.blockInitialAnimation&&(T=!1);let V=!(b&&P)||S;T&&V&&h.push(...M.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let y=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=a(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iT(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iA=0;class iE extends iS{constructor(){super(...arguments),this.id=iA++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function iC(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let ik=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iV(t){return{point:{x:t.pageX,y:t.pageY}}}let iD=t=>e=>ik(e)&&t(e,iV(e));function iR(t,e,i,n){return iC(t,e,iD(i),n)}function iL({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function ij(t){return t.max-t.min}function iF(t,e,i,n=.5){t.origin=n,t.originPoint=ec(e.min,e.max,t.origin),t.scale=ij(i)/ij(e),t.translate=ec(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iB(t,e,i,n){iF(t.x,e.x,i.x,n?n.originX:void 0),iF(t.y,e.y,i.y,n?n.originY:void 0)}function iO(t,e,i){t.min=i.min+e.min,t.max=t.min+ij(e)}function iI(t,e,i){t.min=e.min-i.min,t.max=t.min+ij(e)}function iU(t,e,i){iI(t.x,e.x,i.x),iI(t.y,e.y,i.y)}let iN=()=>({translate:0,scale:1,origin:0,originPoint:0}),i$=()=>({x:iN(),y:iN()}),iW=()=>({min:0,max:0}),iz=()=>({x:iW(),y:iW()});function iY(t){return[t("x"),t("y")]}function iX(t){return void 0===t||1===t}function iH({scale:t,scaleX:e,scaleY:i}){return!iX(t)||!iX(e)||!iX(i)}function iq(t){return iH(t)||iK(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iK(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i_(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iZ(t,e=0,i=1,n,r){t.min=i_(t.min,e,i,n,r),t.max=i_(t.max,e,i,n,r)}function iG(t,{x:e,y:i}){iZ(t.x,e.translate,e.scale,e.originPoint),iZ(t.y,i.translate,i.scale,i.originPoint)}function iJ(t,e){t.min=t.min+e,t.max=t.max+e}function iQ(t,e,i,n,r=.5){let s=ec(t.min,t.max,r);iZ(t,e,i,s,n)}function i0(t,e){iQ(t.x,e.x,e.scaleX,e.scale,e.originX),iQ(t.y,e.y,e.scaleY,e.scale,e.originY)}function i1(t,e){return iL(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let i2=({current:t})=>t?t.ownerDocument.defaultView:null;function i5(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let i3=(t,e)=>Math.abs(t-e);class i9{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i8(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i3(t.x,e.x)**2+i3(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=b;this.history.push({...n,timestamp:r});let{onStart:s,onMove:a}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=i6(e,this.transformPagePoint),x.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=i8("pointercancel"===t.type?this.lastMoveEventInfo:i6(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!ik(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=i6(iV(t),this.transformPagePoint),{point:a}=s,{timestamp:o}=b;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,i8(s,this.history)),this.removeListeners=ew(iR(this.contextWindow,"pointermove",this.handlePointerMove),iR(this.contextWindow,"pointerup",this.handlePointerUp),iR(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),w(this.updatePoint)}}function i6(t,e){return e?{point:e(t.point)}:t}function i4(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i8({point:t},e){return{point:t,delta:i4(t,i7(e)),offset:i4(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=i7(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>eV(.1)));)i--;if(!n)return{x:0,y:0};let s=eD(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let a={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function i7(t){return t[t.length-1]}function nt(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function ne(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function ni(t,e,i){return{min:nn(t,e),max:nn(t,i)}}function nn(t,e){return"number"==typeof t?t:t[e]||0}let nr={x:!1,y:!1},ns=new WeakMap;class na{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iz(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new i9(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iV(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nr[t])return null;else return nr[t]=!0,()=>{nr[t]=!1};return nr.x||nr.y?null:(nr.x=nr.y=!0,()=>{nr.x=nr.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iY(t=>{let e=this.getAxisMotionValue(t).get()||0;if(th.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=ij(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&x.postRender(()=>r(t,e)),L(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iY(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:i2(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&x.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!no(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?ec(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?ec(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&i5(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:nt(t.x,i,r),y:nt(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:ni(t,"left","right"),y:ni(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iY(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i5(e))return!1;let n=e.current;et(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i1(t,i),{scroll:r}=e;return r&&(iJ(n.x,r.offset.x),iJ(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),a=(t=r.layout.layoutBox,{x:ne(t.x,s.x),y:ne(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iL(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iY(a=>{if(!no(a,e,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return L(this.visualElement,t),i.start(ih(t,i,0,e,this.visualElement,!1))}stopAnimation(){iY(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iY(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iY(e=>{let{drag:i}=this.getProps();if(!no(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-ec(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i5(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iY(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=ij(t),r=ij(e);return r>n?i=eZ(e.min,e.max-n,t.min):n>r&&(i=eZ(t.min,t.max-r,e.min)),_(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iY(e=>{if(!no(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(ec(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;ns.set(this.visualElement,this);let t=iR(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i5(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),x.read(e);let r=iC(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iY(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:a}}}function no(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nl extends iS{constructor(t){super(t),this.removeGroupControls=v,this.removeListeners=v,this.controls=new na(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||v}unmount(){this.removeGroupControls(),this.removeListeners()}}let nu=t=>(e,i)=>{t&&x.postRender(()=>t(e,i))};class nh extends iS{constructor(){super(...arguments),this.removePointerDownListener=v}onPointerDown(t){this.session=new i9(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i2(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:nu(t),onStart:nu(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&x.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iR(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var nc,nd,np=i(5155),nm=i(2115);let nf=(0,nm.createContext)(null),ng=(0,nm.createContext)({}),ny=(0,nm.createContext)({}),nv={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nx(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nw={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tc.test(t))return t;else t=parseFloat(t);let i=nx(t,e.target.x),n=nx(t,e.target.y);return`${i}% ${n}%`}},nb={},{schedule:nP}=y(queueMicrotask,!1);class nT extends nm.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nM)nb[t]=nM[t],t9(t)&&(nb[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nv.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,s=i.projection;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||x.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),nP.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nS(t){let[e,i]=function(t=!0){let e=(0,nm.useContext)(nf);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=e,s=(0,nm.useId)();(0,nm.useEffect)(()=>{if(t)return r(s)},[t]);let a=(0,nm.useCallback)(()=>t&&n&&n(s),[s,n,t]);return!i&&n?[!1,a]:[!0]}(),n=(0,nm.useContext)(ng);return(0,np.jsx)(nT,{...t,layoutGroup:n,switchLayoutGroup:(0,nm.useContext)(ny),isPresent:e,safeToRemove:i})}let nM={borderRadius:{...nw,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nw,borderTopRightRadius:nw,borderBottomLeftRadius:nw,borderBottomRightRadius:nw,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tM.parse(t);if(n.length>5)return t;let r=tM.createTransformer(t),s=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+s]/=a,n[1+s]/=o;let l=ec(a,o,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}},nA=(t,e)=>t.depth-e.depth;class nE{constructor(){this.children=[],this.isDirty=!1}add(t){M(this.children,t),this.isDirty=!0}remove(t){A(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nA),this.isDirty=!1,this.children.forEach(t)}}function nC(t){let e=R(t)?t.get():t;return d(e)?e.toValue():e}let nk=["TopLeft","TopRight","BottomLeft","BottomRight"],nV=nk.length,nD=t=>"string"==typeof t?parseFloat(t):t,nR=t=>"number"==typeof t||tc.test(t);function nL(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nj=nB(0,.5,H),nF=nB(.5,.95,v);function nB(t,e,i){return n=>n<t?0:n>e?1:i(eZ(t,e,n))}function nO(t,e){t.min=e.min,t.max=e.max}function nI(t,e){nO(t.x,e.x),nO(t.y,e.y)}function nU(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nN(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function n$(t,e,[i,n,r],s,a){!function(t,e=0,i=1,n=.5,r,s=t,a=t){if(th.test(e)&&(e=parseFloat(e),e=ec(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=ec(s.min,s.max,n);t===s&&(o-=e),t.min=nN(t.min,e,i,o,r),t.max=nN(t.max,e,i,o,r)}(t,e[i],e[n],e[r],e.scale,s,a)}let nW=["x","scaleX","originX"],nz=["y","scaleY","originY"];function nY(t,e,i,n){n$(t.x,e,nW,i?i.x:void 0,n?n.x:void 0),n$(t.y,e,nz,i?i.y:void 0,n?n.y:void 0)}function nX(t){return 0===t.translate&&1===t.scale}function nH(t){return nX(t.x)&&nX(t.y)}function nq(t,e){return t.min===e.min&&t.max===e.max}function nK(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function n_(t,e){return nK(t.x,e.x)&&nK(t.y,e.y)}function nZ(t){return ij(t.x)/ij(t.y)}function nG(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nJ{constructor(){this.members=[]}add(t){M(this.members,t),t.scheduleRender()}remove(t){if(A(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nQ={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n0=["","X","Y","Z"],n1={visibility:"hidden"},n2=0;function n5(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n3({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=n2++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(nQ.nodes=nQ.calculatedTargetDeltas=nQ.calculatedProjections=0),this.nodes.forEach(n4),this.nodes.forEach(rr),this.nodes.forEach(rs),this.nodes.forEach(n8),f.addProjectionMetrics&&f.addProjectionMetrics(nQ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nE)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new E),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||n)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=S.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(w(n),t(s-e))};return x.read(n,!0),()=>w(n)}(n,250),nv.hasAnimatedSinceResize&&(nv.hasAnimatedSinceResize=!1,this.nodes.forEach(rn))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||rc,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!n_(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...ia(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||rn(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,w(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ra),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[F];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",x,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rt);return}this.isUpdating||this.nodes.forEach(re),this.isUpdating=!1,this.nodes.forEach(ri),this.nodes.forEach(n9),this.nodes.forEach(n6),this.clearAllSnapshots();let t=S.now();b.delta=_(0,1e3/60,t-b.timestamp),b.timestamp=t,b.isProcessing=!0,P.update.process(b),P.preRender.process(b),P.render.process(b),b.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nP.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n7),this.sharedNodes.forEach(ro)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,x.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){x.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ij(this.snapshot.measuredBox.x)||ij(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iz(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nH(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&(e||iq(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),rm((e=n).x),rm(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iz();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rg))){let{scroll:t}=this.root;t&&(iJ(e.x,t.offset.x),iJ(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iz();if(nI(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nI(e,t),iJ(e.x,r.offset.x),iJ(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iz();nI(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i0(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iq(n.latestValues)&&i0(i,n.latestValues)}return iq(this.latestValues)&&i0(i,this.latestValues),i}removeTransform(t){let e=iz();nI(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iq(i.latestValues))continue;iH(i.latestValues)&&i.updateSnapshot();let n=iz();nI(n,i.measurePageBox()),nY(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iq(this.latestValues)&&nY(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==b.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=b.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iz(),this.relativeTargetOrigin=iz(),iU(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iz(),this.targetWithTransforms=iz()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iO(s.x,a.x,o.x),iO(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nI(this.target,this.layout.layoutBox),iG(this.target,this.targetDelta)):nI(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iz(),this.relativeTargetOrigin=iz(),iU(this.relativeTargetOrigin,this.target,t.target),nI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&nQ.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iH(this.parent.latestValues)||iK(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===b.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nI(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let r,s,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){s=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i0(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iG(t,s)),n&&iq(r.latestValues)&&i0(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iz());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nU(this.prevProjectionDelta.x,this.projectionDelta.x),nU(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iB(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&nG(this.projectionDelta.x,this.prevProjectionDelta.x)&&nG(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),f.value&&nQ.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=i$(),this.projectionDelta=i$(),this.projectionDeltaWithTransform=i$()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},a=i$();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iz(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rh));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(rl(a.x,t.x,n),rl(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m,f,g;iU(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,ru(p.x,m.x,f.x,g),ru(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,d=i,nq(u.x,d.x)&&nq(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=iz()),nI(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=ec(0,i.opacity??1,nj(n)),t.opacityExit=ec(e.opacity??1,0,nF(n))):s&&(t.opacity=ec(e.opacity??1,i.opacity??1,n));for(let r=0;r<nV;r++){let s=`border${nk[r]}Radius`,a=nL(e,s),o=nL(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||nR(a)===nR(o)?(t[s]=Math.max(ec(nD(a),nD(o),n),0),(th.test(o)||th.test(a))&&(t[s]+="%")):t[s]=o)}(e.rotate||i.rotate)&&(t.rotate=ec(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(w(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=x.update(()=>{nv.hasAnimatedSinceResize=!0,eQ.layout++,this.currentAnimation=function(t,e,i){let n=R(0)?0:D(t);return n.start(ih("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{eQ.layout--},onComplete:()=>{eQ.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&rf(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iz();let e=ij(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ij(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nI(e,i),i0(e,r),iB(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nJ),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n5("z",t,n,this.animationValues);for(let e=0;e<n0.length;e++)n5(`rotate${n0[e]}`,t,n,this.animationValues),n5(`skew${n0[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return n1;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nC(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nC(t?.pointerEvents)||""),this.hasProjected&&!iq(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,a=i?.z||0;if((r||s||a)&&(n=`translate3d(${r}px, ${s}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,nb){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:a}=nb[t],o="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=n===this?nC(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rt),this.root.sharedNodes.clear()}}}function n9(t){t.updateLayout()}function n6(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iY(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=ij(n);n.min=i[t].min,n.max=n.min+r}):rf(r,e.layoutBox,i)&&iY(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],a=ij(i[n]);r.max=r.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=i$();iB(a,i,e.layoutBox);let o=i$();s?iB(o,t.applyTransform(n,!0),e.measuredBox):iB(o,i,e.layoutBox);let l=!nH(a),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let a=iz();iU(a,e.layoutBox,r.layoutBox);let o=iz();iU(o,i,s.layoutBox),n_(a,o)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n4(t){f.value&&nQ.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n8(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n7(t){t.clearSnapshot()}function rt(t){t.clearMeasurements()}function re(t){t.isLayoutDirty=!1}function ri(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function rn(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function rr(t){t.resolveTargetDelta()}function rs(t){t.calcProjection()}function ra(t){t.resetSkewAndRotation()}function ro(t){t.removeLeadSnapshot()}function rl(t,e,i){t.translate=ec(e.translate,0,i),t.scale=ec(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function ru(t,e,i,n){t.min=ec(e.min,i.min,n),t.max=ec(e.max,i.max,n)}function rh(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let rc={duration:.45,ease:[.4,0,.1,1]},rd=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rp=rd("applewebkit/")&&!rd("chrome/")?Math.round:v;function rm(t){t.min=rp(t.min),t.max=rp(t.max)}function rf(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nZ(e)-nZ(i)))}function rg(t){return t!==t.root&&t.scroll?.wasRoot}let ry=n3({attachResizeListener:(t,e)=>iC(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rv={current:void 0},rx=n3({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rv.current){let t=new ry({});t.mount(window),t.setOptions({layoutScroll:!0}),rv.current=t}return rv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function rw(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rb(t){return!("touch"===t.pointerType||nr.x||nr.y)}function rP(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&x.postRender(()=>r(e,iV(e)))}class rT extends iS{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=rw(t,i),a=t=>{if(!rb(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rb(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",a,r)}),s}(t,(t,e)=>(rP(this.node,e,"Start"),t=>rP(this.node,t,"End"))))}unmount(){}}class rS extends iS{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ew(iC(this.node.current,"focus",()=>this.onFocus()),iC(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rM=(t,e)=>!!e&&(t===e||rM(t,e.parentElement)),rA=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rE=new WeakSet;function rC(t){return e=>{"Enter"===e.key&&t(e)}}function rk(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rV=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rC(()=>{if(rE.has(i))return;rk(i,"down");let t=rC(()=>{rk(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rk(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rD(t){return ik(t)&&!(nr.x||nr.y)}function rR(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&x.postRender(()=>r(e,iV(e)))}class rL extends iS{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=rw(t,i),a=t=>{let n=t.currentTarget;if(!rD(t)||rE.has(n))return;rE.add(n);let s=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rD(t)&&rE.has(n)&&(rE.delete(n),"function"==typeof s&&s(t,{success:e}))},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||rM(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,r),t instanceof HTMLElement)&&(t.addEventListener("focus",t=>rV(t,r)),rA.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rR(this.node,e,"Start"),(t,{success:e})=>rR(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rj=new WeakMap,rF=new WeakMap,rB=t=>{let e=rj.get(t.target);e&&e(t)},rO=t=>{t.forEach(rB)},rI={some:0,all:1};class rU extends iS{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rI[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rF.has(i)||rF.set(i,{});let n=rF.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rO,{root:t,...e})),n[r]}(e);return rj.set(t,i),n.observe(t),()=>{rj.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rN=(0,nm.createContext)({strict:!1}),r$=(0,nm.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),rW=(0,nm.createContext)({});function rz(t){return r(t.animate)||iv.some(e=>ig(t[e]))}function rY(t){return!!(rz(t)||t.variants)}function rX(t){return Array.isArray(t)?t.join(" "):t}let rH="undefined"!=typeof window,rq={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rK={};for(let t in rq)rK[t]={isEnabled:e=>rq[t].some(t=>!!e[t])};let r_=Symbol.for("motionComponentSymbol"),rZ=rH?nm.useLayoutEffect:nm.useEffect;function rG(t,{layout:e,layoutId:i}){return u.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!nb[t]||"opacity"===t)}let rJ=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rQ={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},r0=l.length;function r1(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(u.has(t)){a=!0;continue}if(t9(t)){r[t]=i;continue}{let e=rJ(i,tD[t]);t.startsWith("origin")?(o=!0,s[t]=e):n[t]=e}}if(!e.transform&&(a||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<r0;s++){let a=l[s],o=t[a];if(void 0===o)continue;let u=!0;if(!(u="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=rJ(o,tD[a]);if(!u){r=!1;let e=rQ[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let r2=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function r5(t,e,i){for(let n in e)R(e[n])||rG(n,i)||(t[n]=e[n])}let r3=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r9(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||r3.has(t)}let r6=t=>!r9(t);try{!function(t){t&&(r6=e=>e.startsWith("on")?!r9(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r4=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r8(t){if("string"!=typeof t||t.includes("-"));else if(r4.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let r7={offset:"stroke-dashoffset",array:"stroke-dasharray"},st={offset:"strokeDashoffset",array:"strokeDasharray"};function se(t,e,i){return"string"==typeof t?t:tc.transform(e+i*t)}function si(t,{attrX:e,attrY:i,attrScale:n,originX:r,originY:s,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...u},h,c){if(r1(t,u,c),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==r||void 0!==s||p.transform)&&(p.transformOrigin=function(t,e,i){let n=se(e,t.x,t.width),r=se(i,t.y,t.height);return`${n} ${r}`}(m,void 0!==r?r:.5,void 0!==s?s:.5)),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==a&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?r7:st;t[s.offset]=tc.transform(-n);let a=tc.transform(e),o=tc.transform(i);t[s.array]=`${a} ${o}`}(d,a,o,l,!1)}let sn=()=>({...r2(),attrs:{}}),sr=t=>"string"==typeof t&&"svg"===t.toLowerCase(),ss=t=>(e,i)=>{let n=(0,nm.useContext)(rW),s=(0,nm.useContext)(nf),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,s,o){let l={latestValues:function(t,e,i,n){let s={},o=n(t,{});for(let t in o)s[t]=nC(o[t]);let{initial:l,animate:u}=t,h=rz(t),c=rY(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=a(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(n,s,o,t),renderState:e()};return i&&(l.onMount=t=>i({props:n,current:t,...l}),l.onUpdate=t=>i(t)),l})(t,e,n,s);return i?o():function(t){let e=(0,nm.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)};function sa(t,e,i){let{style:n}=t,r={};for(let s in n)(R(n[s])||e.style&&R(e.style[s])||rG(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let so={useVisualState:ss({scrapeMotionValuesFromProps:sa,createRenderState:r2})};function sl(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}function su(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}let sh=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function sc(t,e,i,n){for(let i in su(t,e,void 0,n),e.attrs)t.setAttribute(sh.has(i)?i:j(i),e.attrs[i])}function sd(t,e,i){let n=sa(t,e,i);for(let i in t)(R(t[i])||R(e[i]))&&(n[-1!==l.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let sp=["x","y","width","height","cx","cy","r"],sm={useVisualState:ss({scrapeMotionValuesFromProps:sd,createRenderState:sn,onUpdate:({props:t,prevProps:e,current:i,renderState:n,latestValues:r})=>{if(!i)return;let s=!!t.drag;if(!s){for(let t in r)if(u.has(t)){s=!0;break}}if(!s)return;let a=!e;if(e)for(let i=0;i<sp.length;i++){let n=sp[i];t[n]!==e[n]&&(a=!0)}a&&x.read(()=>{sl(i,n),x.render(()=>{si(n,r,sr(i.tagName),t.transformTemplate),sc(i,n)})})}})},sf={current:null},sg={current:!1},sy=[...en,tg,tM],sv=t=>sy.find(ei(t)),sx=new WeakMap,sw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sb{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=t2,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=S.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,x.render(this.render,!1,!0))};let{latestValues:o,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rz(e),this.isVariantNode=rY(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==o[t]&&R(e)&&e.set(o[t],!1)}}mount(t){this.current=t,sx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sg.current||function(){if(sg.current=!0,rH)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sf.current=t.matches;t.addListener(e),e()}else sf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),w(this.notifyUpdate),w(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=u.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&x.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rK){let e=rK[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iz()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sw.length;e++){let i=sw[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(R(r))t.addValue(n,r);else if(R(s))t.addValue(n,D(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,D(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=D(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(t5(i)||K(i))?i=parseFloat(i):!sv(i)&&tM.test(e)&&(i=tj(t,e)),this.setBaseTarget(t,R(i)?i.get():i)),R(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=a(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||R(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new E),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sP extends sb{constructor(){super(...arguments),this.KeyframeResolver=es}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;R(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class sT extends sP{constructor(){super(...arguments),this.type="html",this.renderInstance=su}readValueFromInstance(t,e){if(u.has(e))return tX(t,e);{let i=window.getComputedStyle(t),n=(t9(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i1(t,e)}build(t,e,i){r1(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return sa(t,e,i)}}class sS extends sP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iz,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&sl(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(u.has(e)){let t=tL(e);return t&&t.default||0}return e=sh.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return sd(t,e,i)}onBindTransform(){this.current&&!this.renderState.dimensions&&x.postRender(this.updateDimensions)}build(t,e,i){si(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){sc(t,e,i,n)}mount(t){this.isSVGTag=sr(t.tagName),super.mount(t)}}let sM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((nc={animation:{Feature:iM},exit:{Feature:iE},inView:{Feature:rU},tap:{Feature:rL},focus:{Feature:rS},hover:{Feature:rT},pan:{Feature:nh},drag:{Feature:nl,ProjectionNode:rx,MeasureLayout:nS},layout:{ProjectionNode:rx,MeasureLayout:nS}},nd=(t,e)=>r8(t)?new sS(e):new sT(e,{allowProjection:t!==nm.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:a,Component:o}=t;function l(t,e){var i,n,l;let u,h={...(0,nm.useContext)(r$),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nm.useContext)(ng).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(rz(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ig(e)?e:void 0,animate:ig(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nm.useContext)(rW));return(0,nm.useMemo)(()=>({initial:e,animate:i}),[rX(e),rX(i)])}(t),p=a(t,c);if(!c&&rH){n=0,l=0,(0,nm.useContext)(rN).strict;let t=function(t){let{drag:e,layout:i}=rK;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nm.useContext)(rW),a=(0,nm.useContext)(rN),o=(0,nm.useContext)(nf),l=(0,nm.useContext)(r$).reducedMotion,u=(0,nm.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let h=u.current,c=(0,nm.useContext)(ny);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!a||o&&i5(o),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let d=(0,nm.useRef)(!1);(0,nm.useInsertionEffect)(()=>{h&&d.current&&h.update(i,o)});let p=i[F],m=(0,nm.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return rZ(()=>{h&&(d.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),nP.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,nm.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(o,p,h,r,t.ProjectionNode)}return(0,np.jsxs)(rW.Provider,{value:d,children:[u&&d.visualElement?(0,np.jsx)(u,{visualElement:d.visualElement,...h}):null,s(o,t,(i=d.visualElement,(0,nm.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):i5(e)&&(e.current=t))},[i])),p,c,d.visualElement)]})}n&&function(t){for(let e in t)rK[e]={...rK[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let u=(0,nm.forwardRef)(l);return u[r_]=o,u}({...r8(t)?sm:so,preloadedFeatures:nc,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let a=(r8(e)?function(t,e,i,n){let r=(0,nm.useMemo)(()=>{let i=sn();return si(i,e,sr(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};r5(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return r5(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nm.useMemo)(()=>{let i=r2();return r1(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),o=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r6(r)||!0===i&&r9(r)||!e&&!r9(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nm.Fragment?{...o,...a,ref:n}:{},{children:u}=i,h=(0,nm.useMemo)(()=>R(u)?u.get():u,[u]);return(0,nm.createElement)(e,{...l,children:h})}}(e),createVisualElement:nd,Component:t})}))},3786:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5585:(t,e,i)=>{i.d(e,{A:()=>r});var n={};!function t(e,i,n,r){var s,a,o,l,u,h,c,d,p,m,f,g=!!(e.Worker&&e.Blob&&e.Promise&&e.OffscreenCanvas&&e.OffscreenCanvasRenderingContext2D&&e.HTMLCanvasElement&&e.HTMLCanvasElement.prototype.transferControlToOffscreen&&e.URL&&e.URL.createObjectURL),y="function"==typeof Path2D&&"function"==typeof DOMMatrix;function v(){}function x(t){var n=i.exports.Promise,r=void 0!==n?n:e.Promise;return"function"==typeof r?new r(t):(t(v,v),null)}var w=(s=function(){if(!e.OffscreenCanvas)return!1;var t=new OffscreenCanvas(1,1),i=t.getContext("2d");i.fillRect(0,0,1,1);var n=t.transferToImageBitmap();try{i.createPattern(n,"no-repeat")}catch(t){return!1}return!0}(),a=new Map,{transform:function(t){if(s)return t;if(a.has(t))return a.get(t);var e=new OffscreenCanvas(t.width,t.height);return e.getContext("2d").drawImage(t,0,0),a.set(t,e),e},clear:function(){a.clear()}}),b=(u=Math.floor(1e3/60),h={},c=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(o=function(t){var e=Math.random();return h[e]=requestAnimationFrame(function i(n){c===n||c+u-1<n?(c=n,delete h[e],t()):h[e]=requestAnimationFrame(i)}),e},l=function(t){h[t]&&cancelAnimationFrame(h[t])}):(o=function(t){return setTimeout(t,u)},l=function(t){return clearTimeout(t)}),{frame:o,cancel:l}),P=(m={},function(){if(d)return d;if(!n&&g){var e=["var CONFETTI, SIZE = {}, module = {};","("+t.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{d=new Worker(URL.createObjectURL(new Blob([e])))}catch(t){return"function"==typeof console.warn&&console.warn("\uD83C\uDF8A Could not load worker",t),null}var i=d;function r(t,e){i.postMessage({options:t||{},callback:e})}i.init=function(t){var e=t.transferControlToOffscreen();i.postMessage({canvas:e},[e])},i.fire=function(t,e,n){if(p)return r(t,null),p;var s=Math.random().toString(36).slice(2);return p=x(function(e){function a(t){t.data.callback===s&&(delete m[s],i.removeEventListener("message",a),p=null,w.clear(),n(),e())}i.addEventListener("message",a),r(t,s),m[s]=a.bind(null,{data:{callback:s}})})},i.reset=function(){for(var t in i.postMessage({reset:!0}),m)m[t](),delete m[t]}}return d}),T={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function S(t,e,i){var n,r;return r=t&&null!=t[e]?t[e]:T[e],i?i(r):r}function M(t){return t<0?0:Math.floor(t)}function A(t){return parseInt(t,16)}function E(t){return t.map(C)}function C(t){var e=String(t).replace(/[^0-9a-f]/gi,"");return e.length<6&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),{r:A(e.substring(0,2)),g:A(e.substring(2,4)),b:A(e.substring(4,6))}}function k(t){t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight}function V(t){var e=t.getBoundingClientRect();t.width=e.width,t.height=e.height}function D(t,i){var s,a=!t,o=!!S(i||{},"resize"),l=!1,u=S(i,"disableForReducedMotion",Boolean),h=g&&S(i||{},"useWorker")?P():null,c=a?k:V,d=!!t&&!!h&&!!t.__confetti_initialized,p="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function m(i){var m,f=u||S(i,"disableForReducedMotion",Boolean),g=S(i,"zIndex",Number);if(f&&p)return x(function(t){t()});a&&s?t=s.canvas:a&&!t&&((m=document.createElement("canvas")).style.position="fixed",m.style.top="0px",m.style.left="0px",m.style.pointerEvents="none",m.style.zIndex=g,t=m,document.body.appendChild(t)),o&&!d&&c(t);var v={width:t.width,height:t.height};function P(){if(h){var e={getBoundingClientRect:function(){if(!a)return t.getBoundingClientRect()}};c(e),h.postMessage({resize:{width:e.width,height:e.height}});return}v.width=v.height=null}function T(){s=null,o&&(l=!1,e.removeEventListener("resize",P)),a&&t&&(document.body.contains(t)&&document.body.removeChild(t),t=null,d=!1)}return(h&&!d&&h.init(t),d=!0,h&&(t.__confetti_initialized=!0),o&&!l&&(l=!0,e.addEventListener("resize",P,!1)),h)?h.fire(i,v,T):function(e,i,a){for(var o,l,u,h,d,p,m,f=S(e,"particleCount",M),g=S(e,"angle",Number),v=S(e,"spread",Number),P=S(e,"startVelocity",Number),T=S(e,"decay",Number),A=S(e,"gravity",Number),C=S(e,"drift",Number),k=S(e,"colors",E),V=S(e,"ticks",Number),D=S(e,"shapes"),R=S(e,"scalar"),L=!!S(e,"flat"),j=((o=S(e,"origin",Object)).x=S(o,"x",Number),o.y=S(o,"y",Number),o),F=f,B=[],O=t.width*j.x,I=t.height*j.y;F--;)B.push(function(t){var e=t.angle*(Math.PI/180),i=t.spread*(Math.PI/180);return{x:t.x,y:t.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*t.startVelocity+Math.random()*t.startVelocity,angle2D:-e+(.5*i-Math.random()*i),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:t.color,shape:t.shape,tick:0,totalTicks:t.ticks,decay:t.decay,drift:t.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*t.gravity,ovalScalar:.6,scalar:t.scalar,flat:t.flat}}({x:O,y:I,angle:g,spread:v,startVelocity:P,color:k[F%k.length],shape:D[Math.floor(Math.random()*(D.length-0))+0],ticks:V,decay:T,gravity:A,drift:C,scalar:R,flat:L}));return s?s.addFettis(B):(l=t,d=B.slice(),p=l.getContext("2d"),m=x(function(t){function e(){u=h=null,p.clearRect(0,0,i.width,i.height),w.clear(),a(),t()}u=b.frame(function t(){n&&(i.width!==r.width||i.height!==r.height)&&(i.width=l.width=r.width,i.height=l.height=r.height),i.width||i.height||(c(l),i.width=l.width,i.height=l.height),p.clearRect(0,0,i.width,i.height),(d=d.filter(function(t){return function(t,e){e.x+=Math.cos(e.angle2D)*e.velocity+e.drift,e.y+=Math.sin(e.angle2D)*e.velocity+e.gravity,e.velocity*=e.decay,e.flat?(e.wobble=0,e.wobbleX=e.x+10*e.scalar,e.wobbleY=e.y+10*e.scalar,e.tiltSin=0,e.tiltCos=0,e.random=1):(e.wobble+=e.wobbleSpeed,e.wobbleX=e.x+10*e.scalar*Math.cos(e.wobble),e.wobbleY=e.y+10*e.scalar*Math.sin(e.wobble),e.tiltAngle+=.1,e.tiltSin=Math.sin(e.tiltAngle),e.tiltCos=Math.cos(e.tiltAngle),e.random=Math.random()+2);var i,n,r,s,a,o,l,u,h,c,d,p,m,f,g,v,x=e.tick++/e.totalTicks,b=e.x+e.random*e.tiltCos,P=e.y+e.random*e.tiltSin,T=e.wobbleX+e.random*e.tiltCos,S=e.wobbleY+e.random*e.tiltSin;if(t.fillStyle="rgba("+e.color.r+", "+e.color.g+", "+e.color.b+", "+(1-x)+")",t.beginPath(),y&&"path"===e.shape.type&&"string"==typeof e.shape.path&&Array.isArray(e.shape.matrix)){t.fill((i=e.shape.path,n=e.shape.matrix,r=e.x,s=e.y,a=.1*Math.abs(T-b),o=.1*Math.abs(S-P),l=Math.PI/10*e.wobble,u=new Path2D(i),(h=new Path2D).addPath(u,new DOMMatrix(n)),(c=new Path2D).addPath(h,new DOMMatrix([Math.cos(l)*a,Math.sin(l)*a,-Math.sin(l)*o,Math.cos(l)*o,r,s])),c))}else if("bitmap"===e.shape.type){var M=Math.PI/10*e.wobble,A=.1*Math.abs(T-b),E=.1*Math.abs(S-P),C=e.shape.bitmap.width*e.scalar,k=e.shape.bitmap.height*e.scalar,V=new DOMMatrix([Math.cos(M)*A,Math.sin(M)*A,-Math.sin(M)*E,Math.cos(M)*E,e.x,e.y]);V.multiplySelf(new DOMMatrix(e.shape.matrix));var D=t.createPattern(w.transform(e.shape.bitmap),"no-repeat");D.setTransform(V),t.globalAlpha=1-x,t.fillStyle=D,t.fillRect(e.x-C/2,e.y-k/2,C,k),t.globalAlpha=1}else if("circle"===e.shape)t.ellipse?t.ellipse(e.x,e.y,Math.abs(T-b)*e.ovalScalar,Math.abs(S-P)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI):(d=e.x,p=e.y,m=Math.abs(T-b)*e.ovalScalar,f=Math.abs(S-P)*e.ovalScalar,g=Math.PI/10*e.wobble,v=2*Math.PI,t.save(),t.translate(d,p),t.rotate(g),t.scale(m,f),t.arc(0,0,1,0,v,void 0),t.restore());else if("star"===e.shape)for(var R=Math.PI/2*3,L=4*e.scalar,j=8*e.scalar,F=e.x,B=e.y,O=5,I=Math.PI/5;O--;)F=e.x+Math.cos(R)*j,B=e.y+Math.sin(R)*j,t.lineTo(F,B),R+=I,F=e.x+Math.cos(R)*L,B=e.y+Math.sin(R)*L,t.lineTo(F,B),R+=I;else t.moveTo(Math.floor(e.x),Math.floor(e.y)),t.lineTo(Math.floor(e.wobbleX),Math.floor(P)),t.lineTo(Math.floor(T),Math.floor(S)),t.lineTo(Math.floor(b),Math.floor(e.wobbleY));return t.closePath(),t.fill(),e.tick<e.totalTicks}(p,t)})).length?u=b.frame(t):e()}),h=e}),(s={addFettis:function(t){return d=d.concat(t),m},canvas:l,promise:m,reset:function(){u&&b.cancel(u),h&&h()}}).promise)}(i,v,T)}return m.reset=function(){h&&h.reset(),s&&s.reset()},m}function R(){return f||(f=D(null,{useWorker:!0,resize:!0})),f}i.exports=function(){return R().apply(this,arguments)},i.exports.reset=function(){R().reset()},i.exports.create=D,i.exports.shapeFromPath=function(t){if(!y)throw Error("path confetti are not supported in this browser");"string"==typeof t?n=t:(n=t.path,r=t.matrix);var e=new Path2D(n),i=document.createElement("canvas").getContext("2d");if(!r){for(var n,r,s,a,o=1e3,l=1e3,u=0,h=0,c=0;c<1e3;c+=2)for(var d=0;d<1e3;d+=2)i.isPointInPath(e,c,d,"nonzero")&&(o=Math.min(o,c),l=Math.min(l,d),u=Math.max(u,c),h=Math.max(h,d));s=u-o;var p=Math.min(10/s,10/(a=h-l));r=[p,0,0,p,-Math.round(s/2+o)*p,-Math.round(a/2+l)*p]}return{type:"path",path:n,matrix:r}},i.exports.shapeFromText=function(t){var e,i=1,n="#000000",r='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"==typeof t?e=t:(e=t.text,i="scalar"in t?t.scalar:i,r="fontFamily"in t?t.fontFamily:r,n="color"in t?t.color:n);var s=10*i,a=""+s+"px "+r,o=new OffscreenCanvas(s,s),l=o.getContext("2d");l.font=a;var u=l.measureText(e),h=Math.ceil(u.actualBoundingBoxRight+u.actualBoundingBoxLeft),c=Math.ceil(u.actualBoundingBoxAscent+u.actualBoundingBoxDescent),d=u.actualBoundingBoxLeft+2,p=u.actualBoundingBoxAscent+2;h+=4,c+=4,(l=(o=new OffscreenCanvas(h,c)).getContext("2d")).font=a,l.fillStyle=n,l.fillText(e,d,p);var m=1/i;return{type:"bitmap",bitmap:o.transferToImageBitmap(),matrix:[m,0,0,m,-h*m/2,-c*m/2]}}}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),n,!1);let r=n.exports;n.exports.create},6160:(t,e,i)=>{i.d(e,{default:()=>u});var n=i(6874),r=i(5695),s=i(2115),a=i(6453),o=i(8986),l=i(5155),u=(0,s.forwardRef)(function(t,e){let{href:i,locale:s,localeCookie:u,onClick:h,prefetch:c,...d}=t,p=(0,a.Ym)(),m=null!=s&&s!==p,f=(0,r.usePathname)();return m&&(c=!1),(0,l.jsx)(n,{ref:e,href:i,hrefLang:m?s:void 0,onClick:function(t){(0,o.A)(u,f,p,s),h&&h(t)},prefetch:c,...d})})},7652:(t,e,i)=>{i.d(e,{c3:()=>s});var n=i(6453);function r(t,e){return(...t)=>{try{return e(...t)}catch{throw Error(void 0)}}}let s=r(0,n.c3);r(0,n.kc)},8986:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(469);function r(t,e,i,r){if(!t||r===i||null==r||!e)return;let s=(0,n.DT)(e),{name:a,...o}=t;o.path||(o.path=""!==s?s:"/");let l=`${a}=${r};`;for(let[t,e]of Object.entries(o))l+=`${"maxAge"===t?"max-age":t}`,"boolean"!=typeof e&&(l+="="+e),l+=";";document.cookie=l}},9946:(t,e,i)=>{i.d(e,{A:()=>h});var n=i(2115);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),a=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},o=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:u="",children:h,iconNode:c,...d}=t;return(0,n.createElement)("svg",{ref:e,...l,width:r,height:r,stroke:i,strokeWidth:a?24*Number(s)/Number(r):s,className:o("lucide",u),...d},[...c.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),h=(t,e)=>{let i=(0,n.forwardRef)((i,s)=>{let{className:l,...h}=i;return(0,n.createElement)(u,{ref:s,iconNode:e,className:o("lucide-".concat(r(a(t))),"lucide-".concat(t),l),...h})});return i.displayName=a(t),i}},9984:(t,e,i)=>{i.d(e,{A:()=>n});function n(t){return t}}}]);