"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-mdx";
exports.ids = ["vendor-chunks/fumadocs-mdx"];
exports.modules = {

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* binding */ _runtime),\n/* harmony export */   createMDXSource: () => (/* binding */ createMDXSource),\n/* harmony export */   resolveFiles: () => (/* binding */ resolveFiles)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n// src/runtime/index.ts\n\nvar _runtime = {\n  doc(files) {\n    return files.map((file) => {\n      const { default: body, frontmatter, ...exports } = file.data;\n      let cachedContent;\n      return {\n        body,\n        ...exports,\n        ...frontmatter,\n        get content() {\n          cachedContent ??= fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(file.info.absolutePath).toString();\n          return cachedContent;\n        },\n        _exports: file.data,\n        _file: file.info\n      };\n    });\n  },\n  meta(files) {\n    return files.map((file) => {\n      return {\n        ...file.data,\n        _file: file.info\n      };\n    });\n  },\n  docs(docs, metas) {\n    const parsedDocs = this.doc(docs);\n    const parsedMetas = this.meta(metas);\n    return {\n      docs: parsedDocs,\n      meta: parsedMetas,\n      toFumadocsSource() {\n        return createMDXSource(parsedDocs, parsedMetas);\n      }\n    };\n  }\n};\nfunction createMDXSource(docs, meta = []) {\n  return {\n    files: () => resolveFiles({\n      docs,\n      meta\n    })\n  };\n}\nfunction resolveFiles({ docs, meta }) {\n  const outputs = [];\n  for (const entry of docs) {\n    outputs.push({\n      type: \"page\",\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  for (const entry of meta) {\n    outputs.push({\n      type: \"meta\",\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  return outputs;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* reexport safe */ _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__._runtime),\n/* harmony export */   createMDXSource: () => (/* reexport safe */ _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__.createMDXSource),\n/* harmony export */   resolveFiles: () => (/* reexport safe */ _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__.resolveFiles)\n/* harmony export */ });\n/* harmony import */ var _chunk_7SSA5RCV_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-7SSA5RCV.js */ \"(rsc)/./node_modules/fumadocs-mdx/dist/chunk-7SSA5RCV.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtbWR4L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUk2QjtBQUszQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEZWVwSW5zaWdodFxcRG93bmxvYWRzXFxwb3J0YWwtbWFpblxccG9ydGFsLW1haW5cXG5vZGVfbW9kdWxlc1xcZnVtYWRvY3MtbWR4XFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfcnVudGltZSxcbiAgY3JlYXRlTURYU291cmNlLFxuICByZXNvbHZlRmlsZXNcbn0gZnJvbSBcIi4vY2h1bmstN1NTQTVSQ1YuanNcIjtcbmV4cG9ydCB7XG4gIF9ydW50aW1lLFxuICBjcmVhdGVNRFhTb3VyY2UsXG4gIHJlc29sdmVGaWxlc1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/index.js\n");

/***/ })

};
;