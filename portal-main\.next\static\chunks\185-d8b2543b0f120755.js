"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{6263:(e,t,r)=>{r.d(t,{ClientSearch:()=>m});var a=r(5155),s=r(2115),n=r(6972),o=r(7516),l=r(5040),c=r(7434),i=r(7924),d=r(4416),x=r(6453);function m(e){let{placeholder:t="Search...",className:r=""}=e,m=(0,x.Ym)(),[u,h]=(0,s.useState)(!1),[g,y]=(0,s.useState)(""),[p,f]=(0,s.useState)([]),[b,w]=(0,s.useState)(!1),[j,k]=(0,s.useState)(null),[v,N]=(0,s.useState)(!1);(0,s.useEffect)(()=>{(async()=>{try{let e=await fetch("/search-index.json");if(e.ok){let t=await e.json(),r=t[m]||t.en;if(r){let e={schema:r.schema,components:{}};"cn"===m&&(e.components.tokenizer=(0,o.e)());let t=(0,n.vt)(e);t.data=r.data,t.index=r.index,t.sorting=r.sorting,t.facets=r.facets,k(t),N(!0),console.log("✅ 搜索索引加载完成 (".concat(m,")"))}else console.warn("未找到 ".concat(m," 语言的搜索索引"))}else console.error("无法加载搜索索引文件")}catch(e){console.error("加载搜索索引失败:",e)}})()},[m]);let E=(0,s.useCallback)(async e=>{if(!j||!e.trim())return void f([]);w(!0);try{let t=(await (0,n.$P)(j,{term:e,limit:10,threshold:.6,boost:{title:2,description:1.5,content:1}})).hits.map(e=>({id:e.id,url:e.document.url,title:e.document.title,description:e.document.description,content:e.document.content,type:e.document.type,score:e.score}));f(t)}catch(e){console.error("搜索失败:",e),f([])}finally{w(!1)}},[j]);(0,s.useEffect)(()=>{let e=setTimeout(()=>{g?E(g):f([])},300);return()=>clearTimeout(e)},[g,E]),(0,s.useEffect)(()=>{let e=e=>{(e.metaKey||e.ctrlKey)&&"k"===e.key?(e.preventDefault(),h(!0)):"Escape"===e.key&&(h(!1),y(""))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]);let A=e=>{h(!1),y(""),window.location.href="/".concat(m).concat(e.url)},S=e=>"blog"===e?(0,a.jsx)(l.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"}),C=e=>"blog"===e?"cn"===m?"博客":"Blog":"cn"===m?"文档":"Docs";return v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>h(!0),className:"flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 transition-colors ".concat(r),children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t}),(0,a.jsxs)("kbd",{className:"ml-auto hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 px-1.5 font-mono text-xs text-gray-600 dark:text-gray-300",children:[(0,a.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]}),u&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:()=>h(!1),children:(0,a.jsx)("div",{className:"fixed left-1/2 top-1/2 w-full max-w-2xl -translate-x-1/2 -translate-y-1/2 transform",children:(0,a.jsxs)("div",{className:"mx-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center border-b border-gray-200 dark:border-gray-700 px-4",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",value:g,onChange:e=>y(e.target.value),placeholder:t,className:"flex-1 bg-transparent px-4 py-4 text-sm outline-none placeholder:text-gray-400",autoFocus:!0}),(0,a.jsx)("button",{onClick:()=>h(!1),className:"rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:b?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"搜索中..."})}):p.length>0?(0,a.jsx)("div",{className:"py-2",children:p.map(e=>(0,a.jsxs)("button",{onClick:()=>A(e),className:"flex w-full items-start gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50",children:[(0,a.jsx)("div",{className:"mt-1 flex-shrink-0 text-gray-400",children:S(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100 truncate",children:e.title}),(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded",children:C(e.type)})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:e.description})]})]},e.id))}):g?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"cn"===m?"未找到结果":"No results found"})}):(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"cn"===m?"输入关键词开始搜索":"Type to search"})})})]})})})]}):(0,a.jsxs)("button",{className:"flex items-center gap-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 ".concat(r),disabled:!0,children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Loading search..."})]})}},9607:(e,t,r)=>{r.d(t,{GitHubStarButton:()=>l});var a=r(5155),s=r(2115),n=r(3311),o=r(8564);function l(e){let{repo:t,className:r=""}=e,[l,c]=(0,s.useState)(null),[i,d]=(0,s.useState)(!1),x=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8e3,[r,a]=(0,s.useState)(0),n=(0,s.useRef)(0),o=(0,s.useRef)(null);return(0,s.useEffect)(()=>{if(!e)return;let r=s=>{null===o.current&&(o.current=s);let l=Math.min((s-o.current)/t,1),c=1===l?1:1-Math.pow(2,-10*l),i=n.current,d=Math.floor(i+(e-i)*c);n.current=d,a(d),l<1&&requestAnimationFrame(r)};return o.current=null,requestAnimationFrame(r),()=>{o.current=null}},[e,t]),r}(l);(0,s.useEffect)(()=>{(async()=>{try{let e=await fetch("/github-stars.json");if(e.ok){let r=(await e.json())[t];r&&r.stargazers_count?(c(r.stargazers_count),r.success||console.info("使用 ".concat(t," 的缓存星标数据:"),r.error||"未知错误")):(console.warn("未找到 ".concat(t," 的星标数据，使用默认值")),c(m(t)))}else console.warn("无法加载星标数据文件，使用默认值"),c(m(t))}catch(e){console.error("获取星标数据失败:",e),c(m(t))}})()},[t]);let m=e=>({"TEN-framework/ten-framework":7135,"TEN-framework/portal":100,"TEN-framework/ten-vad":50,"TEN-framework/ten-turn-detection":30})[e]||1e3;return(0,a.jsxs)("div",{className:"relative",children:[!i&&(0,a.jsxs)("div",{className:"absolute -inset-2 pointer-events-none",children:[(0,a.jsx)(n.A,{className:"absolute top-0 left-0 h-3 w-3 text-yellow-400 animate-ping"}),(0,a.jsx)(n.A,{className:"absolute top-0 right-0 h-2 w-2 text-blue-400 animate-ping animation-delay-300"}),(0,a.jsx)(n.A,{className:"absolute bottom-0 left-2 h-2 w-2 text-purple-400 animate-ping animation-delay-500"}),(0,a.jsx)(n.A,{className:"absolute bottom-0 right-2 h-3 w-3 text-green-400 animate-ping animation-delay-700"})]}),(0,a.jsxs)("button",{onClick:()=>{window.open("https://github.com/".concat(t),"_blank","noopener,noreferrer")},onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),className:"group relative inline-flex items-center gap-2 rounded-md bg-gradient-to-r from-gray-900 to-black px-3 py-1.5 text-sm font-medium text-white transition-all duration-300 hover:from-gray-800 hover:to-gray-900 hover:scale-105 cursor-pointer ".concat(r),children:[(0,a.jsx)("svg",{viewBox:"0 0 24 24",className:"h-4 w-4 relative z-10",fill:"currentColor",children:(0,a.jsx)("path",{d:"M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"})}),(0,a.jsx)("span",{className:"dark:text-gray-300 relative z-10",children:"open source"}),(0,a.jsx)(o.A,{className:"h-4 w-4 relative z-10 transition-all duration-300 ".concat(i?"fill-transparent stroke-yellow-500 scale-110":"fill-transparent stroke-white")}),(0,a.jsx)("span",{className:"inline-block w-16 rounded bg-gradient-to-r from-gray-700 to-gray-600 px-2 py-0.5 text-center text-xs font-bold relative z-10 transition-all duration-300 ".concat(i?"from-yellow-600 to-yellow-500 text-white scale-105":""),children:x.toLocaleString()}),i&&(0,a.jsx)("div",{className:"absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-50",children:"⭐ Star this repo!"})]})]})}}}]);